#!/usr/bin/env python3
"""
Test script to verify that default English responses are properly translated
to the appropriate language when no relevant context is found.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from full_code import get_no_context_response, is_default_no_context_response

def test_no_context_responses():
    """Test the language-specific no-context responses"""
    
    print("🧪 Testing No-Context Response Generation")
    print("=" * 50)
    
    test_query = "What are the recent advancements in space exploration?"
    
    # Test different languages
    languages = ["English", "Tamil", "Telugu", "Kannada", "Hindi"]
    
    for language in languages:
        print(f"\n🌐 Testing {language} response:")
        print("-" * 30)
        response = get_no_context_response(test_query, language)
        print(f"Response: {response[:100]}...")
        print(f"Length: {len(response)} characters")

def test_default_response_detection():
    """Test the detection of default English responses"""
    
    print("\n🔍 Testing Default Response Detection")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            "text": "The provided context does not contain any information about recent advancements in space exploration. It primarily discusses market trends, stock recommendations, and the impact of COVID-19 on businesses, particularly in the IT sector.",
            "expected": True,
            "description": "Typical default response"
        },
        {
            "text": "Based on the provided information, here are the key points about space exploration...",
            "expected": False,
            "description": "Normal response with context"
        },
        {
            "text": "The context does not contain information about this topic. It mainly covers financial data.",
            "expected": True,
            "description": "Another no-context pattern"
        },
        {
            "text": "Hello, how can I help you today?",
            "expected": False,
            "description": "Unrelated text"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        result = is_default_no_context_response(test_case["text"])
        status = "✅ PASS" if result == test_case["expected"] else "❌ FAIL"
        
        print(f"\nTest {i}: {test_case['description']}")
        print(f"Text: {test_case['text'][:60]}...")
        print(f"Expected: {test_case['expected']}, Got: {result} - {status}")

def test_translation_patterns():
    """Test translation service patterns"""
    
    print("\n🔄 Testing Translation Service Patterns")
    print("=" * 50)
    
    try:
        from services.translation_service import translation_service
        
        # Test English to Tamil translation of default response
        english_text = "The provided context does not contain any information"
        
        print(f"Original English: {english_text}")
        
        # Test translation
        result = translation_service.translate_text(english_text, "ta", "en")
        
        print(f"Translation result: {result}")
        
        if result and result.get('translated_text'):
            print(f"Translated to Tamil: {result['translated_text']}")
        else:
            print("❌ Translation failed or not available")
            
    except ImportError as e:
        print(f"⚠️ Translation service not available: {e}")

if __name__ == "__main__":
    print("🚀 Starting Translation Fix Tests")
    print("=" * 60)
    
    try:
        test_no_context_responses()
        test_default_response_detection()
        test_translation_patterns()
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("\n📝 Summary:")
        print("- Default responses are now generated in the appropriate language")
        print("- Detection of English default responses works correctly")
        print("- Translation patterns are configured for common phrases")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
