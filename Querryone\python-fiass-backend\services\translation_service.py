"""
Translation Service for FAISS Backend
Provides language detection and translation capabilities for bot responses.
"""

import os
import re
import json
import time
import hashlib
import requests
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

# Import Deep Translator
try:
    from deep_translator import GoogleTranslator
    TRANSLATOR_AVAILABLE = True
    print("✅ Deep Translator library loaded successfully")
except ImportError:
    TRANSLATOR_AVAILABLE = False
    print("⚠️ Deep Translator library not available - using fallback translations")

class TranslationService:
    """
    Service for handling language detection and translation of bot responses.
    Supports multiple translation providers with caching for performance.
    """
    
    def __init__(self):
        self.cache = {}  # In-memory cache for translations
        self.cache_expiry = 3600  # 1 hour cache expiry
        self.supported_languages = {
            'en': 'English',
            'ta': 'Tamil',
            'te': 'Telugu',
            'kn': 'Kannada',
            'hi': 'Hindi',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }
        
        # Deep Translator doesn't need initialization - it's used directly
        self.translator_available = TRANSLATOR_AVAILABLE
        if self.translator_available:
            print("✅ Deep Translator ready for use")
        else:
            print("⚠️ Deep Translator not available")
        
    def detect_language(self, text: str) -> str:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Language code (e.g., 'en', 'ta', 'hi')
        """
        if not text or not text.strip():
            return 'en'  # Default to English
            
        # Tamil detection using Unicode ranges
        if self._is_tamil_text(text):
            return 'ta'
            
        # Telugu detection using Unicode ranges
        if self._is_telugu_text(text):
            return 'te'
            
        # Kannada detection using Unicode ranges
        if self._is_kannada_text(text):
            return 'kn'
            
        # Hindi detection using Unicode ranges
        if self._is_hindi_text(text):
            return 'hi'
            
        # Arabic detection
        if self._is_arabic_text(text):
            return 'ar'
            
        # Chinese detection
        if self._is_chinese_text(text):
            return 'zh'
            
        # Default to English for other cases
        return 'en'
    
    def _is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def _is_telugu_text(self, text: str) -> bool:
        """Check if text contains Telugu characters"""
        telugu_pattern = re.compile(r'[\u0C00-\u0C7F]')
        return bool(telugu_pattern.search(text))
    
    def _is_kannada_text(self, text: str) -> bool:
        """Check if text contains Kannada characters"""
        kannada_pattern = re.compile(r'[\u0C80-\u0CFF]')
        return bool(kannada_pattern.search(text))
    
    def _is_hindi_text(self, text: str) -> bool:
        """Check if text contains Hindi/Devanagari characters"""
        hindi_pattern = re.compile(r'[\u0900-\u097F]')
        return bool(hindi_pattern.search(text))
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_pattern = re.compile(r'[\u0600-\u06FF]')
        return bool(arabic_pattern.search(text))
    
    def _is_chinese_text(self, text: str) -> bool:
        """Check if text contains Chinese characters"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """Generate a cache key for translation"""
        content = f"{text}|{source_lang}|{target_lang}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if cache entry is still valid"""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        expiry_time = cache_time + timedelta(seconds=self.cache_expiry)
        return datetime.now() < expiry_time
    
    def translate_text(self, text: str, target_lang: str, source_lang: str = None) -> Dict[str, str]:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            
        Returns:
            Dictionary with translation result and metadata
        """
        if not text or not text.strip():
            return {
                'translated_text': text,
                'source_language': 'unknown',
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Auto-detect source language if not provided
        if not source_lang:
            source_lang = self.detect_language(text)
        
        # If source and target are the same, return original text
        if source_lang == target_lang:
            return {
                'translated_text': text,
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Check cache first
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
            cached_result = self.cache[cache_key]
            return {
                'translated_text': cached_result['translated_text'],
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': cached_result.get('provider', 'cached'),
                'cached': True
            }
        
        # Attempt translation using available providers
        translation_result = self._attempt_translation(text, source_lang, target_lang)
        
        # Cache the result
        if translation_result['success']:
            self.cache[cache_key] = {
                'translated_text': translation_result['translated_text'],
                'provider': translation_result['provider'],
                'timestamp': datetime.now().isoformat()
            }
        
        return {
            'translated_text': translation_result['translated_text'],
            'source_language': source_lang,
            'target_language': target_lang,
            'translation_provider': translation_result['provider'],
            'cached': False
        }
    
    def _attempt_translation(self, text: str, source_lang: str, target_lang: str) -> Dict:
        """
        Attempt translation using available providers.
        
        Returns:
            Dictionary with translation result
        """
        # Try Deep Translator first if available
        if self.translator_available and TRANSLATOR_AVAILABLE:
            try:
                print(f"🌐 Attempting Deep Translator: {source_lang} -> {target_lang}")
                
                # Use Deep Translator (GoogleTranslator)
                translator = GoogleTranslator(source=source_lang, target=target_lang)
                result = translator.translate(text)
                
                if result:
                    print(f"✅ Deep Translator successful: {result[:50]}...")
                    return {
                        'success': True,
                        'translated_text': result,
                        'provider': 'deep_translator_google'
                    }
                else:
                    print("⚠️ Deep Translator returned empty result")
                    
            except Exception as e:
                print(f"❌ Deep Translator error: {e}")
        
        # Fallback to pattern-based translations for common cases
        fallback_translations = self._get_fallback_translations(text, source_lang, target_lang)
        
        if fallback_translations:
            print(f"🔄 Using fallback translation")
            return {
                'success': True,
                'translated_text': fallback_translations,
                'provider': 'fallback'
            }
        
        # If no translation available, return original text
        print(f"⚠️ No translation available, returning original text")
        return {
            'success': False,
            'translated_text': text,
            'provider': 'none'
        }
    
    def _get_fallback_translations(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Provide fallback translations for common phrases and responses.
        This is a simplified approach - in production you'd use proper translation APIs.
        """
        
        # Common response patterns and their translations
        translation_patterns = {
            ('en', 'ta'): {
                'The provided context does not contain any information': 'கொடுக்கப்பட்ட சூழலில் எந்த தகவலும் இல்லை',
                'Based on the provided information': 'வழங்கப்பட்ட தகவலின் அடிப்படையில்',
                'According to the document': 'ஆவணத்தின் படி',
                'The key points are': 'முக்கிய புள்ளிகள்',
                'In summary': 'சுருக்கமாக',
                'Therefore': 'எனவே',
                'However': 'இருப்பினும்',
                'Additionally': 'கூடுதலாக',
                'Furthermore': 'மேலும்',
                'If you\'d like, I can provide general information': 'நீங்கள் விரும்பினால், பொதுவான தகவல்களை வழங்க முடியும்',
                'Let me know!': 'தெரிவிக்கவும்!'
            },
            ('ta', 'en'): {
                'கொடுக்கப்பட்ட சூழலில் எந்த தகவலும் இல்லை': 'The provided context does not contain any information',
                'வழங்கப்பட்ட தகவலின் அடிப்படையில்': 'Based on the provided information',
                'ஆவணத்தின் படி': 'According to the document',
                'முக்கிய புள்ளிகள்': 'The key points are',
                'சுருக்கமாக': 'In summary',
                'எனவே': 'Therefore',
                'இருப்பினும்': 'However',
                'கூடுதலாக': 'Additionally',
                'மேலும்': 'Furthermore',
                'நீங்கள் விரும்பினால், பொதுவான தகவல்களை வழங்க முடியும்': 'If you\'d like, I can provide general information',
                'தெரிவிக்கவும்!': 'Let me know!'
            },
            ('en', 'te'): {
                'The provided context does not contain any information': 'అందించిన సందర్భంలో ఎటువంటి సమాచారం లేదు',
                'Based on the provided information': 'అందించిన సమాచారం ఆధారంగా',
                'According to the document': 'పత్రం ప్రకారం',
                'The key points are': 'ముఖ్య అంశాలు',
                'In summary': 'సంక్షేపంలో',
                'Therefore': 'కాబట్టి',
                'However': 'అయితే',
                'Additionally': 'అదనంగా',
                'Furthermore': 'ఇంకా',
                'If you\'d like, I can provide general information': 'మీరు కోరుకుంటే, సాధారణ సమాచారాన్ని అందించగలను',
                'Let me know!': 'తెలియజేయండి!'
            },
            ('te', 'en'): {
                'అందించిన సందర్భంలో ఎటువంటి సమాచారం లేదు': 'The provided context does not contain any information',
                'అందించిన సమాచారం ఆధారంగా': 'Based on the provided information',
                'పత్రం ప్రకారం': 'According to the document',
                'ముఖ్య అంశాలు': 'The key points are',
                'సంక్షేపంలో': 'In summary',
                'కాబట్టి': 'Therefore',
                'అయితే': 'However',
                'అదనంగా': 'Additionally',
                'ఇంకా': 'Furthermore',
                'మీరు కోరుకుంటే, సాధారణ సమాచారాన్ని అందించగలను': 'If you\'d like, I can provide general information',
                'తెలియజేయండి!': 'Let me know!'
            },
            ('en', 'kn'): {
                'The provided context does not contain any information': 'ಒದಗಿಸಿದ ಸಂದರ್ಭದಲ್ಲಿ ಯಾವುದೇ ಮಾಹಿತಿ ಇಲ್ಲ',
                'Based on the provided information': 'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ',
                'According to the document': 'ದಾಖಲೆಯ ಪ್ರಕಾರ',
                'The key points are': 'ಮುಖ್ಯ ಅಂಶಗಳು',
                'In summary': 'ಸಂಕ್ಷೇಪವಾಗಿ',
                'Therefore': 'ಆದ್ದರಿಂದ',
                'However': 'ಆದರೆ',
                'Additionally': 'ಹೆಚ್ಚುವರಿಯಾಗಿ',
                'Furthermore': 'ಇದಲ್ಲದೆ',
                'If you\'d like, I can provide general information': 'ನೀವು ಬಯಸಿದರೆ, ಸಾಮಾನ್ಯ ಮಾಹಿತಿಯನ್ನು ಒದಗಿಸಬಹುದು',
                'Let me know!': 'ತಿಳಿಸಿ!'
            },
            ('kn', 'en'): {
                'ಒದಗಿಸಿದ ಸಂದರ್ಭದಲ್ಲಿ ಯಾವುದೇ ಮಾಹಿತಿ ಇಲ್ಲ': 'The provided context does not contain any information',
                'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ': 'Based on the provided information',
                'ದಾಖಲೆಯ ಪ್ರಕಾರ': 'According to the document',
                'ಮುಖ್ಯ ಅಂಶಗಳು': 'The key points are',
                'ಸಂಕ್ಷೇಪವಾಗಿ': 'In summary',
                'ಆದ್ದರಿಂದ': 'Therefore',
                'ಆದರೆ': 'However',
                'ಹೆಚ್ಚುವರಿಯಾಗಿ': 'Additionally',
                'ಇದಲ್ಲದೆ': 'Furthermore',
                'ನೀವು ಬಯಸಿದರೆ, ಸಾಮಾನ್ಯ ಮಾಹಿತಿಯನ್ನು ಒದಗಿಸಬಹುದು': 'If you\'d like, I can provide general information',
                'ತಿಳಿಸಿ!': 'Let me know!'
            }
        }
        
        pattern_key = (source_lang, target_lang)
        if pattern_key in translation_patterns:
            patterns = translation_patterns[pattern_key]
            
            # Simple pattern matching and replacement
            translated_text = text
            for source_phrase, target_phrase in patterns.items():
                translated_text = translated_text.replace(source_phrase, target_phrase)
            
            # If any replacements were made, return the translated text
            if translated_text != text:
                return translated_text
        
        return None
    
    def translate_response_data(self, response_data: Dict, target_lang: str) -> Dict:
        """
        Translate all text fields in a response data dictionary.
        
        Args:
            response_data: Response data containing text fields to translate
            target_lang: Target language code
            
        Returns:
            Translated response data
        """
        translated_data = response_data.copy()
        
        # Translate main AI response
        if 'ai_response' in translated_data and translated_data['ai_response']:
            translation_result = self.translate_text(
                translated_data['ai_response'], 
                target_lang
            )
            translated_data['ai_response'] = translation_result['translated_text']
            translated_data['translation_metadata'] = {
                'ai_response': translation_result
            }
        
        # Translate related questions
        if 'related_questions' in translated_data and translated_data['related_questions']:
            translated_questions = []
            question_translations = []
            
            for question in translated_data['related_questions']:
                translation_result = self.translate_text(question, target_lang)
                translated_questions.append(translation_result['translated_text'])
                question_translations.append(translation_result)
            
            translated_data['related_questions'] = translated_questions
            if 'translation_metadata' not in translated_data:
                translated_data['translation_metadata'] = {}
            translated_data['translation_metadata']['related_questions'] = question_translations
        
        # Add language metadata
        translated_data['response_language'] = target_lang
        translated_data['translation_timestamp'] = datetime.now().isoformat()
        
        return translated_data
    
    def get_cache_stats(self) -> Dict:
        """Get translation cache statistics"""
        valid_entries = 0
        expired_entries = 0
        
        for cache_entry in self.cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            'total_entries': len(self.cache),
            'valid_entries': valid_entries,
            'expired_entries': expired_entries,
            'cache_expiry_seconds': self.cache_expiry,
            'supported_languages': self.supported_languages
        }
    
    def clear_cache(self):
        """Clear the translation cache"""
        self.cache.clear()

# Global translation service instance
translation_service = TranslationService()
