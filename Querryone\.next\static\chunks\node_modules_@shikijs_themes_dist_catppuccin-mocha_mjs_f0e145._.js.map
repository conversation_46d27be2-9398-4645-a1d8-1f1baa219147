{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/themes/dist/catppuccin-mocha.mjs"], "sourcesContent": ["/* Theme: catppuccin-mocha */\nexport default Object.freeze(JSON.parse(\"{\\\"colors\\\":{\\\"activityBar.activeBackground\\\":\\\"#00000000\\\",\\\"activityBar.activeBorder\\\":\\\"#00000000\\\",\\\"activityBar.activeFocusBorder\\\":\\\"#00000000\\\",\\\"activityBar.background\\\":\\\"#11111b\\\",\\\"activityBar.border\\\":\\\"#00000000\\\",\\\"activityBar.dropBorder\\\":\\\"#cba6f733\\\",\\\"activityBar.foreground\\\":\\\"#cba6f7\\\",\\\"activityBar.inactiveForeground\\\":\\\"#6c7086\\\",\\\"activityBarBadge.background\\\":\\\"#cba6f7\\\",\\\"activityBarBadge.foreground\\\":\\\"#11111b\\\",\\\"activityBarTop.activeBorder\\\":\\\"#00000000\\\",\\\"activityBarTop.dropBorder\\\":\\\"#cba6f733\\\",\\\"activityBarTop.foreground\\\":\\\"#cba6f7\\\",\\\"activityBarTop.inactiveForeground\\\":\\\"#6c7086\\\",\\\"badge.background\\\":\\\"#45475a\\\",\\\"badge.foreground\\\":\\\"#cdd6f4\\\",\\\"banner.background\\\":\\\"#45475a\\\",\\\"banner.foreground\\\":\\\"#cdd6f4\\\",\\\"banner.iconForeground\\\":\\\"#cdd6f4\\\",\\\"breadcrumb.activeSelectionForeground\\\":\\\"#cba6f7\\\",\\\"breadcrumb.background\\\":\\\"#1e1e2e\\\",\\\"breadcrumb.focusForeground\\\":\\\"#cba6f7\\\",\\\"breadcrumb.foreground\\\":\\\"#cdd6f4cc\\\",\\\"breadcrumbPicker.background\\\":\\\"#181825\\\",\\\"button.background\\\":\\\"#cba6f7\\\",\\\"button.border\\\":\\\"#00000000\\\",\\\"button.foreground\\\":\\\"#11111b\\\",\\\"button.hoverBackground\\\":\\\"#dec7fa\\\",\\\"button.secondaryBackground\\\":\\\"#585b70\\\",\\\"button.secondaryBorder\\\":\\\"#cba6f7\\\",\\\"button.secondaryForeground\\\":\\\"#cdd6f4\\\",\\\"button.secondaryHoverBackground\\\":\\\"#686b84\\\",\\\"button.separator\\\":\\\"#00000000\\\",\\\"charts.blue\\\":\\\"#89b4fa\\\",\\\"charts.foreground\\\":\\\"#cdd6f4\\\",\\\"charts.green\\\":\\\"#a6e3a1\\\",\\\"charts.lines\\\":\\\"#bac2de\\\",\\\"charts.orange\\\":\\\"#fab387\\\",\\\"charts.purple\\\":\\\"#cba6f7\\\",\\\"charts.red\\\":\\\"#f38ba8\\\",\\\"charts.yellow\\\":\\\"#f9e2af\\\",\\\"checkbox.background\\\":\\\"#45475a\\\",\\\"checkbox.border\\\":\\\"#00000000\\\",\\\"checkbox.foreground\\\":\\\"#cba6f7\\\",\\\"commandCenter.activeBackground\\\":\\\"#585b7033\\\",\\\"commandCenter.activeBorder\\\":\\\"#cba6f7\\\",\\\"commandCenter.activeForeground\\\":\\\"#cba6f7\\\",\\\"commandCenter.background\\\":\\\"#181825\\\",\\\"commandCenter.border\\\":\\\"#00000000\\\",\\\"commandCenter.foreground\\\":\\\"#bac2de\\\",\\\"commandCenter.inactiveBorder\\\":\\\"#00000000\\\",\\\"commandCenter.inactiveForeground\\\":\\\"#bac2de\\\",\\\"debugConsole.errorForeground\\\":\\\"#f38ba8\\\",\\\"debugConsole.infoForeground\\\":\\\"#89b4fa\\\",\\\"debugConsole.sourceForeground\\\":\\\"#f5e0dc\\\",\\\"debugConsole.warningForeground\\\":\\\"#fab387\\\",\\\"debugConsoleInputIcon.foreground\\\":\\\"#cdd6f4\\\",\\\"debugExceptionWidget.background\\\":\\\"#11111b\\\",\\\"debugExceptionWidget.border\\\":\\\"#cba6f7\\\",\\\"debugIcon.breakpointCurrentStackframeForeground\\\":\\\"#585b70\\\",\\\"debugIcon.breakpointDisabledForeground\\\":\\\"#f38ba899\\\",\\\"debugIcon.breakpointForeground\\\":\\\"#f38ba8\\\",\\\"debugIcon.breakpointStackframeForeground\\\":\\\"#585b70\\\",\\\"debugIcon.breakpointUnverifiedForeground\\\":\\\"#a6738c\\\",\\\"debugIcon.continueForeground\\\":\\\"#a6e3a1\\\",\\\"debugIcon.disconnectForeground\\\":\\\"#585b70\\\",\\\"debugIcon.pauseForeground\\\":\\\"#89b4fa\\\",\\\"debugIcon.restartForeground\\\":\\\"#94e2d5\\\",\\\"debugIcon.startForeground\\\":\\\"#a6e3a1\\\",\\\"debugIcon.stepBackForeground\\\":\\\"#585b70\\\",\\\"debugIcon.stepIntoForeground\\\":\\\"#cdd6f4\\\",\\\"debugIcon.stepOutForeground\\\":\\\"#cdd6f4\\\",\\\"debugIcon.stepOverForeground\\\":\\\"#cba6f7\\\",\\\"debugIcon.stopForeground\\\":\\\"#f38ba8\\\",\\\"debugTokenExpression.boolean\\\":\\\"#cba6f7\\\",\\\"debugTokenExpression.error\\\":\\\"#f38ba8\\\",\\\"debugTokenExpression.number\\\":\\\"#fab387\\\",\\\"debugTokenExpression.string\\\":\\\"#a6e3a1\\\",\\\"debugToolBar.background\\\":\\\"#11111b\\\",\\\"debugToolBar.border\\\":\\\"#00000000\\\",\\\"descriptionForeground\\\":\\\"#cdd6f4\\\",\\\"diffEditor.border\\\":\\\"#585b70\\\",\\\"diffEditor.diagonalFill\\\":\\\"#585b7099\\\",\\\"diffEditor.insertedLineBackground\\\":\\\"#a6e3a126\\\",\\\"diffEditor.insertedTextBackground\\\":\\\"#a6e3a11a\\\",\\\"diffEditor.removedLineBackground\\\":\\\"#f38ba826\\\",\\\"diffEditor.removedTextBackground\\\":\\\"#f38ba81a\\\",\\\"diffEditorOverview.insertedForeground\\\":\\\"#a6e3a1cc\\\",\\\"diffEditorOverview.removedForeground\\\":\\\"#f38ba8cc\\\",\\\"disabledForeground\\\":\\\"#a6adc8\\\",\\\"dropdown.background\\\":\\\"#181825\\\",\\\"dropdown.border\\\":\\\"#cba6f7\\\",\\\"dropdown.foreground\\\":\\\"#cdd6f4\\\",\\\"dropdown.listBackground\\\":\\\"#585b70\\\",\\\"editor.background\\\":\\\"#1e1e2e\\\",\\\"editor.findMatchBackground\\\":\\\"#5e3f53\\\",\\\"editor.findMatchBorder\\\":\\\"#f38ba833\\\",\\\"editor.findMatchHighlightBackground\\\":\\\"#3e5767\\\",\\\"editor.findMatchHighlightBorder\\\":\\\"#89dceb33\\\",\\\"editor.findRangeHighlightBackground\\\":\\\"#3e5767\\\",\\\"editor.findRangeHighlightBorder\\\":\\\"#89dceb33\\\",\\\"editor.focusedStackFrameHighlightBackground\\\":\\\"#a6e3a126\\\",\\\"editor.foldBackground\\\":\\\"#89dceb40\\\",\\\"editor.foreground\\\":\\\"#cdd6f4\\\",\\\"editor.hoverHighlightBackground\\\":\\\"#89dceb40\\\",\\\"editor.lineHighlightBackground\\\":\\\"#cdd6f412\\\",\\\"editor.lineHighlightBorder\\\":\\\"#00000000\\\",\\\"editor.rangeHighlightBackground\\\":\\\"#89dceb40\\\",\\\"editor.rangeHighlightBorder\\\":\\\"#00000000\\\",\\\"editor.selectionBackground\\\":\\\"#9399b240\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#9399b233\\\",\\\"editor.selectionHighlightBorder\\\":\\\"#9399b233\\\",\\\"editor.stackFrameHighlightBackground\\\":\\\"#f9e2af26\\\",\\\"editor.wordHighlightBackground\\\":\\\"#9399b233\\\",\\\"editor.wordHighlightStrongBackground\\\":\\\"#89b4fa33\\\",\\\"editorBracketHighlight.foreground1\\\":\\\"#f38ba8\\\",\\\"editorBracketHighlight.foreground2\\\":\\\"#fab387\\\",\\\"editorBracketHighlight.foreground3\\\":\\\"#f9e2af\\\",\\\"editorBracketHighlight.foreground4\\\":\\\"#a6e3a1\\\",\\\"editorBracketHighlight.foreground5\\\":\\\"#74c7ec\\\",\\\"editorBracketHighlight.foreground6\\\":\\\"#cba6f7\\\",\\\"editorBracketHighlight.unexpectedBracket.foreground\\\":\\\"#eba0ac\\\",\\\"editorBracketMatch.background\\\":\\\"#9399b21a\\\",\\\"editorBracketMatch.border\\\":\\\"#9399b2\\\",\\\"editorCodeLens.foreground\\\":\\\"#7f849c\\\",\\\"editorCursor.background\\\":\\\"#1e1e2e\\\",\\\"editorCursor.foreground\\\":\\\"#f5e0dc\\\",\\\"editorError.background\\\":\\\"#00000000\\\",\\\"editorError.border\\\":\\\"#00000000\\\",\\\"editorError.foreground\\\":\\\"#f38ba8\\\",\\\"editorGroup.border\\\":\\\"#585b70\\\",\\\"editorGroup.dropBackground\\\":\\\"#cba6f733\\\",\\\"editorGroup.emptyBackground\\\":\\\"#1e1e2e\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#11111b\\\",\\\"editorGutter.addedBackground\\\":\\\"#a6e3a1\\\",\\\"editorGutter.background\\\":\\\"#1e1e2e\\\",\\\"editorGutter.commentGlyphForeground\\\":\\\"#cba6f7\\\",\\\"editorGutter.commentRangeForeground\\\":\\\"#313244\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f38ba8\\\",\\\"editorGutter.foldingControlForeground\\\":\\\"#9399b2\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#f9e2af\\\",\\\"editorHoverWidget.background\\\":\\\"#181825\\\",\\\"editorHoverWidget.border\\\":\\\"#585b70\\\",\\\"editorHoverWidget.foreground\\\":\\\"#cdd6f4\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#585b70\\\",\\\"editorIndentGuide.background\\\":\\\"#45475a\\\",\\\"editorInfo.background\\\":\\\"#00000000\\\",\\\"editorInfo.border\\\":\\\"#00000000\\\",\\\"editorInfo.foreground\\\":\\\"#89b4fa\\\",\\\"editorInlayHint.background\\\":\\\"#181825bf\\\",\\\"editorInlayHint.foreground\\\":\\\"#585b70\\\",\\\"editorInlayHint.parameterBackground\\\":\\\"#181825bf\\\",\\\"editorInlayHint.parameterForeground\\\":\\\"#a6adc8\\\",\\\"editorInlayHint.typeBackground\\\":\\\"#181825bf\\\",\\\"editorInlayHint.typeForeground\\\":\\\"#bac2de\\\",\\\"editorLightBulb.foreground\\\":\\\"#f9e2af\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#cba6f7\\\",\\\"editorLineNumber.foreground\\\":\\\"#7f849c\\\",\\\"editorLink.activeForeground\\\":\\\"#cba6f7\\\",\\\"editorMarkerNavigation.background\\\":\\\"#181825\\\",\\\"editorMarkerNavigationError.background\\\":\\\"#f38ba8\\\",\\\"editorMarkerNavigationInfo.background\\\":\\\"#89b4fa\\\",\\\"editorMarkerNavigationWarning.background\\\":\\\"#fab387\\\",\\\"editorOverviewRuler.background\\\":\\\"#181825\\\",\\\"editorOverviewRuler.border\\\":\\\"#cdd6f412\\\",\\\"editorOverviewRuler.modifiedForeground\\\":\\\"#f9e2af\\\",\\\"editorRuler.foreground\\\":\\\"#585b70\\\",\\\"editorStickyScrollHover.background\\\":\\\"#313244\\\",\\\"editorSuggestWidget.background\\\":\\\"#181825\\\",\\\"editorSuggestWidget.border\\\":\\\"#585b70\\\",\\\"editorSuggestWidget.foreground\\\":\\\"#cdd6f4\\\",\\\"editorSuggestWidget.highlightForeground\\\":\\\"#cba6f7\\\",\\\"editorSuggestWidget.selectedBackground\\\":\\\"#313244\\\",\\\"editorWarning.background\\\":\\\"#00000000\\\",\\\"editorWarning.border\\\":\\\"#00000000\\\",\\\"editorWarning.foreground\\\":\\\"#fab387\\\",\\\"editorWhitespace.foreground\\\":\\\"#9399b266\\\",\\\"editorWidget.background\\\":\\\"#181825\\\",\\\"editorWidget.foreground\\\":\\\"#cdd6f4\\\",\\\"editorWidget.resizeBorder\\\":\\\"#585b70\\\",\\\"errorForeground\\\":\\\"#f38ba8\\\",\\\"errorLens.errorBackground\\\":\\\"#f38ba826\\\",\\\"errorLens.errorBackgroundLight\\\":\\\"#f38ba826\\\",\\\"errorLens.errorForeground\\\":\\\"#f38ba8\\\",\\\"errorLens.errorForegroundLight\\\":\\\"#f38ba8\\\",\\\"errorLens.errorMessageBackground\\\":\\\"#f38ba826\\\",\\\"errorLens.hintBackground\\\":\\\"#a6e3a126\\\",\\\"errorLens.hintBackgroundLight\\\":\\\"#a6e3a126\\\",\\\"errorLens.hintForeground\\\":\\\"#a6e3a1\\\",\\\"errorLens.hintForegroundLight\\\":\\\"#a6e3a1\\\",\\\"errorLens.hintMessageBackground\\\":\\\"#a6e3a126\\\",\\\"errorLens.infoBackground\\\":\\\"#89b4fa26\\\",\\\"errorLens.infoBackgroundLight\\\":\\\"#89b4fa26\\\",\\\"errorLens.infoForeground\\\":\\\"#89b4fa\\\",\\\"errorLens.infoForegroundLight\\\":\\\"#89b4fa\\\",\\\"errorLens.infoMessageBackground\\\":\\\"#89b4fa26\\\",\\\"errorLens.statusBarErrorForeground\\\":\\\"#f38ba8\\\",\\\"errorLens.statusBarHintForeground\\\":\\\"#a6e3a1\\\",\\\"errorLens.statusBarIconErrorForeground\\\":\\\"#f38ba8\\\",\\\"errorLens.statusBarIconWarningForeground\\\":\\\"#fab387\\\",\\\"errorLens.statusBarInfoForeground\\\":\\\"#89b4fa\\\",\\\"errorLens.statusBarWarningForeground\\\":\\\"#fab387\\\",\\\"errorLens.warningBackground\\\":\\\"#fab38726\\\",\\\"errorLens.warningBackgroundLight\\\":\\\"#fab38726\\\",\\\"errorLens.warningForeground\\\":\\\"#fab387\\\",\\\"errorLens.warningForegroundLight\\\":\\\"#fab387\\\",\\\"errorLens.warningMessageBackground\\\":\\\"#fab38726\\\",\\\"extensionBadge.remoteBackground\\\":\\\"#89b4fa\\\",\\\"extensionBadge.remoteForeground\\\":\\\"#11111b\\\",\\\"extensionButton.prominentBackground\\\":\\\"#cba6f7\\\",\\\"extensionButton.prominentForeground\\\":\\\"#11111b\\\",\\\"extensionButton.prominentHoverBackground\\\":\\\"#dec7fa\\\",\\\"extensionButton.separator\\\":\\\"#1e1e2e\\\",\\\"extensionIcon.preReleaseForeground\\\":\\\"#585b70\\\",\\\"extensionIcon.sponsorForeground\\\":\\\"#f5c2e7\\\",\\\"extensionIcon.starForeground\\\":\\\"#f9e2af\\\",\\\"extensionIcon.verifiedForeground\\\":\\\"#a6e3a1\\\",\\\"focusBorder\\\":\\\"#cba6f7\\\",\\\"foreground\\\":\\\"#cdd6f4\\\",\\\"gitDecoration.addedResourceForeground\\\":\\\"#a6e3a1\\\",\\\"gitDecoration.conflictingResourceForeground\\\":\\\"#cba6f7\\\",\\\"gitDecoration.deletedResourceForeground\\\":\\\"#f38ba8\\\",\\\"gitDecoration.ignoredResourceForeground\\\":\\\"#6c7086\\\",\\\"gitDecoration.modifiedResourceForeground\\\":\\\"#f9e2af\\\",\\\"gitDecoration.stageDeletedResourceForeground\\\":\\\"#f38ba8\\\",\\\"gitDecoration.stageModifiedResourceForeground\\\":\\\"#f9e2af\\\",\\\"gitDecoration.submoduleResourceForeground\\\":\\\"#89b4fa\\\",\\\"gitDecoration.untrackedResourceForeground\\\":\\\"#a6e3a1\\\",\\\"gitlens.closedAutolinkedIssueIconColor\\\":\\\"#cba6f7\\\",\\\"gitlens.closedPullRequestIconColor\\\":\\\"#f38ba8\\\",\\\"gitlens.decorations.branchAheadForegroundColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.decorations.branchBehindForegroundColor\\\":\\\"#fab387\\\",\\\"gitlens.decorations.branchDivergedForegroundColor\\\":\\\"#f9e2af\\\",\\\"gitlens.decorations.branchMissingUpstreamForegroundColor\\\":\\\"#fab387\\\",\\\"gitlens.decorations.branchUnpublishedForegroundColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.decorations.statusMergingOrRebasingConflictForegroundColor\\\":\\\"#eba0ac\\\",\\\"gitlens.decorations.statusMergingOrRebasingForegroundColor\\\":\\\"#f9e2af\\\",\\\"gitlens.decorations.workspaceCurrentForegroundColor\\\":\\\"#cba6f7\\\",\\\"gitlens.decorations.workspaceRepoMissingForegroundColor\\\":\\\"#a6adc8\\\",\\\"gitlens.decorations.workspaceRepoOpenForegroundColor\\\":\\\"#cba6f7\\\",\\\"gitlens.decorations.worktreeHasUncommittedChangesForegroundColor\\\":\\\"#fab387\\\",\\\"gitlens.decorations.worktreeMissingForegroundColor\\\":\\\"#eba0ac\\\",\\\"gitlens.graphChangesColumnAddedColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.graphChangesColumnDeletedColor\\\":\\\"#f38ba8\\\",\\\"gitlens.graphLane10Color\\\":\\\"#f5c2e7\\\",\\\"gitlens.graphLane1Color\\\":\\\"#cba6f7\\\",\\\"gitlens.graphLane2Color\\\":\\\"#f9e2af\\\",\\\"gitlens.graphLane3Color\\\":\\\"#89b4fa\\\",\\\"gitlens.graphLane4Color\\\":\\\"#f2cdcd\\\",\\\"gitlens.graphLane5Color\\\":\\\"#a6e3a1\\\",\\\"gitlens.graphLane6Color\\\":\\\"#b4befe\\\",\\\"gitlens.graphLane7Color\\\":\\\"#f5e0dc\\\",\\\"gitlens.graphLane8Color\\\":\\\"#f38ba8\\\",\\\"gitlens.graphLane9Color\\\":\\\"#94e2d5\\\",\\\"gitlens.graphMinimapMarkerHeadColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.graphMinimapMarkerHighlightsColor\\\":\\\"#f9e2af\\\",\\\"gitlens.graphMinimapMarkerLocalBranchesColor\\\":\\\"#89b4fa\\\",\\\"gitlens.graphMinimapMarkerRemoteBranchesColor\\\":\\\"#71a4f9\\\",\\\"gitlens.graphMinimapMarkerStashesColor\\\":\\\"#cba6f7\\\",\\\"gitlens.graphMinimapMarkerTagsColor\\\":\\\"#f2cdcd\\\",\\\"gitlens.graphMinimapMarkerUpstreamColor\\\":\\\"#93dd8d\\\",\\\"gitlens.graphScrollMarkerHeadColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.graphScrollMarkerHighlightsColor\\\":\\\"#f9e2af\\\",\\\"gitlens.graphScrollMarkerLocalBranchesColor\\\":\\\"#89b4fa\\\",\\\"gitlens.graphScrollMarkerRemoteBranchesColor\\\":\\\"#71a4f9\\\",\\\"gitlens.graphScrollMarkerStashesColor\\\":\\\"#cba6f7\\\",\\\"gitlens.graphScrollMarkerTagsColor\\\":\\\"#f2cdcd\\\",\\\"gitlens.graphScrollMarkerUpstreamColor\\\":\\\"#93dd8d\\\",\\\"gitlens.gutterBackgroundColor\\\":\\\"#3132444d\\\",\\\"gitlens.gutterForegroundColor\\\":\\\"#cdd6f4\\\",\\\"gitlens.gutterUncommittedForegroundColor\\\":\\\"#cba6f7\\\",\\\"gitlens.lineHighlightBackgroundColor\\\":\\\"#cba6f726\\\",\\\"gitlens.lineHighlightOverviewRulerColor\\\":\\\"#cba6f7cc\\\",\\\"gitlens.mergedPullRequestIconColor\\\":\\\"#cba6f7\\\",\\\"gitlens.openAutolinkedIssueIconColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.openPullRequestIconColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.trailingLineBackgroundColor\\\":\\\"#00000000\\\",\\\"gitlens.trailingLineForegroundColor\\\":\\\"#cdd6f44d\\\",\\\"gitlens.unpublishedChangesIconColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.unpublishedCommitIconColor\\\":\\\"#a6e3a1\\\",\\\"gitlens.unpulledChangesIconColor\\\":\\\"#fab387\\\",\\\"icon.foreground\\\":\\\"#cba6f7\\\",\\\"input.background\\\":\\\"#313244\\\",\\\"input.border\\\":\\\"#00000000\\\",\\\"input.foreground\\\":\\\"#cdd6f4\\\",\\\"input.placeholderForeground\\\":\\\"#cdd6f473\\\",\\\"inputOption.activeBackground\\\":\\\"#585b70\\\",\\\"inputOption.activeBorder\\\":\\\"#cba6f7\\\",\\\"inputOption.activeForeground\\\":\\\"#cdd6f4\\\",\\\"inputValidation.errorBackground\\\":\\\"#f38ba8\\\",\\\"inputValidation.errorBorder\\\":\\\"#11111b33\\\",\\\"inputValidation.errorForeground\\\":\\\"#11111b\\\",\\\"inputValidation.infoBackground\\\":\\\"#89b4fa\\\",\\\"inputValidation.infoBorder\\\":\\\"#11111b33\\\",\\\"inputValidation.infoForeground\\\":\\\"#11111b\\\",\\\"inputValidation.warningBackground\\\":\\\"#fab387\\\",\\\"inputValidation.warningBorder\\\":\\\"#11111b33\\\",\\\"inputValidation.warningForeground\\\":\\\"#11111b\\\",\\\"issues.closed\\\":\\\"#cba6f7\\\",\\\"issues.newIssueDecoration\\\":\\\"#f5e0dc\\\",\\\"issues.open\\\":\\\"#a6e3a1\\\",\\\"list.activeSelectionBackground\\\":\\\"#313244\\\",\\\"list.activeSelectionForeground\\\":\\\"#cdd6f4\\\",\\\"list.dropBackground\\\":\\\"#cba6f733\\\",\\\"list.focusAndSelectionBackground\\\":\\\"#45475a\\\",\\\"list.focusBackground\\\":\\\"#313244\\\",\\\"list.focusForeground\\\":\\\"#cdd6f4\\\",\\\"list.focusOutline\\\":\\\"#00000000\\\",\\\"list.highlightForeground\\\":\\\"#cba6f7\\\",\\\"list.hoverBackground\\\":\\\"#31324480\\\",\\\"list.hoverForeground\\\":\\\"#cdd6f4\\\",\\\"list.inactiveSelectionBackground\\\":\\\"#313244\\\",\\\"list.inactiveSelectionForeground\\\":\\\"#cdd6f4\\\",\\\"list.warningForeground\\\":\\\"#fab387\\\",\\\"listFilterWidget.background\\\":\\\"#45475a\\\",\\\"listFilterWidget.noMatchesOutline\\\":\\\"#f38ba8\\\",\\\"listFilterWidget.outline\\\":\\\"#00000000\\\",\\\"menu.background\\\":\\\"#1e1e2e\\\",\\\"menu.border\\\":\\\"#1e1e2e80\\\",\\\"menu.foreground\\\":\\\"#cdd6f4\\\",\\\"menu.selectionBackground\\\":\\\"#585b70\\\",\\\"menu.selectionBorder\\\":\\\"#00000000\\\",\\\"menu.selectionForeground\\\":\\\"#cdd6f4\\\",\\\"menu.separatorBackground\\\":\\\"#585b70\\\",\\\"menubar.selectionBackground\\\":\\\"#45475a\\\",\\\"menubar.selectionForeground\\\":\\\"#cdd6f4\\\",\\\"merge.commonContentBackground\\\":\\\"#45475a\\\",\\\"merge.commonHeaderBackground\\\":\\\"#585b70\\\",\\\"merge.currentContentBackground\\\":\\\"#a6e3a133\\\",\\\"merge.currentHeaderBackground\\\":\\\"#a6e3a166\\\",\\\"merge.incomingContentBackground\\\":\\\"#89b4fa33\\\",\\\"merge.incomingHeaderBackground\\\":\\\"#89b4fa66\\\",\\\"minimap.background\\\":\\\"#18182580\\\",\\\"minimap.errorHighlight\\\":\\\"#f38ba8bf\\\",\\\"minimap.findMatchHighlight\\\":\\\"#89dceb4d\\\",\\\"minimap.selectionHighlight\\\":\\\"#585b70bf\\\",\\\"minimap.selectionOccurrenceHighlight\\\":\\\"#585b70bf\\\",\\\"minimap.warningHighlight\\\":\\\"#fab387bf\\\",\\\"minimapGutter.addedBackground\\\":\\\"#a6e3a1bf\\\",\\\"minimapGutter.deletedBackground\\\":\\\"#f38ba8bf\\\",\\\"minimapGutter.modifiedBackground\\\":\\\"#f9e2afbf\\\",\\\"minimapSlider.activeBackground\\\":\\\"#cba6f799\\\",\\\"minimapSlider.background\\\":\\\"#cba6f733\\\",\\\"minimapSlider.hoverBackground\\\":\\\"#cba6f766\\\",\\\"notificationCenter.border\\\":\\\"#cba6f7\\\",\\\"notificationCenterHeader.background\\\":\\\"#181825\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#cdd6f4\\\",\\\"notificationLink.foreground\\\":\\\"#89b4fa\\\",\\\"notificationToast.border\\\":\\\"#cba6f7\\\",\\\"notifications.background\\\":\\\"#181825\\\",\\\"notifications.border\\\":\\\"#cba6f7\\\",\\\"notifications.foreground\\\":\\\"#cdd6f4\\\",\\\"notificationsErrorIcon.foreground\\\":\\\"#f38ba8\\\",\\\"notificationsInfoIcon.foreground\\\":\\\"#89b4fa\\\",\\\"notificationsWarningIcon.foreground\\\":\\\"#fab387\\\",\\\"panel.background\\\":\\\"#1e1e2e\\\",\\\"panel.border\\\":\\\"#585b70\\\",\\\"panelSection.border\\\":\\\"#585b70\\\",\\\"panelSection.dropBackground\\\":\\\"#cba6f733\\\",\\\"panelTitle.activeBorder\\\":\\\"#cba6f7\\\",\\\"panelTitle.activeForeground\\\":\\\"#cdd6f4\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#a6adc8\\\",\\\"peekView.border\\\":\\\"#cba6f7\\\",\\\"peekViewEditor.background\\\":\\\"#181825\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#89dceb4d\\\",\\\"peekViewEditor.matchHighlightBorder\\\":\\\"#00000000\\\",\\\"peekViewEditorGutter.background\\\":\\\"#181825\\\",\\\"peekViewResult.background\\\":\\\"#181825\\\",\\\"peekViewResult.fileForeground\\\":\\\"#cdd6f4\\\",\\\"peekViewResult.lineForeground\\\":\\\"#cdd6f4\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#89dceb4d\\\",\\\"peekViewResult.selectionBackground\\\":\\\"#313244\\\",\\\"peekViewResult.selectionForeground\\\":\\\"#cdd6f4\\\",\\\"peekViewTitle.background\\\":\\\"#1e1e2e\\\",\\\"peekViewTitleDescription.foreground\\\":\\\"#bac2deb3\\\",\\\"peekViewTitleLabel.foreground\\\":\\\"#cdd6f4\\\",\\\"pickerGroup.border\\\":\\\"#cba6f7\\\",\\\"pickerGroup.foreground\\\":\\\"#cba6f7\\\",\\\"problemsErrorIcon.foreground\\\":\\\"#f38ba8\\\",\\\"problemsInfoIcon.foreground\\\":\\\"#89b4fa\\\",\\\"problemsWarningIcon.foreground\\\":\\\"#fab387\\\",\\\"progressBar.background\\\":\\\"#cba6f7\\\",\\\"pullRequests.closed\\\":\\\"#f38ba8\\\",\\\"pullRequests.draft\\\":\\\"#9399b2\\\",\\\"pullRequests.merged\\\":\\\"#cba6f7\\\",\\\"pullRequests.notification\\\":\\\"#cdd6f4\\\",\\\"pullRequests.open\\\":\\\"#a6e3a1\\\",\\\"sash.hoverBorder\\\":\\\"#cba6f7\\\",\\\"scrollbar.shadow\\\":\\\"#11111b\\\",\\\"scrollbarSlider.activeBackground\\\":\\\"#31324466\\\",\\\"scrollbarSlider.background\\\":\\\"#585b7080\\\",\\\"scrollbarSlider.hoverBackground\\\":\\\"#6c7086\\\",\\\"selection.background\\\":\\\"#cba6f766\\\",\\\"settings.dropdownBackground\\\":\\\"#45475a\\\",\\\"settings.dropdownListBorder\\\":\\\"#00000000\\\",\\\"settings.focusedRowBackground\\\":\\\"#585b7033\\\",\\\"settings.headerForeground\\\":\\\"#cdd6f4\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#cba6f7\\\",\\\"settings.numberInputBackground\\\":\\\"#45475a\\\",\\\"settings.numberInputBorder\\\":\\\"#00000000\\\",\\\"settings.textInputBackground\\\":\\\"#45475a\\\",\\\"settings.textInputBorder\\\":\\\"#00000000\\\",\\\"sideBar.background\\\":\\\"#181825\\\",\\\"sideBar.border\\\":\\\"#00000000\\\",\\\"sideBar.dropBackground\\\":\\\"#cba6f733\\\",\\\"sideBar.foreground\\\":\\\"#cdd6f4\\\",\\\"sideBarSectionHeader.background\\\":\\\"#181825\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#cdd6f4\\\",\\\"sideBarTitle.foreground\\\":\\\"#cba6f7\\\",\\\"statusBar.background\\\":\\\"#11111b\\\",\\\"statusBar.border\\\":\\\"#00000000\\\",\\\"statusBar.debuggingBackground\\\":\\\"#fab387\\\",\\\"statusBar.debuggingBorder\\\":\\\"#00000000\\\",\\\"statusBar.debuggingForeground\\\":\\\"#11111b\\\",\\\"statusBar.foreground\\\":\\\"#cdd6f4\\\",\\\"statusBar.noFolderBackground\\\":\\\"#11111b\\\",\\\"statusBar.noFolderBorder\\\":\\\"#00000000\\\",\\\"statusBar.noFolderForeground\\\":\\\"#cdd6f4\\\",\\\"statusBarItem.activeBackground\\\":\\\"#585b7066\\\",\\\"statusBarItem.errorBackground\\\":\\\"#00000000\\\",\\\"statusBarItem.errorForeground\\\":\\\"#f38ba8\\\",\\\"statusBarItem.hoverBackground\\\":\\\"#585b7033\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#00000000\\\",\\\"statusBarItem.prominentForeground\\\":\\\"#cba6f7\\\",\\\"statusBarItem.prominentHoverBackground\\\":\\\"#585b7033\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#89b4fa\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#11111b\\\",\\\"statusBarItem.warningBackground\\\":\\\"#00000000\\\",\\\"statusBarItem.warningForeground\\\":\\\"#fab387\\\",\\\"symbolIcon.arrayForeground\\\":\\\"#fab387\\\",\\\"symbolIcon.booleanForeground\\\":\\\"#cba6f7\\\",\\\"symbolIcon.classForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.colorForeground\\\":\\\"#f5c2e7\\\",\\\"symbolIcon.constantForeground\\\":\\\"#fab387\\\",\\\"symbolIcon.constructorForeground\\\":\\\"#b4befe\\\",\\\"symbolIcon.enumeratorForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.enumeratorMemberForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.eventForeground\\\":\\\"#f5c2e7\\\",\\\"symbolIcon.fieldForeground\\\":\\\"#cdd6f4\\\",\\\"symbolIcon.fileForeground\\\":\\\"#cba6f7\\\",\\\"symbolIcon.folderForeground\\\":\\\"#cba6f7\\\",\\\"symbolIcon.functionForeground\\\":\\\"#89b4fa\\\",\\\"symbolIcon.interfaceForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.keyForeground\\\":\\\"#94e2d5\\\",\\\"symbolIcon.keywordForeground\\\":\\\"#cba6f7\\\",\\\"symbolIcon.methodForeground\\\":\\\"#89b4fa\\\",\\\"symbolIcon.moduleForeground\\\":\\\"#cdd6f4\\\",\\\"symbolIcon.namespaceForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.nullForeground\\\":\\\"#eba0ac\\\",\\\"symbolIcon.numberForeground\\\":\\\"#fab387\\\",\\\"symbolIcon.objectForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.operatorForeground\\\":\\\"#94e2d5\\\",\\\"symbolIcon.packageForeground\\\":\\\"#f2cdcd\\\",\\\"symbolIcon.propertyForeground\\\":\\\"#eba0ac\\\",\\\"symbolIcon.referenceForeground\\\":\\\"#f9e2af\\\",\\\"symbolIcon.snippetForeground\\\":\\\"#f2cdcd\\\",\\\"symbolIcon.stringForeground\\\":\\\"#a6e3a1\\\",\\\"symbolIcon.structForeground\\\":\\\"#94e2d5\\\",\\\"symbolIcon.textForeground\\\":\\\"#cdd6f4\\\",\\\"symbolIcon.typeParameterForeground\\\":\\\"#eba0ac\\\",\\\"symbolIcon.unitForeground\\\":\\\"#cdd6f4\\\",\\\"symbolIcon.variableForeground\\\":\\\"#cdd6f4\\\",\\\"tab.activeBackground\\\":\\\"#1e1e2e\\\",\\\"tab.activeBorder\\\":\\\"#00000000\\\",\\\"tab.activeBorderTop\\\":\\\"#cba6f7\\\",\\\"tab.activeForeground\\\":\\\"#cba6f7\\\",\\\"tab.activeModifiedBorder\\\":\\\"#f9e2af\\\",\\\"tab.border\\\":\\\"#181825\\\",\\\"tab.hoverBackground\\\":\\\"#28283d\\\",\\\"tab.hoverBorder\\\":\\\"#00000000\\\",\\\"tab.hoverForeground\\\":\\\"#cba6f7\\\",\\\"tab.inactiveBackground\\\":\\\"#181825\\\",\\\"tab.inactiveForeground\\\":\\\"#6c7086\\\",\\\"tab.inactiveModifiedBorder\\\":\\\"#f9e2af4d\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cba6f7\\\",\\\"tab.unfocusedActiveBackground\\\":\\\"#181825\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#00000000\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#cba6f74d\\\",\\\"tab.unfocusedInactiveBackground\\\":\\\"#0e0e16\\\",\\\"table.headerBackground\\\":\\\"#313244\\\",\\\"table.headerForeground\\\":\\\"#cdd6f4\\\",\\\"terminal.ansiBlack\\\":\\\"#45475a\\\",\\\"terminal.ansiBlue\\\":\\\"#89b4fa\\\",\\\"terminal.ansiBrightBlack\\\":\\\"#585b70\\\",\\\"terminal.ansiBrightBlue\\\":\\\"#74a8fc\\\",\\\"terminal.ansiBrightCyan\\\":\\\"#6bd7ca\\\",\\\"terminal.ansiBrightGreen\\\":\\\"#89d88b\\\",\\\"terminal.ansiBrightMagenta\\\":\\\"#f2aede\\\",\\\"terminal.ansiBrightRed\\\":\\\"#f37799\\\",\\\"terminal.ansiBrightWhite\\\":\\\"#bac2de\\\",\\\"terminal.ansiBrightYellow\\\":\\\"#ebd391\\\",\\\"terminal.ansiCyan\\\":\\\"#94e2d5\\\",\\\"terminal.ansiGreen\\\":\\\"#a6e3a1\\\",\\\"terminal.ansiMagenta\\\":\\\"#f5c2e7\\\",\\\"terminal.ansiRed\\\":\\\"#f38ba8\\\",\\\"terminal.ansiWhite\\\":\\\"#a6adc8\\\",\\\"terminal.ansiYellow\\\":\\\"#f9e2af\\\",\\\"terminal.border\\\":\\\"#585b70\\\",\\\"terminal.dropBackground\\\":\\\"#cba6f733\\\",\\\"terminal.foreground\\\":\\\"#cdd6f4\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#585b7080\\\",\\\"terminal.selectionBackground\\\":\\\"#585b70\\\",\\\"terminal.tab.activeBorder\\\":\\\"#cba6f7\\\",\\\"terminalCommandDecoration.defaultBackground\\\":\\\"#585b70\\\",\\\"terminalCommandDecoration.errorBackground\\\":\\\"#f38ba8\\\",\\\"terminalCommandDecoration.successBackground\\\":\\\"#a6e3a1\\\",\\\"terminalCursor.background\\\":\\\"#1e1e2e\\\",\\\"terminalCursor.foreground\\\":\\\"#f5e0dc\\\",\\\"textBlockQuote.background\\\":\\\"#181825\\\",\\\"textBlockQuote.border\\\":\\\"#11111b\\\",\\\"textCodeBlock.background\\\":\\\"#1e1e2e\\\",\\\"textLink.activeForeground\\\":\\\"#89dceb\\\",\\\"textLink.foreground\\\":\\\"#89b4fa\\\",\\\"textPreformat.foreground\\\":\\\"#cdd6f4\\\",\\\"textSeparator.foreground\\\":\\\"#cba6f7\\\",\\\"titleBar.activeBackground\\\":\\\"#11111b\\\",\\\"titleBar.activeForeground\\\":\\\"#cdd6f4\\\",\\\"titleBar.border\\\":\\\"#00000000\\\",\\\"titleBar.inactiveBackground\\\":\\\"#11111b\\\",\\\"titleBar.inactiveForeground\\\":\\\"#cdd6f480\\\",\\\"tree.inactiveIndentGuidesStroke\\\":\\\"#45475a\\\",\\\"tree.indentGuidesStroke\\\":\\\"#9399b2\\\",\\\"walkThrough.embeddedEditorBackground\\\":\\\"#1e1e2e4d\\\",\\\"welcomePage.progress.background\\\":\\\"#11111b\\\",\\\"welcomePage.progress.foreground\\\":\\\"#cba6f7\\\",\\\"welcomePage.tileBackground\\\":\\\"#181825\\\",\\\"widget.shadow\\\":\\\"#18182580\\\",\\\"window.activeBorder\\\":\\\"#00000000\\\",\\\"window.inactiveBorder\\\":\\\"#00000000\\\"},\\\"displayName\\\":\\\"Catppuccin Mocha\\\",\\\"name\\\":\\\"catppuccin-mocha\\\",\\\"semanticHighlighting\\\":true,\\\"semanticTokenColors\\\":{\\\"boolean\\\":{\\\"foreground\\\":\\\"#fab387\\\"},\\\"builtinAttribute.attribute.library:rust\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"},\\\"class.builtin:python\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"},\\\"class:python\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"},\\\"constant.builtin.readonly:nix\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"},\\\"enumMember\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"},\\\"function.decorator:python\\\":{\\\"foreground\\\":\\\"#fab387\\\"},\\\"generic.attribute:rust\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"heading\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"},\\\"number\\\":{\\\"foreground\\\":\\\"#fab387\\\"},\\\"pol\\\":{\\\"foreground\\\":\\\"#f2cdcd\\\"},\\\"property.readonly:javascript\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"property.readonly:javascriptreact\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"property.readonly:typescript\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"property.readonly:typescriptreact\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"selfKeyword\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"},\\\"text.emph\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f38ba8\\\"},\\\"text.math\\\":{\\\"foreground\\\":\\\"#f2cdcd\\\"},\\\"text.strong\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#f38ba8\\\"},\\\"tomlArrayKey\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#89b4fa\\\"},\\\"tomlTableKey\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#89b4fa\\\"},\\\"type.defaultLibrary:go\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"},\\\"variable.defaultLibrary\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"},\\\"variable.readonly.defaultLibrary:go\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"},\\\"variable.readonly:javascript\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"variable.readonly:javascriptreact\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"variable.readonly:scala\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"variable.readonly:typescript\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"variable.readonly:typescriptreact\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"},\\\"variable.typeHint:python\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},\\\"tokenColors\\\":[{\\\"scope\\\":[\\\"text\\\",\\\"source\\\",\\\"variable.other.readwrite\\\",\\\"punctuation.definition.variable\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"punctuation\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#9399b2\\\"}},{\\\"scope\\\":[\\\"comment\\\",\\\"punctuation.definition.comment\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#9399b2\\\"}},{\\\"scope\\\":[\\\"string\\\",\\\"punctuation.definition.string\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":\\\"constant.character.escape\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.constant\\\",\\\"entity.name.constant\\\",\\\"constant.language.boolean\\\",\\\"constant.language.false\\\",\\\"constant.language.true\\\",\\\"keyword.other.unit.user-defined\\\",\\\"keyword.other.unit.suffix.floating-point\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"keyword\\\",\\\"keyword.operator.word\\\",\\\"keyword.operator.new\\\",\\\"variable.language.super\\\",\\\"support.type.primitive\\\",\\\"storage.type\\\",\\\"storage.modifier\\\",\\\"punctuation.definition.keyword\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"entity.name.tag.documentation\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"keyword.operator\\\",\\\"punctuation.accessor\\\",\\\"punctuation.definition.generic\\\",\\\"meta.function.closure punctuation.section.parameters\\\",\\\"punctuation.definition.tag\\\",\\\"punctuation.separator.key-value\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":[\\\"entity.name.function\\\",\\\"meta.function-call.method\\\",\\\"support.function\\\",\\\"support.function.misc\\\",\\\"variable.function\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"entity.name.class\\\",\\\"entity.other.inherited-class\\\",\\\"support.class\\\",\\\"meta.function-call.constructor\\\",\\\"entity.name.struct\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"entity.name.enum\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"meta.enum variable.other.readwrite\\\",\\\"variable.other.enummember\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"meta.property.object\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":[\\\"meta.type\\\",\\\"meta.type-alias\\\",\\\"support.type\\\",\\\"entity.name.type\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"meta.annotation variable.function\\\",\\\"meta.annotation variable.annotation.function\\\",\\\"meta.annotation punctuation.definition.annotation\\\",\\\"meta.decorator\\\",\\\"punctuation.decorator\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"variable.parameter\\\",\\\"meta.function.parameters\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":[\\\"constant.language\\\",\\\"support.function.builtin\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":\\\"entity.other.attribute-name.documentation\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"keyword.control.directive\\\",\\\"punctuation.definition.directive\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"punctuation.definition.typeparameters\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":\\\"entity.name.namespace\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"support.type.property-name.css\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"variable.language.this\\\",\\\"variable.language.this punctuation.definition.variable\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":\\\"variable.object.property\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"string.template variable\\\",\\\"string variable\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"keyword.operator.new\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"}},{\\\"scope\\\":\\\"storage.modifier.specifier.extern.cpp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"entity.name.scope-resolution.template.call.cpp\\\",\\\"entity.name.scope-resolution.parameter.cpp\\\",\\\"entity.name.scope-resolution.cpp\\\",\\\"entity.name.scope-resolution.function.definition.cpp\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"storage.type.class.doxygen\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":[\\\"storage.modifier.reference.cpp\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"meta.interpolation.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"comment.block.documentation.cs\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"source.css entity.other.attribute-name.class.css\\\",\\\"entity.other.attribute-name.parent-selector.css punctuation.definition.entity.css\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"punctuation.separator.operator.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"source.css constant.other.unicode-range\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"source.css variable.parameter.url\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":[\\\"support.type.vendored.property-name\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":[\\\"source.css meta.property-value variable\\\",\\\"source.css meta.property-value variable.other.less\\\",\\\"source.css meta.property-value variable.other.less punctuation.definition.variable.less\\\",\\\"meta.definition.variable.scss\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":[\\\"source.css meta.property-list variable\\\",\\\"meta.property-list variable.other.less\\\",\\\"meta.property-list variable.other.less punctuation.definition.variable.less\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"keyword.other.unit.percentage.css\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"source.css meta.attribute-selector\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":[\\\"keyword.other.definition.ini\\\",\\\"punctuation.support.type.property-name.json\\\",\\\"support.type.property-name.json\\\",\\\"punctuation.support.type.property-name.toml\\\",\\\"support.type.property-name.toml\\\",\\\"entity.name.tag.yaml\\\",\\\"punctuation.support.type.property-name.yaml\\\",\\\"support.type.property-name.yaml\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"constant.language.json\\\",\\\"constant.language.yaml\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"entity.name.type.anchor.yaml\\\",\\\"variable.other.alias.yaml\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"support.type.property-name.table\\\",\\\"entity.name.section.group-title.ini\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"constant.other.time.datetime.offset.toml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.anchor.yaml\\\",\\\"punctuation.definition.alias.yaml\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"entity.other.document.begin.yaml\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"markup.changed.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"meta.diff.header.from-file\\\",\\\"meta.diff.header.to-file\\\",\\\"punctuation.definition.from-file.diff\\\",\\\"punctuation.definition.to-file.diff\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"markup.inserted.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":\\\"markup.deleted.diff\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"variable.other.env\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"string.quoted variable.other.env\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"support.function.builtin.gdscript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"constant.language.gdscript\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"comment meta.annotation.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"comment meta.annotation.parameters.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"constant.language.go\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"variable.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"string.unquoted.alias.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f2cdcd\\\"}},{\\\"scope\\\":\\\"constant.character.enum.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"meta.objectvalues.graphql constant.object.key.graphql string.unquoted.graphql\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f2cdcd\\\"}},{\\\"scope\\\":[\\\"keyword.other.doctype\\\",\\\"meta.tag.sgml.doctype punctuation.definition.tag\\\",\\\"meta.tag.metadata.doctype entity.name.tag\\\",\\\"meta.tag.metadata.doctype punctuation.definition.tag\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"entity.name.tag\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"text.html constant.character.entity\\\",\\\"text.html constant.character.entity punctuation\\\",\\\"constant.character.entity.xml\\\",\\\"constant.character.entity.xml punctuation\\\",\\\"constant.character.entity.js.jsx\\\",\\\"constant.charactger.entity.js.jsx punctuation\\\",\\\"constant.character.entity.tsx\\\",\\\"constant.character.entity.tsx punctuation\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"entity.other.attribute-name\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"support.class.component\\\",\\\"support.class.component.jsx\\\",\\\"support.class.component.tsx\\\",\\\"support.class.component.vue\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.annotation\\\",\\\"storage.type.annotation\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"constant.other.enum.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"storage.modifier.import.java\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"comment.block.javadoc.java keyword.other.documentation.javadoc.java\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":\\\"meta.export variable.other.readwrite.js\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":[\\\"variable.other.constant.js\\\",\\\"variable.other.constant.ts\\\",\\\"variable.other.property.js\\\",\\\"variable.other.property.ts\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"variable.other.jsdoc\\\",\\\"comment.block.documentation variable.other\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"storage.type.class.jsdoc\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":\\\"support.type.object.console.js\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"support.constant.node\\\",\\\"support.type.object.module.js\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"storage.modifier.implements\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"constant.language.null.js\\\",\\\"constant.language.null.ts\\\",\\\"constant.language.undefined.js\\\",\\\"constant.language.undefined.ts\\\",\\\"support.type.builtin.ts\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"variable.parameter.generic\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"keyword.declaration.function.arrow.js\\\",\\\"storage.type.function.arrow.ts\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"punctuation.decorator.ts\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"keyword.operator.expression.in.js\\\",\\\"keyword.operator.expression.in.ts\\\",\\\"keyword.operator.expression.infer.ts\\\",\\\"keyword.operator.expression.instanceof.js\\\",\\\"keyword.operator.expression.instanceof.ts\\\",\\\"keyword.operator.expression.is\\\",\\\"keyword.operator.expression.keyof.ts\\\",\\\"keyword.operator.expression.of.js\\\",\\\"keyword.operator.expression.of.ts\\\",\\\"keyword.operator.expression.typeof.ts\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"support.function.macro.julia\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"constant.language.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"constant.other.symbol.julia\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"text.tex keyword.control.preamble\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"text.tex support.function.be\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":\\\"constant.other.general.math.tex\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f2cdcd\\\"}},{\\\"scope\\\":\\\"variable.language.liquid\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"comment.line.double-dash.documentation.lua storage.type.annotation.lua\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"comment.line.double-dash.documentation.lua entity.name.variable.lua\\\",\\\"comment.line.double-dash.documentation.lua variable.lua\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"heading.1.markdown punctuation.definition.heading.markdown\\\",\\\"heading.1.markdown\\\",\\\"heading.1.quarto punctuation.definition.heading.quarto\\\",\\\"heading.1.quarto\\\",\\\"markup.heading.atx.1.mdx\\\",\\\"markup.heading.atx.1.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.setext.1.markdown\\\",\\\"markup.heading.heading-0.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"heading.2.markdown punctuation.definition.heading.markdown\\\",\\\"heading.2.markdown\\\",\\\"heading.2.quarto punctuation.definition.heading.quarto\\\",\\\"heading.2.quarto\\\",\\\"markup.heading.atx.2.mdx\\\",\\\"markup.heading.atx.2.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.setext.2.markdown\\\",\\\"markup.heading.heading-1.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"heading.3.markdown punctuation.definition.heading.markdown\\\",\\\"heading.3.markdown\\\",\\\"heading.3.quarto punctuation.definition.heading.quarto\\\",\\\"heading.3.quarto\\\",\\\"markup.heading.atx.3.mdx\\\",\\\"markup.heading.atx.3.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.heading-2.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"heading.4.markdown punctuation.definition.heading.markdown\\\",\\\"heading.4.markdown\\\",\\\"heading.4.quarto punctuation.definition.heading.quarto\\\",\\\"heading.4.quarto\\\",\\\"markup.heading.atx.4.mdx\\\",\\\"markup.heading.atx.4.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.heading-3.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":[\\\"heading.5.markdown punctuation.definition.heading.markdown\\\",\\\"heading.5.markdown\\\",\\\"heading.5.quarto punctuation.definition.heading.quarto\\\",\\\"heading.5.quarto\\\",\\\"markup.heading.atx.5.mdx\\\",\\\"markup.heading.atx.5.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.heading-4.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#74c7ec\\\"}},{\\\"scope\\\":[\\\"heading.6.markdown punctuation.definition.heading.markdown\\\",\\\"heading.6.markdown\\\",\\\"heading.6.quarto punctuation.definition.heading.quarto\\\",\\\"heading.6.quarto\\\",\\\"markup.heading.atx.6.mdx\\\",\\\"markup.heading.atx.6.mdx punctuation.definition.heading.mdx\\\",\\\"markup.heading.heading-5.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#b4befe\\\"}},{\\\"scope\\\":\\\"markup.bold\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":\\\"markup.italic\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":\\\"markup.strikethrough\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\",\\\"foreground\\\":\\\"#a6adc8\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.link\\\",\\\"markup.underline.link\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"text.html.markdown punctuation.definition.link.title\\\",\\\"text.html.quarto punctuation.definition.link.title\\\",\\\"string.other.link.title.markdown\\\",\\\"string.other.link.title.quarto\\\",\\\"markup.link\\\",\\\"punctuation.definition.constant.markdown\\\",\\\"punctuation.definition.constant.quarto\\\",\\\"constant.other.reference.link.markdown\\\",\\\"constant.other.reference.link.quarto\\\",\\\"markup.substitution.attribute-reference\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#b4befe\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.raw.markdown\\\",\\\"punctuation.definition.raw.quarto\\\",\\\"markup.inline.raw.string.markdown\\\",\\\"markup.inline.raw.string.quarto\\\",\\\"markup.raw.block.markdown\\\",\\\"markup.raw.block.quarto\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":\\\"fenced_code.block.language\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":[\\\"markup.fenced_code.block punctuation.definition\\\",\\\"markup.raw support.asciidoc\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#9399b2\\\"}},{\\\"scope\\\":[\\\"markup.quote\\\",\\\"punctuation.definition.quote.begin\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"meta.separator.markdown\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.list.begin.markdown\\\",\\\"punctuation.definition.list.begin.quarto\\\",\\\"markup.list.bullet\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"markup.heading.quarto\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"}},{\\\"scope\\\":[\\\"entity.other.attribute-name.multipart.nix\\\",\\\"entity.other.attribute-name.single.nix\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"variable.parameter.name.nix\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"meta.embedded variable.parameter.name.nix\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#b4befe\\\"}},{\\\"scope\\\":\\\"string.unquoted.path.nix\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"support.attribute.builtin\\\",\\\"meta.attribute.php\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"meta.function.parameters.php punctuation.definition.variable.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"constant.language.php\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"text.html.php support.function\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":\\\"keyword.other.phpdoc.php\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":[\\\"support.variable.magic.python\\\",\\\"meta.function-call.arguments.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"support.function.magic.python\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":[\\\"variable.parameter.function.language.special.self.python\\\",\\\"variable.language.special.self.python\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"keyword.control.flow.python\\\",\\\"keyword.operator.logical.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"storage.type.function.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"support.token.decorator.python\\\",\\\"meta.function.decorator.identifier.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":[\\\"meta.function-call.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"entity.name.function.decorator.python\\\",\\\"punctuation.definition.decorator.python\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"constant.character.format.placeholder.other.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"support.type.exception.python\\\",\\\"support.function.builtin.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"support.type.python\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"constant.language.python\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":[\\\"meta.indexed-name.python\\\",\\\"meta.item-access.python\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"storage.type.string.python\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":\\\"meta.function.parameters.python\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":[\\\"string.regexp punctuation.definition.string.begin\\\",\\\"string.regexp punctuation.definition.string.end\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"keyword.control.anchor.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"string.regexp.ts\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"keyword.other.back-reference.regexp\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#a6e3a1\\\"}},{\\\"scope\\\":\\\"punctuation.definition.character-class.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"constant.other.character-class.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"constant.other.character-class.range.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5e0dc\\\"}},{\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"constant.character.numeric.regexp\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"punctuation.definition.group.no-capture.regexp\\\",\\\"meta.assertion.look-ahead.regexp\\\",\\\"meta.assertion.negative-look-ahead.regexp\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"meta.annotation.rust\\\",\\\"meta.annotation.rust punctuation\\\",\\\"meta.attribute.rust\\\",\\\"punctuation.definition.attribute.rust\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":[\\\"meta.attribute.rust string.quoted.double.rust\\\",\\\"meta.attribute.rust string.quoted.single.char.rust\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\"}},{\\\"scope\\\":[\\\"entity.name.function.macro.rules.rust\\\",\\\"storage.type.module.rust\\\",\\\"storage.modifier.rust\\\",\\\"storage.type.struct.rust\\\",\\\"storage.type.enum.rust\\\",\\\"storage.type.trait.rust\\\",\\\"storage.type.union.rust\\\",\\\"storage.type.impl.rust\\\",\\\"storage.type.rust\\\",\\\"storage.type.function.rust\\\",\\\"storage.type.type.rust\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"entity.name.type.numeric.rust\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"\\\",\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"meta.generic.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"entity.name.impl.rust\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"entity.name.module.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":\\\"entity.name.trait.rust\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"storage.type.source.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"entity.name.union.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f9e2af\\\"}},{\\\"scope\\\":\\\"meta.enum.rust storage.type.source.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":[\\\"support.macro.rust\\\",\\\"meta.macro.rust support.function.rust\\\",\\\"entity.name.function.macro.rust\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":[\\\"storage.modifier.lifetime.rust\\\",\\\"entity.name.type.lifetime\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"string.quoted.double.rust constant.other.placeholder.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"meta.function.return-type.rust meta.generic.rust storage.type.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"meta.function.call.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89b4fa\\\"}},{\\\"scope\\\":\\\"punctuation.brackets.angle.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#89dceb\\\"}},{\\\"scope\\\":\\\"constant.other.caps.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"meta.function.definition.rust variable.other.rust\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#eba0ac\\\"}},{\\\"scope\\\":\\\"meta.function.call.rust variable.other.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}},{\\\"scope\\\":\\\"variable.language.self.rust\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":[\\\"variable.other.metavariable.name.rust\\\",\\\"meta.macro.metavariable.rust keyword.operator.macro.dollar.rust\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":[\\\"comment.line.shebang\\\",\\\"comment.line.shebang punctuation.definition.comment\\\",\\\"comment.line.shebang\\\",\\\"punctuation.definition.comment.shebang.shell\\\",\\\"meta.shebang.shell\\\"],\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#f5c2e7\\\"}},{\\\"scope\\\":\\\"comment.line.shebang constant.language\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":[\\\"meta.function-call.arguments.shell punctuation.definition.variable.shell\\\",\\\"meta.function-call.arguments.shell punctuation.section.interpolation\\\",\\\"meta.function-call.arguments.shell punctuation.definition.variable.shell\\\",\\\"meta.function-call.arguments.shell punctuation.section.interpolation\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#f38ba8\\\"}},{\\\"scope\\\":\\\"meta.string meta.interpolation.parameter.shell variable.other.readwrite\\\",\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\",\\\"foreground\\\":\\\"#fab387\\\"}},{\\\"scope\\\":[\\\"source.shell punctuation.section.interpolation\\\",\\\"punctuation.definition.evaluation.backticks.shell\\\"],\\\"settings\\\":{\\\"foreground\\\":\\\"#94e2d5\\\"}},{\\\"scope\\\":\\\"entity.name.tag.heredoc.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cba6f7\\\"}},{\\\"scope\\\":\\\"string.quoted.double.shell variable.other.normal.shell\\\",\\\"settings\\\":{\\\"foreground\\\":\\\"#cdd6f4\\\"}}],\\\"type\\\":\\\"dark\\\"}\"))\n"], "names": [], "mappings": "AAAA,2BAA2B;;;uCACZ,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}