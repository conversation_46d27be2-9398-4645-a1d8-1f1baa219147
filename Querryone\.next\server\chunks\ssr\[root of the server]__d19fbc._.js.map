{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/UserUploadedContent.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PiFile, PiFileText, PiMusicNote, PiYoutubeLogo, PiLink } from 'react-icons/pi';\r\nimport { UploadedFile, UploadedURL } from '@/stores/chatList';\r\n\r\ninterface UserUploadedContentProps {\r\n  uploadedFiles?: UploadedFile[];\r\n  uploadedURLs?: UploadedURL[];\r\n  selectedLanguage: string;\r\n}\r\n\r\nconst UserUploadedContent: React.FC<UserUploadedContentProps> = ({\r\n  uploadedFiles = [],\r\n  uploadedURLs = [],\r\n  selectedLanguage\r\n}) => {\r\n  // Function to get language-specific text\r\n  const getLanguageText = () => {\r\n    switch(selectedLanguage) {\r\n      case \"Tamil\":\r\n        return {\r\n          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',\r\n          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',\r\n          pdfDocument: 'PDF ஆவணம்',\r\n          mp3Audio: 'MP3 ஆடியோ',\r\n          youtubeVideo: 'YouTube வீடியோ',\r\n          articleLink: 'கட்டுரை இணைப்பு',\r\n          basedOn: 'அடிப்படையில்'\r\n        };\r\n      case \"Telugu\":\r\n        return {\r\n          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',\r\n          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',\r\n          pdfDocument: 'PDF డాక్యుమెంట్',\r\n          mp3Audio: 'MP3 ఆడియో',\r\n          youtubeVideo: 'YouTube వీడియో',\r\n          articleLink: 'ఆర్టికల్ లింక్',\r\n          basedOn: 'ఆధారంగా'\r\n        };\r\n      case \"Kannada\":\r\n        return {\r\n          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',\r\n          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',\r\n          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',\r\n          mp3Audio: 'MP3 ಆಡಿಯೋ',\r\n          youtubeVideo: 'YouTube ವೀಡಿಯೋ',\r\n          articleLink: 'ಲೇಖನ ಲಿಂಕ್',\r\n          basedOn: 'ಆಧಾರದ ಮೇಲೆ'\r\n        };\r\n      default:\r\n        return {\r\n          uploadedFiles: 'Uploaded Files',\r\n          uploadedUrls: 'Uploaded URLs',\r\n          pdfDocument: 'PDF Document',\r\n          mp3Audio: 'MP3 Audio',\r\n          youtubeVideo: 'YouTube Video',\r\n          articleLink: 'Article Link',\r\n          basedOn: 'Based on'\r\n        };\r\n    }\r\n  };\r\n\r\n  // Function to get file icon based on file type\r\n  const getFileIcon = (fileName: string) => {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    \r\n    if (extension === 'pdf' || extension === 'doc' || extension === 'docx') {\r\n      return <PiFileText className=\"w-4 h-4\" />;\r\n    } else if (extension === 'mp3' || extension === 'wav' || extension === 'm4a') {\r\n      return <PiMusicNote className=\"w-4 h-4\" />;\r\n    }\r\n    return <PiFile className=\"w-4 h-4\" />;\r\n  };\r\n\r\n  // Function to get URL icon based on type\r\n  const getUrlIcon = (type: 'youtube' | 'article') => {\r\n    if (type === 'youtube') {\r\n      return <PiYoutubeLogo className=\"w-4 h-4\" />;\r\n    }\r\n    return <PiLink className=\"w-4 h-4\" />;\r\n  };\r\n\r\n  // Function to get language-specific color\r\n  const getLanguageColor = () => {\r\n    switch(selectedLanguage) {\r\n      case \"Tamil\":\r\n        return \"text-purple-600 dark:text-purple-400\";\r\n      case \"Telugu\":\r\n        return \"text-green-600 dark:text-green-400\";\r\n      case \"Kannada\":\r\n        return \"text-orange-600 dark:text-orange-400\";\r\n      default:\r\n        return \"text-blue-600 dark:text-blue-400\";\r\n    }\r\n  };\r\n\r\n  // Function to get background color\r\n  const getBackgroundColor = () => {\r\n    switch(selectedLanguage) {\r\n      case \"Tamil\":\r\n        return \"bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800\";\r\n      case \"Telugu\":\r\n        return \"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800\";\r\n      case \"Kannada\":\r\n        return \"bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800\";\r\n      default:\r\n        return \"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800\";\r\n    }\r\n  };\r\n\r\n  // Don't render if no uploaded content\r\n  if ((!uploadedFiles || uploadedFiles.length === 0) && (!uploadedURLs || uploadedURLs.length === 0)) {\r\n    return null;\r\n  }\r\n\r\n  const text = getLanguageText();\r\n  const colorClass = getLanguageColor();\r\n  const bgClass = getBackgroundColor();\r\n\r\n  return (\r\n    <div className={`mb-3 p-3 border rounded-lg ${bgClass}`}>\r\n      <div className={`flex items-center gap-2 text-sm font-medium ${colorClass} mb-2`}>\r\n        <PiFile className=\"w-4 h-4\" />\r\n        <span>{text.basedOn}:</span>\r\n      </div>\r\n      \r\n      {/* Uploaded Files Section */}\r\n      {uploadedFiles && uploadedFiles.length > 0 && (\r\n        <div className=\"mb-2\">\r\n          <div className=\"space-y-1\">\r\n            {uploadedFiles.map((file, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center gap-2 text-xs text-gray-700 dark:text-gray-300\"\r\n              >\r\n                <div className={colorClass}>\r\n                  {getFileIcon(file.name)}\r\n                </div>\r\n                <span className=\"truncate font-medium\">\r\n                  {file.name}\r\n                </span>\r\n                <span className=\"text-gray-500 dark:text-gray-400 text-xs\">\r\n                  ({(file.size / 1024 / 1024).toFixed(1)} MB)\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Uploaded URLs Section */}\r\n      {uploadedURLs && uploadedURLs.length > 0 && (\r\n        <div>\r\n          <div className=\"space-y-1\">\r\n            {uploadedURLs.map((urlItem, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center gap-2 text-xs text-gray-700 dark:text-gray-300\"\r\n              >\r\n                <div className={colorClass}>\r\n                  {getUrlIcon(urlItem.type)}\r\n                </div>\r\n                <span className=\"font-medium\">\r\n                  {urlItem.type === 'youtube' ? text.youtubeVideo : text.articleLink}\r\n                </span>\r\n                <span className=\"text-gray-500 dark:text-gray-400 text-xs truncate max-w-xs\">\r\n                  {urlItem.url}\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserUploadedContent;\r\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,sBAA0D,CAAC,EAC/D,gBAAgB,EAAE,EAClB,eAAe,EAAE,EACjB,gBAAgB,EACjB;IACC,yCAAyC;IACzC,MAAM,kBAAkB;QACtB,OAAO;YACL,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,SAAS;gBACX;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,SAAS;gBACX;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,SAAS;gBACX;YACF;gBACE,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,SAAS;gBACX;QACJ;IACF;IAEA,+CAA+C;IAC/C,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QAE7C,IAAI,cAAc,SAAS,cAAc,SAAS,cAAc,QAAQ;YACtE,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B,OAAO,IAAI,cAAc,SAAS,cAAc,SAAS,cAAc,OAAO;YAC5E,qBAAO,8OAAC,8IAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,yCAAyC;IACzC,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,WAAW;YACtB,qBAAO,8OAAC,8IAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAClC;QACA,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,0CAA0C;IAC1C,MAAM,mBAAmB;QACvB,OAAO;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB;QACzB,OAAO;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,sCAAsC;IACtC,IAAI,CAAC,CAAC,iBAAiB,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,aAAa,MAAM,KAAK,CAAC,GAAG;QAClG,OAAO;IACT;IAEA,MAAM,OAAO;IACb,MAAM,aAAa;IACnB,MAAM,UAAU;IAEhB,qBACE,8OAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,SAAS;;0BACrD,8OAAC;gBAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW,KAAK,CAAC;;kCAC9E,8OAAC,8IAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;;4BAAM,KAAK,OAAO;4BAAC;;;;;;;;;;;;;YAIrB,iBAAiB,cAAc,MAAM,GAAG,mBACvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAW;8CACb,YAAY,KAAK,IAAI;;;;;;8CAExB,8OAAC;oCAAK,WAAU;8CACb,KAAK,IAAI;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;;wCAA2C;wCACvD,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;2BAVpC;;;;;;;;;;;;;;;YAmBd,gBAAgB,aAAa,MAAM,GAAG,mBACrC,8OAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAW;8CACb,WAAW,QAAQ,IAAI;;;;;;8CAE1B,8OAAC;oCAAK,WAAU;8CACb,QAAQ,IAAI,KAAK,YAAY,KAAK,YAAY,GAAG,KAAK,WAAW;;;;;;8CAEpE,8OAAC;oCAAK,WAAU;8CACb,QAAQ,GAAG;;;;;;;2BAVT;;;;;;;;;;;;;;;;;;;;;AAmBrB;uCAEe"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/UserMessage.tsx"], "sourcesContent": ["// components/chatComponents/UserMessage.tsx\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { PiArrowsCounterClockwise, PiCopy, PiShareFat, PiThumbsDown, PiThumbsUp } from \"react-icons/pi\";\r\nimport ReactMarkdown from 'react-markdown';\r\nimport UserUploadedContent from './UserUploadedContent';\r\nimport { UploadedFile, UploadedURL } from '@/stores/chatList';\r\n\r\ntype UserMessageProps = {\r\n  message: string;\r\n  timestamp?: string;\r\n  uploadedFiles?: UploadedFile[];\r\n  uploadedURLs?: UploadedURL[];\r\n  selectedLanguage?: string;\r\n};\r\n\r\nconst UserMessage: React.FC<UserMessageProps> = ({\r\n  message,\r\n  timestamp,\r\n  uploadedFiles,\r\n  uploadedURLs,\r\n  selectedLanguage = \"English\"\r\n}) => {\r\n  const formattedTime = timestamp\r\n    ? new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    : 'just now';\r\n\r\n  return (\r\n    <div className=\"flex justify-end items-start gap-1 sm:gap-3 w-full\">\r\n      <div className=\"flex flex-col justify-end items-end gap-3 flex-1\">\r\n        <p className=\"text-xs text-n100\">You, {formattedTime}</p>\r\n\r\n        {/* Display uploaded content above the message */}\r\n        <div className=\"w-full sm:max-w-[90%]\">\r\n          <UserUploadedContent\r\n            uploadedFiles={uploadedFiles}\r\n            uploadedURLs={uploadedURLs}\r\n            selectedLanguage={selectedLanguage}\r\n          />\r\n\r\n          <div className=\"bg-primaryColor text-white py-3 px-5 rounded-lg w-full\">\r\n            <div className=\"whitespace-pre-wrap text-sm\">\r\n              <ReactMarkdown>{message}</ReactMarkdown>\r\n            </div>\r\n          </div>\r\n        </div>\r\n{/* \r\n        <div className=\"flex justify-end items-center gap-2 cursor-pointer\">\r\n          <PiThumbsUp />\r\n          <PiThumbsDown />\r\n          <PiCopy />\r\n          <PiArrowsCounterClockwise />\r\n          <PiShareFat />\r\n        </div> */}\r\n      </div>\r\n      {/* <div className=\"size-7 sm:size-9 rounded-full bg-primaryColor flex justify-center items-center text-white\">\r\n        U\r\n      </div> */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserMessage;"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAK5C;AADA;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,SAAS,EACT,aAAa,EACb,YAAY,EACZ,mBAAmB,SAAS,EAC7B;IACC,MAAM,gBAAgB,YAClB,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;QAAE,MAAM;QAAW,QAAQ;IAAU,KAChF;IAEJ,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;;wBAAoB;wBAAM;;;;;;;8BAGvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oJAAA,CAAA,UAAmB;4BAClB,eAAe;4BACf,cAAc;4BACd,kBAAkB;;;;;;sCAGpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B;uCAEe"}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/UploadDropdown.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PiFilePdf, PiYoutubeLogo, PiGlobe, PiMusicNote } from 'react-icons/pi';\r\nimport { UploadType } from './ChatInputUpload';\r\n\r\ninterface UploadDropdownProps {\r\n  onSelect: (type: UploadType) => void;\r\n  selectedLanguage: string;\r\n}\r\n\r\ninterface UploadOption {\r\n  type: UploadType;\r\n  icon: React.ReactNode;\r\n  label: {\r\n    English: string;\r\n    Tamil: string;\r\n    Telugu: string;\r\n    Kannada: string;\r\n  };\r\n  description: {\r\n    English: string;\r\n    Tamil: string;\r\n    Telugu: string;\r\n    Kannada: string;\r\n  };\r\n}\r\n\r\nconst uploadOptions: UploadOption[] = [\r\n  {\r\n    type: 'pdf',\r\n    icon: <PiFilePdf className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'PDF/Document',\r\n      Tamil: 'PDF/ஆவணம்',\r\n      Telugu: 'PDF/పత్రం',\r\n      Kannada: 'PDF/ದಾಖಲೆ'\r\n    },\r\n    description: {\r\n      English: 'Upload PDF or document files',\r\n      Tamil: 'PDF அல்லது ஆவண கோப்புகளை பதிவேற்றவும்',\r\n      Telugu: 'PDF లేదా పత్రం ఫైల్‌లను అప్‌లోడ్ చేయండి',\r\n      Kannada: 'PDF ಅಥವಾ ದಾಖಲೆ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'youtube',\r\n    icon: <PiYoutubeLogo className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'YouTube URL',\r\n      Tamil: 'YouTube URL',\r\n      Telugu: 'YouTube URL',\r\n      Kannada: 'YouTube URL'\r\n    },\r\n    description: {\r\n      English: 'Add YouTube video link',\r\n      Tamil: 'YouTube வீடியோ இணைப்பைச் சேர்க்கவும்',\r\n      Telugu: 'YouTube వీడియో లింక్ జోడించండి',\r\n      Kannada: 'YouTube ವೀಡಿಯೊ ಲಿಂಕ್ ಸೇರಿಸಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'article',\r\n    icon: <PiGlobe className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'Article URL',\r\n      Tamil: 'கட்டுரை URL',\r\n      Telugu: 'వ్యాసం URL',\r\n      Kannada: 'ಲೇಖನ URL'\r\n    },\r\n    description: {\r\n      English: 'Add article or webpage link',\r\n      Tamil: 'கட்டுரை அல்லது வலைப்பக்க இணைப்பைச் சேர்க்கவும்',\r\n      Telugu: 'వ్యాసం లేదా వెబ్‌పేజీ లింక్ జోడించండి',\r\n      Kannada: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ ಲಿಂಕ್ ಸೇರಿಸಿ'\r\n    }\r\n  },\r\n  {\r\n    type: 'mp3',\r\n    icon: <PiMusicNote className=\"w-5 h-5\" />,\r\n    label: {\r\n      English: 'MP3 Audio',\r\n      Tamil: 'MP3 ஆடியோ',\r\n      Telugu: 'MP3 ఆడియో',\r\n      Kannada: 'MP3 ಆಡಿಯೊ'\r\n    },\r\n    description: {\r\n      English: 'Upload audio files',\r\n      Tamil: 'ஆடியோ கோப்புகளை பதிவேற்றவும்',\r\n      Telugu: 'ఆడియో ఫైల్‌లను అప్‌లోడ్ చేయండి',\r\n      Kannada: 'ಆಡಿಯೊ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'\r\n    }\r\n  }\r\n];\r\n\r\nconst UploadDropdown: React.FC<UploadDropdownProps> = ({ onSelect, selectedLanguage }) => {\r\n  const getLanguageKey = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'Tamil';\r\n      case 'Telugu':\r\n        return 'Telugu';\r\n      case 'Kannada':\r\n        return 'Kannada';\r\n      default:\r\n        return 'English';\r\n    }\r\n  };\r\n\r\n  const getHoverColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'hover:bg-purple-50 hover:border-purple-200';\r\n      case 'Telugu':\r\n        return 'hover:bg-green-50 hover:border-green-200';\r\n      case 'Kannada':\r\n        return 'hover:bg-orange-50 hover:border-orange-200';\r\n      default:\r\n        return 'hover:bg-blue-50 hover:border-blue-200';\r\n    }\r\n  };\r\n\r\n  const getIconColor = (index: number) => {\r\n    const colors = {\r\n      Tamil: ['text-purple-600', 'text-purple-500', 'text-purple-700', 'text-purple-400'],\r\n      Telugu: ['text-green-600', 'text-green-500', 'text-green-700', 'text-green-400'],\r\n      Kannada: ['text-orange-600', 'text-orange-500', 'text-orange-700', 'text-orange-400'],\r\n      English: ['text-blue-600', 'text-blue-500', 'text-blue-700', 'text-blue-400']\r\n    };\r\n\r\n    const languageColors = colors[selectedLanguage as keyof typeof colors] || colors.English;\r\n    return languageColors[index % languageColors.length];\r\n  };\r\n\r\n  const languageKey = getLanguageKey();\r\n\r\n  return (\r\n    <div className=\"absolute bottom-full right-0 mb-2 w-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn\">\r\n      <div className=\"p-2\">\r\n        {uploadOptions.map((option, index) => (\r\n          <button\r\n            key={option.type}\r\n            onClick={() => onSelect(option.type)}\r\n            className={`w-full flex items-start gap-3 p-3 rounded-lg border border-transparent transition-all ${getHoverColor()}`}\r\n          >\r\n            <div className={`flex-shrink-0 ${getIconColor(index)}`}>\r\n              {option.icon}\r\n            </div>\r\n            <div className=\"flex-grow text-left\">\r\n              <div className=\"font-medium text-sm text-gray-900 dark:text-gray-100\">\r\n                {option.label[languageKey as keyof typeof option.label]}\r\n              </div>\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-0.5\">\r\n                {option.description[languageKey as keyof typeof option.description]}\r\n              </div>\r\n            </div>\r\n          </button>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadDropdown;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAyBA,MAAM,gBAAgC;IACpC;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;IACA;QACE,MAAM;QACN,oBAAM,8OAAC,8IAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA,aAAa;YACX,SAAS;YACT,OAAO;YACP,QAAQ;YACR,SAAS;QACX;IACF;CACD;AAED,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE;IACnF,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,OAAO;gBAAC;gBAAmB;gBAAmB;gBAAmB;aAAkB;YACnF,QAAQ;gBAAC;gBAAkB;gBAAkB;gBAAkB;aAAiB;YAChF,SAAS;gBAAC;gBAAmB;gBAAmB;gBAAmB;aAAkB;YACrF,SAAS;gBAAC;gBAAiB;gBAAiB;gBAAiB;aAAgB;QAC/E;QAEA,MAAM,iBAAiB,MAAM,CAAC,iBAAwC,IAAI,OAAO,OAAO;QACxF,OAAO,cAAc,CAAC,QAAQ,eAAe,MAAM,CAAC;IACtD;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC;oBAEC,SAAS,IAAM,SAAS,OAAO,IAAI;oBACnC,WAAW,CAAC,sFAAsF,EAAE,iBAAiB;;sCAErH,8OAAC;4BAAI,WAAW,CAAC,cAAc,EAAE,aAAa,QAAQ;sCACnD,OAAO,IAAI;;;;;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK,CAAC,YAAyC;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;8CACZ,OAAO,WAAW,CAAC,YAA+C;;;;;;;;;;;;;mBAZlE,OAAO,IAAI;;;;;;;;;;;;;;;AAoB5B;uCAEe"}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/FileDropZone.tsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\r\nimport { PiCloudArrowUp, PiFile, PiX, PiCheck } from 'react-icons/pi';\r\n\r\ninterface FileDropZoneProps {\r\n  acceptedTypes: string;\r\n  onFilesSelected: (files: File[]) => void;\r\n  maxFiles?: number;\r\n  maxFileSize?: number; // in MB\r\n  selectedLanguage: string;\r\n}\r\n\r\ninterface FileWithStatus {\r\n  file: File;\r\n  status: 'selected' | 'uploading' | 'success' | 'error';\r\n  error?: string | null;\r\n}\r\n\r\nconst FileDropZone: React.FC<FileDropZoneProps> = ({\r\n  acceptedTypes,\r\n  onFilesSelected,\r\n  maxFiles = 1,\r\n  maxFileSize = 50,\r\n  selectedLanguage\r\n}) => {\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [files, setFiles] = useState<FileWithStatus[]>([]);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const getLanguageText = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return {\r\n          dragText: 'கோப்புகளை இங்கே இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',\r\n          browseText: 'உலாவு',\r\n          maxSizeText: `அதிகபட்சம் ${maxFileSize}MB`,\r\n          removeText: 'அகற்று',\r\n          selectedText: 'தேர்ந்தெடுக்கப்பட்டது'\r\n        };\r\n      case 'Telugu':\r\n        return {\r\n          dragText: 'ఫైల్‌లను ఇక్కడ లాగండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',\r\n          browseText: 'బ్రౌజ్',\r\n          maxSizeText: `గరిష్టంగా ${maxFileSize}MB`,\r\n          removeText: 'తొలగించు',\r\n          selectedText: 'ఎంచుకోబడింది'\r\n        };\r\n      case 'Kannada':\r\n        return {\r\n          dragText: 'ಫೈಲ್‌ಗಳನ್ನು ಇಲ್ಲಿ ಎಳೆಯಿರಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',\r\n          browseText: 'ಬ್ರೌಸ್',\r\n          maxSizeText: `ಗರಿಷ್ಠ ${maxFileSize}MB`,\r\n          removeText: 'ತೆಗೆದುಹಾಕಿ',\r\n          selectedText: 'ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ'\r\n        };\r\n      default:\r\n        return {\r\n          dragText: 'Drag files here or click to browse',\r\n          browseText: 'Browse',\r\n          maxSizeText: `Max ${maxFileSize}MB`,\r\n          removeText: 'Remove',\r\n          selectedText: 'Selected'\r\n        };\r\n    }\r\n  };\r\n\r\n  const getBorderColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return isDragging ? 'border-purple-400 bg-purple-50' : 'border-purple-300';\r\n      case 'Telugu':\r\n        return isDragging ? 'border-green-400 bg-green-50' : 'border-green-300';\r\n      case 'Kannada':\r\n        return isDragging ? 'border-orange-400 bg-orange-50' : 'border-orange-300';\r\n      default:\r\n        return isDragging ? 'border-blue-400 bg-blue-50' : 'border-blue-300';\r\n    }\r\n  };\r\n\r\n  const getIconColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'text-purple-500';\r\n      case 'Telugu':\r\n        return 'text-green-500';\r\n      case 'Kannada':\r\n        return 'text-orange-500';\r\n      default:\r\n        return 'text-blue-500';\r\n    }\r\n  };\r\n\r\n  const validateFile = (file: File): string | null => {\r\n    // Check file size\r\n    if (file.size > maxFileSize * 1024 * 1024) {\r\n      return `File size exceeds ${maxFileSize}MB limit`;\r\n    }\r\n\r\n    // Check file type\r\n    const fileName = file.name.toLowerCase();\r\n    const acceptedExtensions = acceptedTypes.split(',').map(type => type.trim().toLowerCase());\r\n\r\n    // More comprehensive file type validation\r\n    const isValidType = acceptedExtensions.some(acceptedType => {\r\n      if (acceptedType.startsWith('.')) {\r\n        return fileName.endsWith(acceptedType);\r\n      }\r\n      return file.type === acceptedType;\r\n    });\r\n\r\n    if (!isValidType) {\r\n      // Provide more specific error messages based on accepted types\r\n      let typeDescription = '';\r\n      if (acceptedTypes.includes('.pdf')) {\r\n        typeDescription = 'PDF, DOC, DOCX, TXT, or RTF files';\r\n      } else if (acceptedTypes.includes('.mp3')) {\r\n        typeDescription = 'MP3, WAV, M4A, or FLAC audio files';\r\n      } else {\r\n        typeDescription = acceptedTypes;\r\n      }\r\n\r\n      return `File type not supported. Please upload ${typeDescription}`;\r\n    }\r\n\r\n    return null;\r\n  };\r\n\r\n  const handleFiles = useCallback((fileList: FileList) => {\r\n    const newFiles: FileWithStatus[] = [];\r\n    const validFiles: File[] = [];\r\n\r\n    Array.from(fileList).slice(0, maxFiles).forEach(file => {\r\n      const error = validateFile(file);\r\n      const fileWithStatus: FileWithStatus = {\r\n        file,\r\n        status: error ? 'error' : 'selected',\r\n        error\r\n      };\r\n      \r\n      newFiles.push(fileWithStatus);\r\n      \r\n      if (!error) {\r\n        validFiles.push(file);\r\n      }\r\n    });\r\n\r\n    setFiles(newFiles);\r\n    \r\n    if (validFiles.length > 0) {\r\n      onFilesSelected(validFiles);\r\n    }\r\n  }, [maxFiles, onFilesSelected]);\r\n\r\n  const handleDragOver = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(true);\r\n  }, []);\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  }, []);\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n    \r\n    if (e.dataTransfer.files) {\r\n      handleFiles(e.dataTransfer.files);\r\n    }\r\n  }, [handleFiles]);\r\n\r\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files) {\r\n      handleFiles(e.target.files);\r\n    }\r\n  }, [handleFiles]);\r\n\r\n  const handleRemoveFile = (index: number) => {\r\n    const newFiles = files.filter((_, i) => i !== index);\r\n    setFiles(newFiles);\r\n    \r\n    const validFiles = newFiles\r\n      .filter(f => f.status !== 'error')\r\n      .map(f => f.file);\r\n    \r\n    onFilesSelected(validFiles);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const text = getLanguageText();\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Drop zone */}\r\n      <div\r\n        onClick={handleClick}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        className={`\r\n          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200\r\n          ${getBorderColor()}\r\n          hover:bg-gray-50 dark:hover:bg-gray-700/50\r\n          min-h-[160px] flex items-center justify-center\r\n        `}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={acceptedTypes}\r\n          multiple={maxFiles > 1}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n        />\r\n\r\n        <div className=\"flex flex-col items-center justify-center gap-3\">\r\n          <div className=\"p-3 rounded-full bg-gray-100 dark:bg-gray-800\">\r\n            <PiCloudArrowUp className={`w-10 h-10 ${getIconColor()}`} />\r\n          </div>\r\n          <div className=\"space-y-1\">\r\n            <p className=\"text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed\">\r\n              {text.dragText}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {text.maxSizeText}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Selected files */}\r\n      {files.length > 0 && (\r\n        <div className=\"space-y-3\">\r\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\r\n            <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n              {text.selectedText} ({files.length})\r\n            </h4>\r\n            <div className=\"space-y-2\">\r\n              {files.map((fileWithStatus, index) => (\r\n                <div\r\n                  key={index}\r\n                  className={`\r\n                    flex items-center gap-4 p-4 rounded-lg border transition-all duration-200\r\n                    ${fileWithStatus.status === 'error'\r\n                      ? 'border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800'\r\n                      : 'border-gray-200 bg-gray-50 dark:bg-gray-700/50 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className=\"flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600\">\r\n                    {fileWithStatus.status === 'error' ? (\r\n                      <PiX className=\"w-5 h-5 text-red-500\" />\r\n                    ) : fileWithStatus.status === 'success' ? (\r\n                      <PiCheck className=\"w-5 h-5 text-green-500\" />\r\n                    ) : (\r\n                      <PiFile className={`w-5 h-5 ${getIconColor()}`} />\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex-grow min-w-0 space-y-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                      {fileWithStatus.file.name}\r\n                    </p>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {(fileWithStatus.file.size / 1024 / 1024).toFixed(2)} MB\r\n                      </p>\r\n                      {fileWithStatus.status === 'selected' && (\r\n                        <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\">\r\n                          Ready\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                    {fileWithStatus.error && (\r\n                      <p className=\"text-xs text-red-600 dark:text-red-400 mt-1 leading-relaxed\">\r\n                        {fileWithStatus.error}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n\r\n                  <button\r\n                    onClick={() => handleRemoveFile(index)}\r\n                    className=\"flex-shrink-0 p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200\"\r\n                    title={text.removeText}\r\n                  >\r\n                    <PiX className=\"w-4 h-4\" />\r\n                  </button>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileDropZone;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,aAAa,EACb,eAAe,EACf,WAAW,CAAC,EACZ,cAAc,EAAE,EAChB,gBAAgB,EACjB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACvD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;oBAC1C,YAAY;oBACZ,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC;oBACzC,YAAY;oBACZ,cAAc;gBAChB;YACF,KAAK;gBACH,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;oBACtC,YAAY;oBACZ,cAAc;gBAChB;YACF;gBACE,OAAO;oBACL,UAAU;oBACV,YAAY;oBACZ,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC;oBACnC,YAAY;oBACZ,cAAc;gBAChB;QACJ;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,aAAa,mCAAmC;YACzD,KAAK;gBACH,OAAO,aAAa,iCAAiC;YACvD,KAAK;gBACH,OAAO,aAAa,mCAAmC;YACzD;gBACE,OAAO,aAAa,+BAA+B;QACvD;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,kBAAkB;QAClB,IAAI,KAAK,IAAI,GAAG,cAAc,OAAO,MAAM;YACzC,OAAO,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC;QACnD;QAEA,kBAAkB;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,qBAAqB,cAAc,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,WAAW;QAEvF,0CAA0C;QAC1C,MAAM,cAAc,mBAAmB,IAAI,CAAC,CAAA;YAC1C,IAAI,aAAa,UAAU,CAAC,MAAM;gBAChC,OAAO,SAAS,QAAQ,CAAC;YAC3B;YACA,OAAO,KAAK,IAAI,KAAK;QACvB;QAEA,IAAI,CAAC,aAAa;YAChB,+DAA+D;YAC/D,IAAI,kBAAkB;YACtB,IAAI,cAAc,QAAQ,CAAC,SAAS;gBAClC,kBAAkB;YACpB,OAAO,IAAI,cAAc,QAAQ,CAAC,SAAS;gBACzC,kBAAkB;YACpB,OAAO;gBACL,kBAAkB;YACpB;YAEA,OAAO,CAAC,uCAAuC,EAAE,iBAAiB;QACpE;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,WAA6B,EAAE;QACrC,MAAM,aAAqB,EAAE;QAE7B,MAAM,IAAI,CAAC,UAAU,KAAK,CAAC,GAAG,UAAU,OAAO,CAAC,CAAA;YAC9C,MAAM,QAAQ,aAAa;YAC3B,MAAM,iBAAiC;gBACrC;gBACA,QAAQ,QAAQ,UAAU;gBAC1B;YACF;YAEA,SAAS,IAAI,CAAC;YAEd,IAAI,CAAC,OAAO;gBACV,WAAW,IAAI,CAAC;YAClB;QACF;QAEA,SAAS;QAET,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAU;KAAgB;IAE9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE;YACxB,YAAY,EAAE,YAAY,CAAC,KAAK;QAClC;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,YAAY,EAAE,MAAM,CAAC,KAAK;QAC5B;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC9C,SAAS;QAET,MAAM,aAAa,SAChB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SACzB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;QAElB,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS;gBACT,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,WAAW,CAAC;;UAEV,EAAE,iBAAiB;;;QAGrB,CAAC;;kCAED,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU,WAAW;wBACrB,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,WAAW,CAAC,UAAU,EAAE,gBAAgB;;;;;;;;;;;0CAE1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,KAAK,QAAQ;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAOxB,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,KAAK,YAAY;gCAAC;gCAAG,MAAM,MAAM;gCAAC;;;;;;;sCAErC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,gBAAgB,sBAC1B,8OAAC;oCAEC,WAAW,CAAC;;oBAEV,EAAE,eAAe,MAAM,KAAK,UACxB,oEACA,+GACH;kBACH,CAAC;;sDAED,8OAAC;4CAAI,WAAU;sDACZ,eAAe,MAAM,KAAK,wBACzB,8OAAC,8IAAA,CAAA,MAAG;gDAAC,WAAU;;;;;uDACb,eAAe,MAAM,KAAK,0BAC5B,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAW,CAAC,QAAQ,EAAE,gBAAgB;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,eAAe,IAAI,CAAC,IAAI;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;gEACV,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;wDAEtD,eAAe,MAAM,KAAK,4BACzB,8OAAC;4DAAK,WAAU;sEAA6I;;;;;;;;;;;;gDAKhK,eAAe,KAAK,kBACnB,8OAAC;oDAAE,WAAU;8DACV,eAAe,KAAK;;;;;;;;;;;;sDAK3B,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;4CACV,OAAO,KAAK,UAAU;sDAEtB,cAAA,8OAAC,8IAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;mCA7CZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDvB;uCAEe"}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/URLInput.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>rrowRight } from 'react-icons/pi';\r\n\r\ninterface URLInputProps {\r\n  type: 'youtube' | 'article';\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  onSubmit: (url: string) => void;\r\n  selectedLanguage: string;\r\n}\r\n\r\nconst URLInput: React.FC<URLInputProps> = ({\r\n  type,\r\n  value,\r\n  onChange,\r\n  onSubmit,\r\n  selectedLanguage\r\n}) => {\r\n  const [isValid, setIsValid] = useState<boolean | null>(null);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  const getLanguageText = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return {\r\n          youtubePlaceholder: 'YouTube வீடியோ URL ஐ உள்ளிடவும்...',\r\n          articlePlaceholder: 'கட்டுரை அல்லது வலைப்பக்க URL ஐ உள்ளிடவும்...',\r\n          addButton: 'சேர்',\r\n          invalidUrl: 'தவறான URL வடிவம்',\r\n          invalidYoutube: 'தவறான YouTube URL',\r\n          validUrl: 'சரியான URL'\r\n        };\r\n      case 'Telugu':\r\n        return {\r\n          youtubePlaceholder: 'YouTube వీడియో URL ని ఎంటర్ చేయండి...',\r\n          articlePlaceholder: 'వ్యాసం లేదా వెబ్‌పేజీ URL ని ఎంటర్ చేయండి...',\r\n          addButton: 'జోడించు',\r\n          invalidUrl: 'చెల్లని URL ఫార్మాట్',\r\n          invalidYoutube: 'చెల్లని YouTube URL',\r\n          validUrl: 'చెల్లుబాటు అయ్యే URL'\r\n        };\r\n      case 'Kannada':\r\n        return {\r\n          youtubePlaceholder: 'YouTube ವೀಡಿಯೊ URL ಅನ್ನು ನಮೂದಿಸಿ...',\r\n          articlePlaceholder: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ URL ಅನ್ನು ನಮೂದಿಸಿ...',\r\n          addButton: 'ಸೇರಿಸಿ',\r\n          invalidUrl: 'ಅಮಾನ್ಯ URL ಸ್ವರೂಪ',\r\n          invalidYoutube: 'ಅಮಾನ್ಯ YouTube URL',\r\n          validUrl: 'ಮಾನ್ಯ URL'\r\n        };\r\n      default:\r\n        return {\r\n          youtubePlaceholder: 'Enter YouTube video URL...',\r\n          articlePlaceholder: 'Enter article or webpage URL...',\r\n          addButton: 'Add',\r\n          invalidUrl: 'Invalid URL format',\r\n          invalidYoutube: 'Invalid YouTube URL',\r\n          validUrl: 'Valid URL'\r\n        };\r\n    }\r\n  };\r\n\r\n  const validateURL = (url: string): { isValid: boolean; error: string } => {\r\n    if (!url.trim()) {\r\n      return { isValid: false, error: '' };\r\n    }\r\n\r\n    try {\r\n      const urlObj = new URL(url);\r\n      \r\n      if (type === 'youtube') {\r\n        const isYouTube = \r\n          urlObj.hostname === 'www.youtube.com' ||\r\n          urlObj.hostname === 'youtube.com' ||\r\n          urlObj.hostname === 'youtu.be' ||\r\n          urlObj.hostname === 'm.youtube.com';\r\n        \r\n        if (!isYouTube) {\r\n          return { isValid: false, error: getLanguageText().invalidYoutube };\r\n        }\r\n      }\r\n      \r\n      return { isValid: true, error: '' };\r\n    } catch {\r\n      return { isValid: false, error: getLanguageText().invalidUrl };\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newValue = e.target.value;\r\n    onChange(newValue);\r\n    \r\n    const validation = validateURL(newValue);\r\n    setIsValid(validation.isValid);\r\n    setError(validation.error);\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    if (isValid && value.trim()) {\r\n      onSubmit(value.trim());\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && isValid && value.trim()) {\r\n      e.preventDefault();\r\n      handleSubmit();\r\n    }\r\n  };\r\n\r\n  const getBorderColor = () => {\r\n    if (isValid === null) {\r\n      switch (selectedLanguage) {\r\n        case 'Tamil':\r\n          return 'border-purple-300 focus:border-purple-500';\r\n        case 'Telugu':\r\n          return 'border-green-300 focus:border-green-500';\r\n        case 'Kannada':\r\n          return 'border-orange-300 focus:border-orange-500';\r\n        default:\r\n          return 'border-blue-300 focus:border-blue-500';\r\n      }\r\n    }\r\n    \r\n    return isValid \r\n      ? 'border-green-300 focus:border-green-500' \r\n      : 'border-red-300 focus:border-red-500';\r\n  };\r\n\r\n  const getButtonColor = () => {\r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return 'bg-purple-500 hover:bg-purple-600 focus:ring-purple-500';\r\n      case 'Telugu':\r\n        return 'bg-green-500 hover:bg-green-600 focus:ring-green-500';\r\n      case 'Kannada':\r\n        return 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500';\r\n      default:\r\n        return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500';\r\n    }\r\n  };\r\n\r\n  const text = getLanguageText();\r\n\r\n  return (\r\n    <div className=\"space-y-2\">\r\n      <div className=\"relative\">\r\n        <input\r\n          type=\"url\"\r\n          value={value}\r\n          onChange={handleInputChange}\r\n          onKeyPress={handleKeyPress}\r\n          placeholder={type === 'youtube' ? text.youtubePlaceholder : text.articlePlaceholder}\r\n          className={`\r\n            w-full px-3 py-2 pr-10 border rounded-lg\r\n            focus:outline-none focus:ring-2 focus:ring-opacity-50\r\n            ${getBorderColor()}\r\n            dark:bg-gray-700 dark:text-white dark:placeholder-gray-400\r\n          `}\r\n        />\r\n\r\n        {/* Validation icon */}\r\n        <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n          {isValid === true && (\r\n            <PiCheck className=\"w-5 h-5 text-green-500\" />\r\n          )}\r\n          {isValid === false && value.trim() && (\r\n            <PiX className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <p className=\"text-xs text-red-500 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n\r\n      {/* Success message */}\r\n      {isValid && value.trim() && (\r\n        <p className=\"text-xs text-green-500 dark:text-green-400\">\r\n          {text.validUrl}\r\n        </p>\r\n      )}\r\n\r\n      {/* Add button */}\r\n      <button\r\n        type=\"button\"\r\n        onClick={handleSubmit}\r\n        disabled={!isValid || !value.trim()}\r\n        className={`\r\n          w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg\r\n          text-white font-medium text-sm transition-all\r\n          ${isValid && value.trim()\r\n            ? getButtonColor()\r\n            : 'bg-gray-400 cursor-not-allowed'\r\n          }\r\n          disabled:opacity-50\r\n        `}\r\n      >\r\n        {text.addButton}\r\n        <PiArrowRight className=\"w-4 h-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default URLInput;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,WAAoC,CAAC,EACzC,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,gBAAgB,EACjB;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF,KAAK;gBACH,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;YACF;gBACE,OAAO;oBACL,oBAAoB;oBACpB,oBAAoB;oBACpB,WAAW;oBACX,YAAY;oBACZ,gBAAgB;oBAChB,UAAU;gBACZ;QACJ;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAG;QACrC;QAEA,IAAI;YACF,MAAM,SAAS,IAAI,IAAI;YAEvB,IAAI,SAAS,WAAW;gBACtB,MAAM,YACJ,OAAO,QAAQ,KAAK,qBACpB,OAAO,QAAQ,KAAK,iBACpB,OAAO,QAAQ,KAAK,cACpB,OAAO,QAAQ,KAAK;gBAEtB,IAAI,CAAC,WAAW;oBACd,OAAO;wBAAE,SAAS;wBAAO,OAAO,kBAAkB,cAAc;oBAAC;gBACnE;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAG;QACpC,EAAE,OAAM;YACN,OAAO;gBAAE,SAAS;gBAAO,OAAO,kBAAkB,UAAU;YAAC;QAC/D;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QAET,MAAM,aAAa,YAAY;QAC/B,WAAW,WAAW,OAAO;QAC7B,SAAS,WAAW,KAAK;IAC3B;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,MAAM,IAAI,IAAI;YAC3B,SAAS,MAAM,IAAI;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,WAAW,MAAM,IAAI,IAAI;YAChD,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,YAAY,MAAM;YACpB,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,OAAO,UACH,4CACA;IACN;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,OAAO;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,YAAY;wBACZ,aAAa,SAAS,YAAY,KAAK,kBAAkB,GAAG,KAAK,kBAAkB;wBACnF,WAAW,CAAC;;;YAGV,EAAE,iBAAiB;;UAErB,CAAC;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,sBACX,8OAAC,8IAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAEpB,YAAY,SAAS,MAAM,IAAI,oBAC9B,8OAAC,8IAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAMpB,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAKJ,WAAW,MAAM,IAAI,oBACpB,8OAAC;gBAAE,WAAU;0BACV,KAAK,QAAQ;;;;;;0BAKlB,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU,CAAC,WAAW,CAAC,MAAM,IAAI;gBACjC,WAAW,CAAC;;;UAGV,EAAE,WAAW,MAAM,IAAI,KACnB,mBACA,iCACH;;QAEH,CAAC;;oBAEA,KAAK,SAAS;kCACf,8OAAC,8IAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;uCAEe"}}, {"offset": {"line": 1290, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/services/faissService.ts"], "sourcesContent": ["/**\n * FAISS Service - Handles all FAISS-related API calls\n * Replaces Pinecone-specific functionality with FAISS backend integration\n */\n\nexport interface EmbeddingModel {\n  name: string;\n  dimension: number;\n  description: string;\n}\n\nexport interface FaissCategory {\n  index_name: string;\n  email?: string;\n  embedding_model?: string;\n  embedding_dimension?: number;\n  created_at?: string;\n}\n\nexport interface FaissQueryResult {\n  rank: number;\n  score: string;\n  metadata: any;\n  text: string;\n}\n\nexport interface UploadProgress {\n  upload_id: string;\n  status: 'processing' | 'complete' | 'cancelled' | 'error';\n  total_rows: number;\n  processed_rows: number;\n  total_vectors: number;\n  index_name: string;\n  cancelled: boolean;\n  processing_time?: number;\n}\n\n// Base URL for FAISS backend\nconst FAISS_BASE_URL = process.env.NODE_ENV === 'development' \n  ? 'http://localhost:5010' \n  : 'http://localhost:5010';\n\n/**\n * Upload CSV file to FAISS backend\n */\nexport const uploadCSVToFaiss = async (\n  file: File,\n  indexName: string,\n  clientEmail?: string,\n  updateMode: 'update' | 'new' = 'update',\n  embedModel: string = 'all-MiniLM-L6-v2',\n  signal?: AbortSignal,\n  onProgress?: (progress: number) => void\n): Promise<any> => {\n  return new Promise((resolve, reject) => {\n    try {\n      // Validate file type\n      if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {\n        reject(new Error('Only CSV files are supported for CSV upload'));\n        return;\n      }\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('index_name', indexName);\n      formData.append('update_mode', updateMode);\n      formData.append('embed_model', embedModel);\n      \n      if (clientEmail) {\n        formData.append('client', clientEmail);\n      }\n\n      // Create XMLHttpRequest for progress tracking\n      const xhr = new XMLHttpRequest();\n\n      // Handle response\n      xhr.onload = () => {\n        if (xhr.status >= 200 && xhr.status < 300) {\n          try {\n            const response = JSON.parse(xhr.responseText);\n            resolve(response);\n          } catch (e) {\n            reject(new Error('Invalid JSON response from server'));\n          }\n        } else {\n          try {\n            const errorResponse = JSON.parse(xhr.responseText);\n            reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));\n          } catch (e) {\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\n          }\n        }\n      };\n\n      xhr.onerror = () => {\n        reject(new Error('Network error occurred during upload'));\n      };\n\n      xhr.onabort = () => {\n        reject(new Error('Upload was cancelled'));\n      };\n\n      // Track upload progress\n      if (onProgress) {\n        xhr.upload.onprogress = (event) => {\n          if (event.lengthComputable) {\n            const progress = Math.round((event.loaded / event.total) * 100);\n            onProgress(progress);\n          }\n        };\n      }\n\n      // Handle cancellation\n      if (signal) {\n        signal.addEventListener('abort', () => {\n          xhr.abort();\n        });\n      }\n\n      // Send request\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);\n      xhr.send(formData);\n\n    } catch (error) {\n      reject(error);\n    }\n  });\n};\n\n/**\n * Upload Excel file to FAISS backend\n */\nexport const uploadExcelToFaiss = async (\n  file: File,\n  indexName: string,\n  clientId: string,\n  updateMode: 'update' | 'new' = 'update',\n  embedModel: string = 'all-MiniLM-L6-v2',\n  signal?: AbortSignal,\n  onProgress?: (progress: number) => void\n): Promise<any> => {\n  return new Promise((resolve, reject) => {\n    try {\n      // Validate file type\n      const fileName = file.name.toLowerCase();\n      if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {\n        reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));\n        return;\n      }\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('index_name', indexName);\n      formData.append('client_id', clientId);\n      formData.append('update_mode', updateMode);\n      formData.append('embed_model', embedModel);\n\n      // Create XMLHttpRequest for progress tracking\n      const xhr = new XMLHttpRequest();\n\n      // Handle response\n      xhr.onload = () => {\n        if (xhr.status >= 200 && xhr.status < 300) {\n          try {\n            const response = JSON.parse(xhr.responseText);\n            resolve(response);\n          } catch (e) {\n            reject(new Error('Invalid JSON response from server'));\n          }\n        } else {\n          try {\n            const errorResponse = JSON.parse(xhr.responseText);\n            reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));\n          } catch (e) {\n            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));\n          }\n        }\n      };\n\n      xhr.onerror = () => {\n        reject(new Error('Network error occurred during upload'));\n      };\n\n      xhr.onabort = () => {\n        reject(new Error('Upload was cancelled'));\n      };\n\n      // Track upload progress\n      if (onProgress) {\n        xhr.upload.onprogress = (event) => {\n          if (event.lengthComputable) {\n            const progress = Math.round((event.loaded / event.total) * 100);\n            onProgress(progress);\n          }\n        };\n      }\n\n      // Handle cancellation\n      if (signal) {\n        signal.addEventListener('abort', () => {\n          xhr.abort();\n        });\n      }\n\n      // Send request\n      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);\n      xhr.send(formData);\n\n    } catch (error) {\n      reject(error);\n    }\n  });\n};\n\n/**\n * Upload file to FAISS backend (supports both CSV and Excel)\n */\nexport const uploadFileToFaiss = async (\n  file: File,\n  indexName: string,\n  clientEmail?: string,\n  updateMode: 'update' | 'new' = 'update',\n  embedModel: string = 'all-MiniLM-L6-v2',\n  signal?: AbortSignal,\n  onProgress?: (progress: number) => void\n): Promise<any> => {\n  const fileName = file.name.toLowerCase();\n  const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');\n\n  if (isExcel) {\n    // For Excel files, client_id is required\n    if (!clientEmail) {\n      throw new Error('Client email is required for Excel file uploads');\n    }\n    return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\n  } else {\n    // For CSV files, use the existing CSV upload function\n    return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);\n  }\n};\n\n/**\n * Get list of available embedding models\n */\nexport const getEmbeddingModels = async (): Promise<{ models: Record<string, EmbeddingModel>, default_model: string }> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);\n  if (!response.ok) {\n    throw new Error(`Failed to fetch embedding models: ${response.statusText}`);\n  }\n  return response.json();\n};\n\n/**\n * Get list of FAISS categories/indexes\n */\nexport const getFaissCategories = async (clientEmail?: string): Promise<FaissCategory[]> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\n  });\n  \n  if (!response.ok) {\n    throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);\n  }\n  \n  const data = await response.json();\n  return data.categories || [];\n};\n\n/**\n * Check if FAISS index exists\n */\nexport const checkFaissIndexExists = async (indexName: string, embedModel?: string): Promise<boolean> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      index_name: indexName,\n      embed_model: embedModel\n    })\n  });\n  \n  if (!response.ok) {\n    throw new Error(`Failed to check FAISS index: ${response.statusText}`);\n  }\n  \n  const data = await response.json();\n  return data.exists || false;\n};\n\n/**\n * Get current user's email from session storage\n */\nconst getCurrentUserEmail = (): string | null => {\n  try {\n    if (typeof window === 'undefined') return null;\n\n    // Try multiple sources for user email\n    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\n    if (directEmail) return directEmail;\n\n    // Try from user session data\n    const userSession = sessionStorage.getItem('resultUser');\n    if (userSession) {\n      const userData = JSON.parse(userSession);\n      return userData.email || userData.username || null;\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error getting current user email:', error);\n    return null;\n  }\n};\n\n/**\n * Query FAISS index with user access validation\n */\nexport const queryFaiss = async (\n  query: string,\n  indexName: string,\n  k: number = 5,\n  userEmail?: string\n): Promise<FaissQueryResult[]> => {\n  // Get user email if not provided\n  const emailToUse = userEmail || getCurrentUserEmail();\n\n  const requestBody: any = {\n    query,\n    index_name: indexName,\n    k\n  };\n\n  // Add user email for access validation if available\n  if (emailToUse) {\n    requestBody.user_email = emailToUse;\n  }\n\n  const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(requestBody)\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to query FAISS index: ${response.statusText}`);\n  }\n\n  const data = await response.json();\n  return data.results || [];\n};\n\n/**\n * Get upload status\n */\nexport const getUploadStatus = async (uploadId: string): Promise<UploadProgress> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({ upload_id: uploadId })\n  });\n  \n  if (!response.ok) {\n    throw new Error(`Failed to get upload status: ${response.statusText}`);\n  }\n  \n  return response.json();\n};\n\n/**\n * Cancel upload\n */\nexport const cancelUpload = async (uploadId: string): Promise<void> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({ upload_id: uploadId })\n  });\n  \n  if (!response.ok) {\n    throw new Error(`Failed to cancel upload: ${response.statusText}`);\n  }\n};\n\n/**\n * Get CSV data from database\n */\nexport const getCSVData = async (\n  indexName: string, \n  limit: number = 100, \n  offset: number = 0\n): Promise<any> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      index_name: indexName,\n      limit,\n      offset\n    })\n  });\n  \n  if (!response.ok) {\n    throw new Error(`Failed to get CSV data: ${response.statusText}`);\n  }\n  \n  return response.json();\n};\n\n/**\n * List CSV files in database\n */\nexport const listCSVFiles = async (clientEmail?: string): Promise<any[]> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to list CSV files: ${response.statusText}`);\n  }\n\n  const data = await response.json();\n  return data.files || [];\n};\n\n/**\n * List Excel files in database\n */\nexport const listExcelFiles = async (clientId?: string): Promise<any[]> => {\n  const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(clientId ? { client_id: clientId } : {})\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to list Excel files: ${response.statusText}`);\n  }\n\n  const data = await response.json();\n  return data.excel_files || [];\n};\n\n// Utility functions for localStorage management\nexport const getFaissConfig = () => {\n  if (typeof window === 'undefined') return null;\n  \n  return {\n    indexName: localStorage.getItem('faiss_index_name'),\n    embedModel: localStorage.getItem('faiss_embed_model') || 'all-MiniLM-L6-v2',\n    clientEmail: localStorage.getItem('faiss_client_email')\n  };\n};\n\nexport const setFaissConfig = (config: {\n  indexName?: string;\n  embedModel?: string;\n  clientEmail?: string;\n}) => {\n  if (typeof window === 'undefined') return;\n  \n  if (config.indexName) {\n    localStorage.setItem('faiss_index_name', config.indexName);\n  }\n  if (config.embedModel) {\n    localStorage.setItem('faiss_embed_model', config.embedModel);\n  }\n  if (config.clientEmail) {\n    localStorage.setItem('faiss_client_email', config.clientEmail);\n  }\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;AAkCD,6BAA6B;AAC7B,MAAM,iBAAiB,uCACnB;AAMG,MAAM,mBAAmB,OAC9B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,cAAc,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACzE,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACjF,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,eAAe,CAAC,EAAE;YACrD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,qBAAqB,OAChC,MACA,WACA,UACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,SAAS,QAAQ,CAAC,YAAY,CAAC,SAAS,QAAQ,CAAC,SAAS;gBAC7D,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,kBAAkB;YAClB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,eAAe;YAC/B,SAAS,MAAM,CAAC,eAAe;YAE/B,8CAA8C;YAC9C,MAAM,MAAM,IAAI;YAEhB,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,YAAY;wBACjD,OAAO,IAAI,MAAM,cAAc,KAAK,EAAE,WAAW,cAAc,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBACnH,EAAE,OAAO,GAAG;wBACV,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;oBAC1D;gBACF;YACF;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,sBAAsB;YACtB,IAAI,QAAQ;gBACV,OAAO,gBAAgB,CAAC,SAAS;oBAC/B,IAAI,KAAK;gBACX;YACF;YAEA,eAAe;YACf,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,iBAAiB,CAAC,EAAE;YACvD,IAAI,IAAI,CAAC;QAEX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAKO,MAAM,oBAAoB,OAC/B,MACA,WACA,aACA,aAA+B,QAAQ,EACvC,aAAqB,kBAAkB,EACvC,QACA;IAEA,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;IACtC,MAAM,UAAU,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC;IAEhE,IAAI,SAAS;QACX,yCAAyC;QACzC,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,mBAAmB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IAC1F,OAAO;QACL,sDAAsD;QACtD,OAAO,iBAAiB,MAAM,WAAW,aAAa,YAAY,YAAY,QAAQ;IACxF;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,0BAA0B,CAAC;IAC1E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IACA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,oBAAoB,CAAC,EAAE;QACpE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,UAAU,IAAI,EAAE;AAC9B;AAKO,MAAM,wBAAwB,OAAO,WAAmB;IAC7D,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ,aAAa;QACf;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,MAAM,IAAI;AACxB;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAC1B,IAAI;QACF,wCAAmC,OAAO;;QAE1C,sCAAsC;QACtC,MAAM;QAGN,6BAA6B;QAC7B,MAAM;IAOR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAKO,MAAM,aAAa,OACxB,OACA,WACA,IAAY,CAAC,EACb;IAEA,iCAAiC;IACjC,MAAM,aAAa,aAAa;IAEhC,MAAM,cAAmB;QACvB;QACA,YAAY;QACZ;IACF;IAEA,oDAAoD;IACpD,IAAI,YAAY;QACd,YAAY,UAAU,GAAG;IAC3B;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,gBAAgB,CAAC,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,OAAO,IAAI,EAAE;AAC3B;AAKO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,UAAU,EAAE;IACvE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,kBAAkB,CAAC,EAAE;QAClE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE,WAAW;QAAS;IAC7C;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;IACnE;AACF;AAKO,MAAM,aAAa,OACxB,WACA,QAAgB,GAAG,EACnB,SAAiB,CAAC;IAElB,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,iBAAiB,CAAC,EAAE;QACjE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB,YAAY;YACZ;YACA;QACF;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,mBAAmB,CAAC,EAAE;QACnE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,cAAc;YAAE,cAAc;QAAY,IAAI,CAAC;IACtE;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;IACpE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,KAAK,IAAI,EAAE;AACzB;AAKO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,qBAAqB,CAAC,EAAE;QACrE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC,WAAW;YAAE,WAAW;QAAS,IAAI,CAAC;IAC7D;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;IACtE;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,OAAO,KAAK,WAAW,IAAI,EAAE;AAC/B;AAGO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;AAO5C;AAEO,MAAM,iBAAiB,CAAC;IAK7B,wCAAmC;;AAWrC"}}, {"offset": {"line": 1628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1706, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/services/uploadService.ts"], "sourcesContent": ["/**\r\n * Upload Service for ChatInputUpload component\r\n * Handles file and URL uploads to the Python FAISS backend\r\n * Enhanced with cache memory functionality for faster responses\r\n */\r\n\r\nimport axios from 'axios';\r\nimport { getFaissConfig } from './faissService';\r\n\r\n// Cache configuration\r\nconst CACHE_CONFIG = {\r\n  UPLOAD_CACHE_KEY: 'upload_cache',\r\n  QUERY_CACHE_KEY: 'query_cache',\r\n  CACHE_EXPIRY_MS: 24 * 60 * 60 * 1000, // 24 hours\r\n  MAX_CACHE_SIZE: 100, // Maximum number of cached items\r\n  ENABLE_CACHE: true\r\n};\r\n\r\n// Cache interface\r\ninterface CacheItem<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  key: string;\r\n  expiresAt: number;\r\n}\r\n\r\ninterface UploadCacheData {\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n  upload_id?: string;\r\n  index_name?: string;\r\n  filename?: string;\r\n  fileHash?: string;\r\n  fileSize?: number;\r\n  processingTime?: number;\r\n}\r\n\r\n// Cache utility class\r\nclass CacheManager {\r\n  private static instance: CacheManager;\r\n\r\n  private constructor() {}\r\n\r\n  static getInstance(): CacheManager {\r\n    if (!CacheManager.instance) {\r\n      CacheManager.instance = new CacheManager();\r\n    }\r\n    return CacheManager.instance;\r\n  }\r\n\r\n  // Generate cache key for uploads\r\n  generateUploadCacheKey(type: string, data: File | string, options: any = {}): string {\r\n    if (data instanceof File) {\r\n      return `upload_${type}_${data.name}_${data.size}_${data.lastModified}_${options.index_name || 'default'}`;\r\n    } else {\r\n      return `upload_${type}_${this.hashString(data)}_${options.index_name || 'default'}`;\r\n    }\r\n  }\r\n\r\n  // Generate cache key for queries\r\n  generateQueryCacheKey(query: string, indexName: string, options: any = {}): string {\r\n    return `query_${this.hashString(query)}_${indexName}_${JSON.stringify(options)}`;\r\n  }\r\n\r\n  // Simple string hash function\r\n  private hashString(str: string): string {\r\n    let hash = 0;\r\n    for (let i = 0; i < str.length; i++) {\r\n      const char = str.charCodeAt(i);\r\n      hash = ((hash << 5) - hash) + char;\r\n      hash = hash & hash; // Convert to 32-bit integer\r\n    }\r\n    return Math.abs(hash).toString(36);\r\n  }\r\n\r\n  // Get cached item\r\n  get<T>(cacheKey: string, key: string): T | null {\r\n    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return null;\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return null;\r\n\r\n      const cache: Record<string, CacheItem<T>> = JSON.parse(cacheData);\r\n      const item = cache[key];\r\n\r\n      if (!item) return null;\r\n\r\n      // Check if item has expired\r\n      if (Date.now() > item.expiresAt) {\r\n        this.remove(cacheKey, key);\r\n        return null;\r\n      }\r\n\r\n      console.log(`💾 Cache hit for key: ${key}`);\r\n      return item.data;\r\n    } catch (error) {\r\n      console.error('Error reading from cache:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Set cached item\r\n  set<T>(cacheKey: string, key: string, data: T, customExpiryMs?: number): void {\r\n    if (!CACHE_CONFIG.ENABLE_CACHE || typeof window === 'undefined') return;\r\n\r\n    try {\r\n      const expiryMs = customExpiryMs || CACHE_CONFIG.CACHE_EXPIRY_MS;\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      let cache: Record<string, CacheItem<T>> = {};\r\n\r\n      if (cacheData) {\r\n        cache = JSON.parse(cacheData);\r\n      }\r\n\r\n      // Clean expired items and enforce size limit\r\n      this.cleanCache(cache);\r\n\r\n      const item: CacheItem<T> = {\r\n        data,\r\n        timestamp: Date.now(),\r\n        key,\r\n        expiresAt: Date.now() + expiryMs\r\n      };\r\n\r\n      cache[key] = item;\r\n      localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n      console.log(`💾 Cached item with key: ${key}`);\r\n    } catch (error) {\r\n      console.error('Error writing to cache:', error);\r\n    }\r\n  }\r\n\r\n  // Remove cached item\r\n  remove(cacheKey: string, key: string): void {\r\n    if (typeof window === 'undefined') return;\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return;\r\n\r\n      const cache = JSON.parse(cacheData);\r\n      delete cache[key];\r\n      localStorage.setItem(cacheKey, JSON.stringify(cache));\r\n    } catch (error) {\r\n      console.error('Error removing from cache:', error);\r\n    }\r\n  }\r\n\r\n  // Clean expired items and enforce size limit\r\n  private cleanCache<T>(cache: Record<string, CacheItem<T>>): void {\r\n    const now = Date.now();\r\n    const keys = Object.keys(cache);\r\n\r\n    // Remove expired items\r\n    keys.forEach(key => {\r\n      if (cache[key].expiresAt < now) {\r\n        delete cache[key];\r\n      }\r\n    });\r\n\r\n    // Enforce size limit by removing oldest items\r\n    const remainingKeys = Object.keys(cache);\r\n    if (remainingKeys.length > CACHE_CONFIG.MAX_CACHE_SIZE) {\r\n      const sortedKeys = remainingKeys.sort((a, b) => cache[a].timestamp - cache[b].timestamp);\r\n      const keysToRemove = sortedKeys.slice(0, remainingKeys.length - CACHE_CONFIG.MAX_CACHE_SIZE);\r\n      keysToRemove.forEach(key => delete cache[key]);\r\n    }\r\n  }\r\n\r\n  // Clear all cache\r\n  clearCache(cacheKey: string): void {\r\n    if (typeof window === 'undefined') return;\r\n    localStorage.removeItem(cacheKey);\r\n    console.log(`🗑️ Cleared cache: ${cacheKey}`);\r\n  }\r\n\r\n  // Get cache statistics\r\n  getCacheStats(cacheKey: string): { size: number; oldestItem: number; newestItem: number } {\r\n    if (typeof window === 'undefined') return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n    try {\r\n      const cacheData = localStorage.getItem(cacheKey);\r\n      if (!cacheData) return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n      const cache = JSON.parse(cacheData);\r\n      const items = Object.values(cache) as CacheItem<any>[];\r\n\r\n      if (items.length === 0) return { size: 0, oldestItem: 0, newestItem: 0 };\r\n\r\n      const timestamps = items.map(item => item.timestamp);\r\n      return {\r\n        size: items.length,\r\n        oldestItem: Math.min(...timestamps),\r\n        newestItem: Math.max(...timestamps)\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting cache stats:', error);\r\n      return { size: 0, oldestItem: 0, newestItem: 0 };\r\n    }\r\n  }\r\n}\r\n\r\n// Configure axios defaults for longer timeouts\r\nconst api = axios.create({\r\n  timeout: 120000, // 2 minutes timeout for long-running operations\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  withCredentials: false, // Disable credentials for CORS\r\n});\r\n\r\n// Initialize cache manager\r\nconst cacheManager = CacheManager.getInstance();\r\n\r\n// Add request interceptor for debugging\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    console.log('🚀 Making request to:', config.url);\r\n    console.log('📤 Request config:', {\r\n      method: config.method,\r\n      url: config.url,\r\n      headers: config.headers,\r\n      timeout: config.timeout\r\n    });\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('❌ Request interceptor error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for debugging\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    console.log('✅ Response received:', {\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      url: response.config.url\r\n    });\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error('❌ Response interceptor error:', {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      url: error.config?.url,\r\n      data: error.response?.data\r\n    });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Backend configuration - Use the same port as FAISS service for consistency\r\nconst BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5010';\r\n\r\n// API endpoints\r\nconst ENDPOINTS = {\r\n  PROCESS_YOUTUBE: '/api/process_youtube',\r\n  PROCESS_ARTICLE: '/api/process_article',\r\n  PROCESS_DOCUMENT: '/api/process_document',\r\n  PROCESS_PDF: '/api/process_pdf',\r\n  PROCESS_AUDIO: '/api/process_audio',\r\n  SEARCH: '/api/search',\r\n  HEALTH: '/api/health'\r\n};\r\n\r\n// Types\r\nexport interface UploadResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  error?: string;\r\n  upload_id?: string;\r\n  index_name?: string;\r\n  filename?: string;\r\n}\r\n\r\nexport interface SearchResponse {\r\n  success: boolean;\r\n  results: SearchResult[];\r\n  query: string;\r\n  index_name?: string;\r\n  enhanced_response?: {\r\n    content: string;\r\n    model: string;\r\n    sources_used: number;\r\n  };\r\n  error?: string;\r\n  cached?: boolean;\r\n  searchTime?: number;\r\n}\r\n\r\nexport interface SearchResult {\r\n  score: number;\r\n  text: string;\r\n  source_type: string;\r\n  url: string;\r\n  title: string;\r\n  date: string;\r\n  category: string;\r\n  vector_id: string;\r\n  index_name?: string;\r\n}\r\n\r\nexport interface UploadOptions {\r\n  index_name?: string;\r\n  client_email?: string;\r\n}\r\n\r\n/**\r\n * Process YouTube URL with caching\r\n */\r\nexport async function processYouTubeURL(\r\n  url: string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('🎥 Starting YouTube URL processing:', url);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for YouTube processing: ${selectedIndex}`);\r\n\r\n    // Check cache first\r\n    const cacheKey = cacheManager.generateUploadCacheKey('youtube', url, { index_name: selectedIndex });\r\n    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached YouTube processing result');\r\n      return {\r\n        success: cachedResult.success,\r\n        message: cachedResult.message + ' (from cache)',\r\n        error: cachedResult.error,\r\n        upload_id: cachedResult.upload_id,\r\n        index_name: cachedResult.index_name,\r\n        filename: cachedResult.filename\r\n      };\r\n    }\r\n\r\n    const requestData = {\r\n      url,\r\n      index_name: selectedIndex,\r\n      client_email: options.client_email || ''\r\n    };\r\n\r\n    console.log('📤 Sending request to:', `${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`);\r\n    console.log('📤 Request data:', requestData);\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`, requestData);\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    console.log('✅ YouTube processing response:', response.data);\r\n\r\n    // Cache successful results\r\n    if (response.data.success) {\r\n      const cacheData: UploadCacheData = {\r\n        success: response.data.success,\r\n        message: response.data.message,\r\n        upload_id: response.data.upload_id,\r\n        index_name: response.data.index_name,\r\n        filename: response.data.filename,\r\n        processingTime\r\n      };\r\n\r\n      // Cache for 24 hours for successful URL processing\r\n      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);\r\n      console.log('💾 Cached YouTube processing result');\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing YouTube URL:', error);\r\n\r\n    // Log detailed error information first\r\n    const errorDetails = {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      url: error.config?.url,\r\n      method: error.config?.method\r\n    };\r\n    console.error('🔍 Detailed error information:', errorDetails);\r\n\r\n    // Handle network errors\r\n    if (error.code === 'ERR_NETWORK') {\r\n      console.error('🌐 Network error detected - backend may be unreachable');\r\n      return {\r\n        success: false,\r\n        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running on port 5010.`\r\n      };\r\n    }\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      console.error('⏰ Request timeout detected');\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the video is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    // Handle CORS errors\r\n    if (error.message?.includes('CORS') || error.code === 'ERR_BLOCKED_BY_CLIENT') {\r\n      console.error('🚫 CORS error detected');\r\n      return {\r\n        success: false,\r\n        error: 'CORS error - please check backend CORS configuration'\r\n      };\r\n    }\r\n\r\n    // Handle connection refused\r\n    if (error.code === 'ECONNREFUSED') {\r\n      console.error('🔌 Connection refused - backend server not running');\r\n      return {\r\n        success: false,\r\n        error: `Connection refused - Backend server is not running on ${BACKEND_BASE_URL}`\r\n      };\r\n    }\r\n\r\n    // Handle other specific error codes\r\n    if (error.response?.status === 404) {\r\n      console.error('🔍 Endpoint not found');\r\n      return {\r\n        success: false,\r\n        error: `Endpoint not found: ${ENDPOINTS.PROCESS_YOUTUBE}`\r\n      };\r\n    }\r\n\r\n    if (error.response?.status === 500) {\r\n      console.error('💥 Server error');\r\n      return {\r\n        success: false,\r\n        error: `Server error: ${error.response?.data?.error || 'Internal server error'}`\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process YouTube URL'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Article URL with caching\r\n */\r\nexport async function processArticleURL(\r\n  url: string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📰 Starting Article URL processing:', url);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Article processing: ${selectedIndex}`);\r\n\r\n    // Check cache first\r\n    const cacheKey = cacheManager.generateUploadCacheKey('article', url, { index_name: selectedIndex });\r\n    const cachedResult = cacheManager.get<UploadCacheData>(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached Article processing result');\r\n      return {\r\n        success: cachedResult.success,\r\n        message: cachedResult.message + ' (from cache)',\r\n        error: cachedResult.error,\r\n        upload_id: cachedResult.upload_id,\r\n        index_name: cachedResult.index_name,\r\n        filename: cachedResult.filename\r\n      };\r\n    }\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_ARTICLE}`, {\r\n      url,\r\n      index_name: selectedIndex,\r\n      client_email: options.client_email || ''\r\n    });\r\n    const processingTime = Date.now() - startTime;\r\n\r\n    console.log('✅ Article processing response:', response.data);\r\n\r\n    // Cache successful results\r\n    if (response.data.success) {\r\n      const cacheData: UploadCacheData = {\r\n        success: response.data.success,\r\n        message: response.data.message,\r\n        upload_id: response.data.upload_id,\r\n        index_name: response.data.index_name,\r\n        filename: response.data.filename,\r\n        processingTime\r\n      };\r\n\r\n      // Cache for 24 hours for successful URL processing\r\n      cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);\r\n      console.log('💾 Cached Article processing result');\r\n    }\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Article URL:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the article is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Article URL'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Document file\r\n */\r\nexport async function processDocument(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting Document processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Document processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_DOCUMENT}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log('✅ Document processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Document:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the document is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Document'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process PDF file\r\n */\r\nexport async function processPDF(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting PDF processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for PDF processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_PDF}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log('✅ PDF processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing PDF:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the PDF is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process PDF'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process Audio file\r\n */\r\nexport async function processAudio(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('🎵 Starting Audio processing:', file.name);\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for Audio processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_AUDIO}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 300000, // 5 minutes for audio processing (transcription takes time)\r\n    });\r\n\r\n    console.log('✅ Audio processing response:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing Audio:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the audio is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process Audio'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Search with enhanced DeepSeek integration and caching\r\n */\r\nexport async function searchContent(\r\n  query: string,\r\n  options: {\r\n    k?: number;\r\n    index_name?: string;\r\n    use_deepseek?: boolean;\r\n  } = {}\r\n): Promise<SearchResponse> {\r\n  try {\r\n    console.log('🔍 Starting content search:', query);\r\n\r\n    // Check cache first for search results\r\n    const cacheKey = cacheManager.generateQueryCacheKey(query, options.index_name || 'default', options);\r\n    const cachedResult = cacheManager.get<SearchResponse>(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey);\r\n\r\n    // Track cache statistics\r\n    cacheHitStats.totalRequests++;\r\n\r\n    if (cachedResult) {\r\n      console.log('💾 Using cached search result');\r\n      cacheHitStats.cacheHits++;\r\n      return {\r\n        ...cachedResult,\r\n        cached: true\r\n      };\r\n    }\r\n\r\n    cacheHitStats.cacheMisses++;\r\n\r\n    const startTime = Date.now();\r\n    const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.SEARCH}`, {\r\n      query,\r\n      k: options.k || 5,\r\n      index_name: options.index_name,\r\n      use_deepseek: options.use_deepseek || false\r\n    });\r\n    const searchTime = Date.now() - startTime;\r\n\r\n    console.log('✅ Search response:', response.data);\r\n\r\n    const searchResult: SearchResponse = {\r\n      success: true,\r\n      searchTime,\r\n      ...response.data\r\n    };\r\n\r\n    // Cache successful search results for 1 hour\r\n    if (searchResult.success && searchResult.results && searchResult.results.length > 0) {\r\n      cacheManager.set(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey, searchResult, 60 * 60 * 1000); // 1 hour\r\n      console.log('💾 Cached search result');\r\n    }\r\n\r\n    return searchResult;\r\n  } catch (error: any) {\r\n    console.error('❌ Error searching content:', error);\r\n    return {\r\n      success: false,\r\n      results: [],\r\n      query,\r\n      error: error.response?.data?.error || error.message || 'Failed to search content'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Test basic connection to backend\r\n */\r\nexport async function testConnection(): Promise<{ success: boolean; message?: string; error?: string }> {\r\n  try {\r\n    console.log('🔌 Testing connection to backend...');\r\n    console.log('🎯 Backend URL:', BACKEND_BASE_URL);\r\n\r\n    // Try a simple GET request first\r\n    const response = await axios.get(`${BACKEND_BASE_URL}/api/list-faiss-indexes`, {\r\n      timeout: 10000,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      withCredentials: false\r\n    });\r\n\r\n    console.log('✅ Connection test successful:', response.data);\r\n    return {\r\n      success: true,\r\n      message: 'Backend connection successful'\r\n    };\r\n  } catch (error: any) {\r\n    console.error('❌ Connection test failed:', error);\r\n\r\n    // Provide detailed error information\r\n    const errorDetails = {\r\n      message: error.message,\r\n      code: error.code,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      url: error.config?.url\r\n    };\r\n\r\n    console.error('🔍 Error details:', errorDetails);\r\n\r\n    return {\r\n      success: false,\r\n      error: `Connection failed: ${error.message} (${error.code || 'Unknown error'})`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Check backend health\r\n */\r\nexport async function checkBackendHealth(): Promise<{ success: boolean; message?: string; error?: string }> {\r\n  try {\r\n    console.log('🏥 Checking backend health...');\r\n    const response = await api.get(`${BACKEND_BASE_URL}${ENDPOINTS.HEALTH}`, {\r\n      timeout: 10000 // 10 seconds for health check\r\n    });\r\n    console.log('✅ Backend health check successful:', response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Backend health check failed:', error);\r\n    return {\r\n      success: false,\r\n      error: error.message || 'Backend is not available'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get upload options with FAISS configuration\r\n */\r\nfunction getUploadOptionsWithConfig(options: UploadOptions = {}): UploadOptions {\r\n  const faissConfig = getFaissConfig();\r\n\r\n  return {\r\n    index_name: options.index_name || faissConfig?.indexName || 'default',\r\n    client_email: options.client_email || faissConfig?.clientEmail || ''\r\n  };\r\n}\r\n\r\n/**\r\n * Process PDF/Document file with unified handling\r\n */\r\nexport async function processPDFDocument(\r\n  file: File,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  try {\r\n    console.log('📄 Starting PDF/Document processing:', file.name);\r\n\r\n    // Validate file type\r\n    const fileName = file.name.toLowerCase();\r\n    const isPDF = fileName.endsWith('.pdf');\r\n    const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||\r\n                     fileName.endsWith('.txt') || fileName.endsWith('.rtf');\r\n\r\n    if (!isPDF && !isDocument) {\r\n      return {\r\n        success: false,\r\n        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'\r\n      };\r\n    }\r\n\r\n    // Get selected index from localStorage if not provided in options\r\n    const selectedIndex = options.index_name ||\r\n      (typeof window !== 'undefined' ? localStorage.getItem('selectedFaissIndex') : null) ||\r\n      'default';\r\n\r\n    console.log(`📌 Using index for ${isPDF ? 'PDF' : 'Document'} processing: ${selectedIndex}`);\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('index_name', selectedIndex);\r\n    formData.append('client_email', options.client_email || '');\r\n\r\n    // Choose the appropriate endpoint based on file type\r\n    const endpoint = isPDF ? ENDPOINTS.PROCESS_PDF : ENDPOINTS.PROCESS_DOCUMENT;\r\n\r\n    console.log(`📤 Sending ${isPDF ? 'PDF' : 'Document'} to endpoint:`, `${BACKEND_BASE_URL}${endpoint}`);\r\n\r\n    const response = await api.post(`${BACKEND_BASE_URL}${endpoint}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n      timeout: 180000, // 3 minutes for file uploads\r\n    });\r\n\r\n    console.log(`✅ ${isPDF ? 'PDF' : 'Document'} processing response:`, response.data);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    console.error('❌ Error processing PDF/Document:', error);\r\n\r\n    // Handle timeout specifically\r\n    if (error.code === 'ECONNABORTED') {\r\n      return {\r\n        success: false,\r\n        error: 'Processing timeout - the file is still being processed in the background'\r\n      };\r\n    }\r\n\r\n    // Handle network errors\r\n    if (error.code === 'ERR_NETWORK') {\r\n      return {\r\n        success: false,\r\n        error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running.`\r\n      };\r\n    }\r\n\r\n    // Handle file size errors\r\n    if (error.response?.status === 413) {\r\n      return {\r\n        success: false,\r\n        error: 'File too large. Please upload a file smaller than 50MB.'\r\n      };\r\n    }\r\n\r\n    // Handle unsupported file type errors\r\n    if (error.response?.status === 400 && error.response?.data?.error?.includes('Unsupported file type')) {\r\n      return {\r\n        success: false,\r\n        error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || error.message || 'Failed to process file'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Upload handler that routes to appropriate processor based on type\r\n */\r\nexport async function handleUpload(\r\n  type: 'pdf' | 'youtube' | 'article' | 'mp3',\r\n  data: File | string,\r\n  options: UploadOptions = {}\r\n): Promise<UploadResponse> {\r\n  // Merge with FAISS configuration\r\n  const uploadOptions = getUploadOptionsWithConfig(options);\r\n\r\n  switch (type) {\r\n    case 'youtube':\r\n      return processYouTubeURL(data as string, uploadOptions);\r\n    case 'article':\r\n      return processArticleURL(data as string, uploadOptions);\r\n    case 'pdf':\r\n      // Use the unified PDF/Document processor\r\n      return processPDFDocument(data as File, uploadOptions);\r\n    case 'mp3':\r\n      return processAudio(data as File, uploadOptions);\r\n    default:\r\n      return {\r\n        success: false,\r\n        error: `Unsupported upload type: ${type}`\r\n      };\r\n  }\r\n}\r\n\r\n// Cache hit rate tracking\r\nlet cacheHitStats = {\r\n  totalRequests: 0,\r\n  cacheHits: 0,\r\n  cacheMisses: 0\r\n};\r\n\r\n// Cache utility functions for external use\r\nexport const CacheUtils = {\r\n  /**\r\n   * Clear all upload cache\r\n   */\r\n  clearUploadCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Clear all query cache\r\n   */\r\n  clearQueryCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Clear all caches\r\n   */\r\n  clearAllCache: () => {\r\n    cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);\r\n    cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);\r\n  },\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  getCacheStats: () => {\r\n    return {\r\n      upload: cacheManager.getCacheStats(CACHE_CONFIG.UPLOAD_CACHE_KEY),\r\n      query: cacheManager.getCacheStats(CACHE_CONFIG.QUERY_CACHE_KEY)\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Get cache hit rate statistics\r\n   */\r\n  getCacheHitRate: () => {\r\n    const hitRate = cacheHitStats.totalRequests > 0\r\n      ? (cacheHitStats.cacheHits / cacheHitStats.totalRequests * 100).toFixed(2)\r\n      : '0.00';\r\n\r\n    return {\r\n      ...cacheHitStats,\r\n      hitRate: `${hitRate}%`\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Reset cache hit rate statistics\r\n   */\r\n  resetCacheHitStats: () => {\r\n    cacheHitStats = {\r\n      totalRequests: 0,\r\n      cacheHits: 0,\r\n      cacheMisses: 0\r\n    };\r\n  },\r\n\r\n  /**\r\n   * Check if caching is enabled\r\n   */\r\n  isCacheEnabled: () => CACHE_CONFIG.ENABLE_CACHE,\r\n\r\n  /**\r\n   * Toggle cache functionality\r\n   */\r\n  toggleCache: (enabled: boolean) => {\r\n    CACHE_CONFIG.ENABLE_CACHE = enabled;\r\n    console.log(`💾 Cache ${enabled ? 'enabled' : 'disabled'}`);\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;AAGD;AADA;;;AAGA,sBAAsB;AACtB,MAAM,eAAe;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,iBAAiB,KAAK,KAAK,KAAK;IAChC,gBAAgB;IAChB,cAAc;AAChB;AAsBA,sBAAsB;AACtB,MAAM;IACJ,OAAe,SAAuB;IAEtC,aAAsB,CAAC;IAEvB,OAAO,cAA4B;QACjC,IAAI,CAAC,aAAa,QAAQ,EAAE;YAC1B,aAAa,QAAQ,GAAG,IAAI;QAC9B;QACA,OAAO,aAAa,QAAQ;IAC9B;IAEA,iCAAiC;IACjC,uBAAuB,IAAY,EAAE,IAAmB,EAAE,UAAe,CAAC,CAAC,EAAU;QACnF,IAAI,gBAAgB,MAAM;YACxB,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,CAAC,EAAE,QAAQ,UAAU,IAAI,WAAW;QAC3G,OAAO;YACL,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ,UAAU,IAAI,WAAW;QACrF;IACF;IAEA,iCAAiC;IACjC,sBAAsB,KAAa,EAAE,SAAiB,EAAE,UAAe,CAAC,CAAC,EAAU;QACjF,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;IAClF;IAEA,8BAA8B;IACtB,WAAW,GAAW,EAAU;QACtC,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,IAAI,UAAU,CAAC;YAC5B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;YAC9B,OAAO,OAAO,MAAM,4BAA4B;QAClD;QACA,OAAO,KAAK,GAAG,CAAC,MAAM,QAAQ,CAAC;IACjC;IAEA,kBAAkB;IAClB,IAAO,QAAgB,EAAE,GAAW,EAAY;QAC9C,wCAAiE,OAAO;;IAuB1E;IAEA,kBAAkB;IAClB,IAAO,QAAgB,EAAE,GAAW,EAAE,IAAO,EAAE,cAAuB,EAAQ;QAC5E,wCAAiE;;IA2BnE;IAEA,qBAAqB;IACrB,OAAO,QAAgB,EAAE,GAAW,EAAQ;QAC1C,wCAAmC;;IAYrC;IAEA,6CAA6C;IACrC,WAAc,KAAmC,EAAQ;QAC/D,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAO,OAAO,IAAI,CAAC;QAEzB,uBAAuB;QACvB,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,KAAK;gBAC9B,OAAO,KAAK,CAAC,IAAI;YACnB;QACF;QAEA,8CAA8C;QAC9C,MAAM,gBAAgB,OAAO,IAAI,CAAC;QAClC,IAAI,cAAc,MAAM,GAAG,aAAa,cAAc,EAAE;YACtD,MAAM,aAAa,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS;YACvF,MAAM,eAAe,WAAW,KAAK,CAAC,GAAG,cAAc,MAAM,GAAG,aAAa,cAAc;YAC3F,aAAa,OAAO,CAAC,CAAA,MAAO,OAAO,KAAK,CAAC,IAAI;QAC/C;IACF;IAEA,kBAAkB;IAClB,WAAW,QAAgB,EAAQ;QACjC,wCAAmC;;IAGrC;IAEA,uBAAuB;IACvB,cAAc,QAAgB,EAA4D;QACxF,wCAAmC,OAAO;YAAE,MAAM;YAAG,YAAY;YAAG,YAAY;QAAE;;IAqBpF;AACF;AAEA,+CAA+C;AAC/C,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,2BAA2B;AAC3B,MAAM,eAAe,aAAa,WAAW;AAE7C,wCAAwC;AACxC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,QAAQ,GAAG,CAAC,yBAAyB,OAAO,GAAG;IAC/C,QAAQ,GAAG,CAAC,sBAAsB;QAChC,QAAQ,OAAO,MAAM;QACrB,KAAK,OAAO,GAAG;QACf,SAAS,OAAO,OAAO;QACvB,SAAS,OAAO,OAAO;IACzB;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,gCAAgC;IAC9C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,yCAAyC;AACzC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,QAAQ,GAAG,CAAC,wBAAwB;QAClC,QAAQ,SAAS,MAAM;QACvB,YAAY,SAAS,UAAU;QAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;IAC1B;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,iCAAiC;QAC7C,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,IAAI;QAChB,QAAQ,MAAM,QAAQ,EAAE;QACxB,YAAY,MAAM,QAAQ,EAAE;QAC5B,KAAK,MAAM,MAAM,EAAE;QACnB,MAAM,MAAM,QAAQ,EAAE;IACxB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,6EAA6E;AAC7E,MAAM,mBAAmB,QAAQ,GAAG,CAAC,uBAAuB,IAAI;AAEhE,gBAAgB;AAChB,MAAM,YAAY;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;IACb,eAAe;IACf,QAAQ;IACR,QAAQ;AACV;AA+CO,eAAe,kBACpB,GAAW,EACX,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,eAAe;QAErE,oBAAoB;QACpB,MAAM,WAAW,aAAa,sBAAsB,CAAC,WAAW,KAAK;YAAE,YAAY;QAAc;QACjG,MAAM,eAAe,aAAa,GAAG,CAAkB,aAAa,gBAAgB,EAAE;QAEtF,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO,GAAG;gBAChC,OAAO,aAAa,KAAK;gBACzB,WAAW,aAAa,SAAS;gBACjC,YAAY,aAAa,UAAU;gBACnC,UAAU,aAAa,QAAQ;YACjC;QACF;QAEA,MAAM,cAAc;YAClB;YACA,YAAY;YACZ,cAAc,QAAQ,YAAY,IAAI;QACxC;QAEA,QAAQ,GAAG,CAAC,0BAA0B,GAAG,mBAAmB,UAAU,eAAe,EAAE;QACvF,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,eAAe,EAAE,EAAE;QACnF,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;QAE3D,2BAA2B;QAC3B,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YACzB,MAAM,YAA6B;gBACjC,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,WAAW,SAAS,IAAI,CAAC,SAAS;gBAClC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACpC,UAAU,SAAS,IAAI,CAAC,QAAQ;gBAChC;YACF;YAEA,mDAAmD;YACnD,aAAa,GAAG,CAAC,aAAa,gBAAgB,EAAE,UAAU;YAC1D,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,uCAAuC;QACvC,MAAM,eAAe;YACnB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,KAAK,MAAM,MAAM,EAAE;YACnB,QAAQ,MAAM,MAAM,EAAE;QACxB;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAEhD,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,eAAe;YAChC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wCAAwC,EAAE,iBAAiB,qDAAqD,CAAC;YAC3H;QACF;QAEA,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,EAAE,SAAS,WAAW,MAAM,IAAI,KAAK,yBAAyB;YAC7E,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,sDAAsD,EAAE,kBAAkB;YACpF;QACF;QAEA,oCAAoC;QACpC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,oBAAoB,EAAE,UAAU,eAAe,EAAE;YAC3D;QACF;QAEA,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,KAAK,CAAC;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,cAAc,EAAE,MAAM,QAAQ,EAAE,MAAM,SAAS,yBAAyB;YAClF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,kBACpB,GAAW,EACX,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,eAAe;QAErE,oBAAoB;QACpB,MAAM,WAAW,aAAa,sBAAsB,CAAC,WAAW,KAAK;YAAE,YAAY;QAAc;QACjG,MAAM,eAAe,aAAa,GAAG,CAAkB,aAAa,gBAAgB,EAAE;QAEtF,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO,GAAG;gBAChC,OAAO,aAAa,KAAK;gBACzB,WAAW,aAAa,SAAS;gBACjC,YAAY,aAAa,UAAU;gBACnC,UAAU,aAAa,QAAQ;YACjC;QACF;QAEA,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,eAAe,EAAE,EAAE;YACjF;YACA,YAAY;YACZ,cAAc,QAAQ,YAAY,IAAI;QACxC;QACA,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;QAE3D,2BAA2B;QAC3B,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;YACzB,MAAM,YAA6B;gBACjC,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,SAAS,SAAS,IAAI,CAAC,OAAO;gBAC9B,WAAW,SAAS,IAAI,CAAC,SAAS;gBAClC,YAAY,SAAS,IAAI,CAAC,UAAU;gBACpC,UAAU,SAAS,IAAI,CAAC,QAAQ;gBAChC;YACF;YAEA,mDAAmD;YACnD,aAAa,GAAG,CAAC,aAAa,gBAAgB,EAAE,UAAU;YAC1D,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,gBACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC,KAAK,IAAI;QAEzD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,eAAe;QAEtE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,gBAAgB,EAAE,EAAE,UAAU;YAC5F,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,WACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI;QAEpD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,eAAe;QAEjE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,WAAW,EAAE,EAAE,UAAU;YACvF,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,aACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,iCAAiC,KAAK,IAAI;QAEtD,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,eAAe;QAEnE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,aAAa,EAAE,EAAE,UAAU;YACzF,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;QACzD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,cACpB,KAAa,EACb,UAII,CAAC,CAAC;IAEN,IAAI;QACF,QAAQ,GAAG,CAAC,+BAA+B;QAE3C,uCAAuC;QACvC,MAAM,WAAW,aAAa,qBAAqB,CAAC,OAAO,QAAQ,UAAU,IAAI,WAAW;QAC5F,MAAM,eAAe,aAAa,GAAG,CAAiB,aAAa,eAAe,EAAE;QAEpF,yBAAyB;QACzB,cAAc,aAAa;QAE3B,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC;YACZ,cAAc,SAAS;YACvB,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ;YACV;QACF;QAEA,cAAc,WAAW;QAEzB,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,MAAM,EAAE,EAAE;YACxE;YACA,GAAG,QAAQ,CAAC,IAAI;YAChB,YAAY,QAAQ,UAAU;YAC9B,cAAc,QAAQ,YAAY,IAAI;QACxC;QACA,MAAM,aAAa,KAAK,GAAG,KAAK;QAEhC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,IAAI;QAE/C,MAAM,eAA+B;YACnC,SAAS;YACT;YACA,GAAG,SAAS,IAAI;QAClB;QAEA,6CAA6C;QAC7C,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;YACnF,aAAa,GAAG,CAAC,aAAa,eAAe,EAAE,UAAU,cAAc,KAAK,KAAK,OAAO,SAAS;YACjG,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,SAAS,EAAE;YACX;YACA,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB;QAE/B,iCAAiC;QACjC,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,iBAAiB,uBAAuB,CAAC,EAAE;YAC7E,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;QACnB;QAEA,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,qCAAqC;QACrC,MAAM,eAAe;YACnB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,KAAK,MAAM,MAAM,EAAE;QACrB;QAEA,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,mBAAmB,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,IAAI,IAAI,gBAAgB,CAAC,CAAC;QACjF;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,GAAG,mBAAmB,UAAU,MAAM,EAAE,EAAE;YACvE,SAAS,MAAM,8BAA8B;QAC/C;QACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,MAAM,OAAO,IAAI;QAC1B;IACF;AACF;AAEA;;CAEC,GACD,SAAS,2BAA2B,UAAyB,CAAC,CAAC;IAC7D,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI,aAAa,aAAa;QAC5D,cAAc,QAAQ,YAAY,IAAI,aAAa,eAAe;IACpE;AACF;AAKO,eAAe,mBACpB,IAAU,EACV,UAAyB,CAAC,CAAC;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC,KAAK,IAAI;QAE7D,qBAAqB;QACrB,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,QAAQ,SAAS,QAAQ,CAAC;QAChC,MAAM,aAAa,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,YACjD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;QAEhE,IAAI,CAAC,SAAS,CAAC,YAAY;YACzB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,QAAQ,UAAU,IACtC,CAAC,6EAA6E,IAAI,KAClF;QAEF,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ,QAAQ,WAAW,aAAa,EAAE,eAAe;QAE3F,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,cAAc;QAC9B,SAAS,MAAM,CAAC,gBAAgB,QAAQ,YAAY,IAAI;QAExD,qDAAqD;QACrD,MAAM,WAAW,QAAQ,UAAU,WAAW,GAAG,UAAU,gBAAgB;QAE3E,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,QAAQ,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,UAAU;QAErG,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,GAAG,mBAAmB,UAAU,EAAE,UAAU;YAC1E,SAAS;gBACP,gBAAgB;YAClB;YACA,SAAS;QACX;QAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,QAAQ,WAAW,qBAAqB,CAAC,EAAE,SAAS,IAAI;QACjF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,oCAAoC;QAElD,8BAA8B;QAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,wBAAwB;QACxB,IAAI,MAAM,IAAI,KAAK,eAAe;YAChC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wCAAwC,EAAE,iBAAiB,wCAAwC,CAAC;YAC9G;QACF;QAEA,0BAA0B;QAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,sCAAsC;QACtC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,0BAA0B;YACpG,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,IAAI;QACzD;IACF;AACF;AAKO,eAAe,aACpB,IAA2C,EAC3C,IAAmB,EACnB,UAAyB,CAAC,CAAC;IAE3B,iCAAiC;IACjC,MAAM,gBAAgB,2BAA2B;IAEjD,OAAQ;QACN,KAAK;YACH,OAAO,kBAAkB,MAAgB;QAC3C,KAAK;YACH,OAAO,kBAAkB,MAAgB;QAC3C,KAAK;YACH,yCAAyC;YACzC,OAAO,mBAAmB,MAAc;QAC1C,KAAK;YACH,OAAO,aAAa,MAAc;QACpC;YACE,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,yBAAyB,EAAE,MAAM;YAC3C;IACJ;AACF;AAEA,0BAA0B;AAC1B,IAAI,gBAAgB;IAClB,eAAe;IACf,WAAW;IACX,aAAa;AACf;AAGO,MAAM,aAAa;IACxB;;GAEC,GACD,kBAAkB;QAChB,aAAa,UAAU,CAAC,aAAa,gBAAgB;IACvD;IAEA;;GAEC,GACD,iBAAiB;QACf,aAAa,UAAU,CAAC,aAAa,eAAe;IACtD;IAEA;;GAEC,GACD,eAAe;QACb,aAAa,UAAU,CAAC,aAAa,gBAAgB;QACrD,aAAa,UAAU,CAAC,aAAa,eAAe;IACtD;IAEA;;GAEC,GACD,eAAe;QACb,OAAO;YACL,QAAQ,aAAa,aAAa,CAAC,aAAa,gBAAgB;YAChE,OAAO,aAAa,aAAa,CAAC,aAAa,eAAe;QAChE;IACF;IAEA;;GAEC,GACD,iBAAiB;QACf,MAAM,UAAU,cAAc,aAAa,GAAG,IAC1C,CAAC,cAAc,SAAS,GAAG,cAAc,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC,KACtE;QAEJ,OAAO;YACL,GAAG,aAAa;YAChB,SAAS,GAAG,QAAQ,CAAC,CAAC;QACxB;IACF;IAEA;;GAEC,GACD,oBAAoB;QAClB,gBAAgB;YACd,eAAe;YACf,WAAW;YACX,aAAa;QACf;IACF;IAEA;;GAEC,GACD,gBAAgB,IAAM,aAAa,YAAY;IAE/C;;GAEC,GACD,aAAa,CAAC;QACZ,aAAa,YAAY,GAAG;QAC5B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,YAAY,YAAY;IAC5D;AACF"}}, {"offset": {"line": 2395, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/ChatInputUpload.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Pi<PERSON>, Pi<PERSON>heck<PERSON><PERSON><PERSON>, PiWarning, PiDatabase } from 'react-icons/pi';\r\nimport UploadDropdown from './UploadDropdown';\r\nimport FileDropZone from './FileDropZone';\r\nimport URLInput from './URLInput';\r\nimport { handleUpload, UploadResponse } from '../../services/uploadService';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface ChatInputUploadProps {\r\n  selectedLanguage: string;\r\n  onFileUpload?: (files: File[]) => void;\r\n  onURLSubmit?: (url: string, type: 'youtube' | 'article') => void;\r\n  onUploadStateChange?: (isActive: boolean, showDropdown: boolean) => void;\r\n  onNetworkError?: () => void; // Callback for network errors\r\n  disabled?: boolean;\r\n}\r\n\r\nexport type UploadType = 'pdf' | 'youtube' | 'article' | 'mp3';\r\n\r\ninterface UploadState {\r\n  type: UploadType | null;\r\n  files: File[];\r\n  url: string;\r\n  isActive: boolean;\r\n  isProcessing: boolean;\r\n  uploadResult: UploadResponse | null;\r\n}\r\n\r\nconst ChatInputUpload: React.FC<ChatInputUploadProps> = ({\r\n  selectedLanguage,\r\n  onFileUpload,\r\n  onURLSubmit,\r\n  onUploadStateChange,\r\n  onNetworkError,\r\n  disabled = false\r\n}) => {\r\n  const [showDropdown, setShowDropdown] = useState(false);\r\n  const [uploadState, setUploadState] = useState<UploadState>({\r\n    type: null,\r\n    files: [],\r\n    url: '',\r\n    isActive: false,\r\n    isProcessing: false,\r\n    uploadResult: null\r\n  });\r\n\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const iconRef = useRef<HTMLButtonElement>(null);\r\n  const autoCloseTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Close dropdown when clicking outside or pressing Escape\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target as Node) &&\r\n        iconRef.current &&\r\n        !iconRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowDropdown(false);\r\n      }\r\n    }\r\n\r\n    function handleKeyDown(event: KeyboardEvent) {\r\n      if (event.key === 'Escape' && showDropdown) {\r\n        setShowDropdown(false);\r\n        // Return focus to the pin button\r\n        iconRef.current?.focus();\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    document.addEventListener('keydown', handleKeyDown);\r\n\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }, [showDropdown]);\r\n\r\n  // Cleanup timeout on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (autoCloseTimeoutRef.current) {\r\n        clearTimeout(autoCloseTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Notify parent about upload state changes\r\n  useEffect(() => {\r\n    if (onUploadStateChange) {\r\n      onUploadStateChange(uploadState.isActive, showDropdown);\r\n    }\r\n  }, [uploadState.isActive, showDropdown, onUploadStateChange]);\r\n\r\n  const handleUploadTypeSelect = (type: UploadType) => {\r\n    // If switching to a different type, clear previous content\r\n    const isSwitchingType = uploadState.isActive && uploadState.type !== type;\r\n\r\n    setUploadState({\r\n      type,\r\n      files: isSwitchingType ? [] : uploadState.files,\r\n      url: isSwitchingType ? '' : uploadState.url,\r\n      isActive: true,\r\n      isProcessing: false,\r\n      uploadResult: null\r\n    });\r\n    setShowDropdown(false);\r\n\r\n    // If switching types, clear previous uploads\r\n    if (isSwitchingType) {\r\n      if (onFileUpload) {\r\n        onFileUpload([]);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleAddMoreContent = () => {\r\n    // Allow adding more content of the same type or switching types\r\n    setShowDropdown(!showDropdown);\r\n  };\r\n\r\n  const getUploadStatusText = () => {\r\n    if (!uploadState.isActive) return '';\r\n\r\n    // Show processing status\r\n    if (uploadState.isProcessing) {\r\n      return 'Processing...';\r\n    }\r\n\r\n    // Show upload result status\r\n    if (uploadState.uploadResult) {\r\n      if (uploadState.uploadResult.success) {\r\n        return '✅ Processed successfully';\r\n      } else {\r\n        return '❌ Processing failed';\r\n      }\r\n    }\r\n\r\n    const fileCount = uploadState.files.length;\r\n    const hasUrl = uploadState.url.trim().length > 0;\r\n\r\n    if (uploadState.type === 'pdf' || uploadState.type === 'mp3') {\r\n      return fileCount > 0 ? `${fileCount} file${fileCount > 1 ? 's' : ''} selected` : 'No files selected';\r\n    } else {\r\n      return hasUrl ? 'URL added' : 'No URL added';\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (files: File[]) => {\r\n    if (!files.length || !uploadState.type || (uploadState.type !== 'pdf' && uploadState.type !== 'mp3')) {\r\n      return;\r\n    }\r\n\r\n    const file = files[0];\r\n    console.log('📁 Files selected for upload:', files);\r\n\r\n    // Validate file type and size before processing\r\n    const fileName = file.name.toLowerCase();\r\n    const fileSize = file.size;\r\n    const maxSize = 50 * 1024 * 1024; // 50MB\r\n\r\n    // Check file size\r\n    if (fileSize > maxSize) {\r\n      toast.error('File too large. Please upload a file smaller than 50MB.');\r\n      return;\r\n    }\r\n\r\n    // Validate file type based on upload type\r\n    if (uploadState.type === 'pdf') {\r\n      const isPDF = fileName.endsWith('.pdf');\r\n      const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') ||\r\n                        fileName.endsWith('.txt') || fileName.endsWith('.rtf');\r\n\r\n      if (!isPDF && !isDocument) {\r\n        toast.error('Please upload PDF, DOC, DOCX, TXT, or RTF files only.');\r\n        return;\r\n      }\r\n    } else if (uploadState.type === 'mp3') {\r\n      const isAudio = fileName.endsWith('.mp3') || fileName.endsWith('.wav') ||\r\n                     fileName.endsWith('.m4a') || fileName.endsWith('.flac');\r\n\r\n      if (!isAudio) {\r\n        toast.error('Please upload MP3, WAV, M4A, or FLAC audio files only.');\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Update state with files and processing status\r\n    setUploadState(prev => ({\r\n      ...prev,\r\n      files,\r\n      isProcessing: true,\r\n      uploadResult: null\r\n    }));\r\n\r\n    try {\r\n      console.log(`🚀 Starting ${uploadState.type} upload for file:`, file.name);\r\n\r\n      const result = await handleUpload(uploadState.type, file);\r\n      console.log('📥 Upload result:', result);\r\n\r\n      // Update state with result\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: result\r\n      }));\r\n\r\n      // Show success/error toast with more specific messages\r\n      if (result.success) {\r\n        const fileType = uploadState.type === 'pdf' ? 'PDF/Document' : 'Audio';\r\n        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';\r\n        toast.success(`${fileType} file processed successfully${cacheMessage}! Content has been indexed.`);\r\n\r\n        // Call the original callback if provided\r\n        if (onFileUpload) {\r\n          onFileUpload(files);\r\n        }\r\n\r\n        // Auto-close popup after successful upload with a slight delay for user feedback\r\n        autoCloseTimeoutRef.current = setTimeout(() => {\r\n          handleClearUpload();\r\n        }, 2000); // 2 second delay to show success state\r\n      } else {\r\n        const errorMessage = result.error || 'Failed to process file';\r\n        console.error('❌ Upload failed:', errorMessage);\r\n        toast.error(errorMessage);\r\n\r\n        // Check if it's a network error and trigger connection test\r\n        if (errorMessage.includes('Network error') || errorMessage.includes('Connection refused')) {\r\n          if (onNetworkError) {\r\n            onNetworkError();\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Upload error:', error);\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: { success: false, error: 'Upload failed due to network error' }\r\n      }));\r\n      toast.error('Upload failed due to network error. Please check your connection.');\r\n\r\n      // Call network error callback if provided\r\n      if (onNetworkError) {\r\n        onNetworkError();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleURLChange = (url: string) => {\r\n    setUploadState(prev => ({\r\n      ...prev,\r\n      url\r\n    }));\r\n  };\r\n\r\n  const handleURLSubmit = async (url: string) => {\r\n    if (!uploadState.type || (uploadState.type !== 'youtube' && uploadState.type !== 'article')) {\r\n      return;\r\n    }\r\n\r\n    // Set processing state\r\n    setUploadState(prev => ({ ...prev, isProcessing: true, uploadResult: null }));\r\n\r\n    try {\r\n      // Process the URL using the upload service\r\n      const result = await handleUpload(uploadState.type, url);\r\n\r\n      // Update state with result\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: result\r\n      }));\r\n\r\n      // Show success/error toast\r\n      if (result.success) {\r\n        const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';\r\n        toast.success(`${uploadState.type === 'youtube' ? 'YouTube video' : 'Article'} processed successfully${cacheMessage}!`);\r\n        // Call the original callback if provided\r\n        if (onURLSubmit) {\r\n          onURLSubmit(url, uploadState.type);\r\n        }\r\n\r\n        // Auto-close popup after successful upload with a slight delay for user feedback\r\n        autoCloseTimeoutRef.current = setTimeout(() => {\r\n          handleClearUpload();\r\n        }, 2000); // 2 second delay to show success state\r\n      } else {\r\n        toast.error(result.error || 'Failed to process URL');\r\n\r\n        // Check if it's a network error and trigger connection test\r\n        if (result.error?.includes('Network error') || result.error?.includes('Connection refused')) {\r\n          if (onNetworkError) {\r\n            onNetworkError();\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      setUploadState(prev => ({\r\n        ...prev,\r\n        isProcessing: false,\r\n        uploadResult: { success: false, error: 'Upload failed' }\r\n      }));\r\n      toast.error('Upload failed');\r\n\r\n      // Trigger network error callback for any catch block errors\r\n      if (onNetworkError) {\r\n        onNetworkError();\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleClearUpload = () => {\r\n    // Clear any pending auto-close timeout\r\n    if (autoCloseTimeoutRef.current) {\r\n      clearTimeout(autoCloseTimeoutRef.current);\r\n      autoCloseTimeoutRef.current = null;\r\n    }\r\n\r\n    setUploadState({\r\n      type: null,\r\n      files: [],\r\n      url: '',\r\n      isActive: false,\r\n      isProcessing: false,\r\n      uploadResult: null\r\n    });\r\n  };\r\n\r\n  const getIconColor = () => {\r\n    if (disabled) return 'text-gray-400';\r\n    \r\n    switch (selectedLanguage) {\r\n      case 'Tamil':\r\n        return uploadState.isActive ? 'text-purple-600' : 'text-purple-500 hover:text-purple-600';\r\n      case 'Telugu':\r\n        return uploadState.isActive ? 'text-green-600' : 'text-green-500 hover:text-green-600';\r\n      case 'Kannada':\r\n        return uploadState.isActive ? 'text-orange-600' : 'text-orange-500 hover:text-orange-600';\r\n      default:\r\n        return uploadState.isActive ? 'text-blue-600' : 'text-blue-500 hover:text-blue-600';\r\n    }\r\n  };\r\n\r\n  const renderUploadContent = () => {\r\n    if (!uploadState.isActive || !uploadState.type) return null;\r\n\r\n    const isFileType = uploadState.type === 'pdf' || uploadState.type === 'mp3';\r\n    const isURLType = uploadState.type === 'youtube' || uploadState.type === 'article';\r\n\r\n    return (\r\n      <div className=\"mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className={`p-1 rounded text-xs font-medium text-white ${\r\n              uploadState.type === 'pdf' ? 'bg-red-500' :\r\n              uploadState.type === 'mp3' ? 'bg-purple-500' :\r\n              uploadState.type === 'youtube' ? 'bg-red-600' :\r\n              uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'\r\n            }`}>\r\n              {uploadState.type === 'pdf' ? 'PDF' :\r\n               uploadState.type === 'mp3' ? 'MP3' :\r\n               uploadState.type === 'youtube' ? 'YT' :\r\n               uploadState.type === 'article' ? 'WEB' : '?'}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                {uploadState.type === 'pdf' && 'PDF/Document Upload'}\r\n                {uploadState.type === 'mp3' && 'MP3 Audio Upload'}\r\n                {uploadState.type === 'youtube' && 'YouTube URL'}\r\n                {uploadState.type === 'article' && 'Article URL'}\r\n              </span>\r\n              <span className={`text-xs flex items-center gap-1 ${\r\n                uploadState.uploadResult?.success ? 'text-green-600 dark:text-green-400' :\r\n                uploadState.uploadResult && !uploadState.uploadResult.success ? 'text-red-600 dark:text-red-400' :\r\n                uploadState.isProcessing ? 'text-blue-600 dark:text-blue-400' :\r\n                'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {uploadState.isProcessing && (\r\n                  <div className=\"w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin\"></div>\r\n                )}\r\n                {uploadState.uploadResult?.success && (\r\n                  <>\r\n                    <PiCheckCircle className=\"w-3 h-3\" />\r\n                    {uploadState.uploadResult.message?.includes('cache') && (\r\n                      <PiDatabase className=\"w-3 h-3 text-blue-500\" title=\"Loaded from cache\" />\r\n                    )}\r\n                  </>\r\n                )}\r\n                {uploadState.uploadResult && !uploadState.uploadResult.success && (\r\n                  <PiWarning className=\"w-3 h-3\" />\r\n                )}\r\n                {getUploadStatusText()}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            {/* Functional pin icon positioned near close button when upload is active */}\r\n            <div className=\"relative\">\r\n              <button\r\n                ref={iconRef}\r\n                type=\"button\"\r\n                onClick={handleAddMoreContent}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter' || e.key === ' ') {\r\n                    e.preventDefault();\r\n                    handleAddMoreContent();\r\n                  }\r\n                }}\r\n                disabled={disabled}\r\n                className={`p-1.5 rounded-full transition-all duration-200 transform hover:scale-105 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 ${getIconColor()} ${\r\n                  disabled ? 'cursor-not-allowed opacity-50 bg-gray-100' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 bg-white dark:bg-gray-700 shadow-sm hover:shadow-md'\r\n                } ${showDropdown ? 'ring-2 ring-offset-1 ' + (\r\n                  selectedLanguage === 'Tamil' ? 'ring-purple-300 focus:ring-purple-500' :\r\n                  selectedLanguage === 'Telugu' ? 'ring-green-300 focus:ring-green-500' :\r\n                  selectedLanguage === 'Kannada' ? 'ring-orange-300 focus:ring-orange-500' : 'ring-blue-300 focus:ring-blue-500'\r\n                ) : (\r\n                  selectedLanguage === 'Tamil' ? 'focus:ring-purple-500' :\r\n                  selectedLanguage === 'Telugu' ? 'focus:ring-green-500' :\r\n                  selectedLanguage === 'Kannada' ? 'focus:ring-orange-500' : 'focus:ring-blue-500'\r\n                )}`}\r\n                title={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options (Press Esc)' : 'Add more files or change upload type (Press Enter)'}\r\n                aria-label={disabled ? 'Upload disabled' : showDropdown ? 'Close upload options' : 'Add more files or change upload type'}\r\n                aria-expanded={showDropdown}\r\n                aria-haspopup=\"menu\"\r\n              >\r\n                <PiPaperclip className={`w-4 h-4 transition-transform duration-200 ${showDropdown ? 'rotate-45' : ''}`} />\r\n              </button>\r\n\r\n              {/* Badge indicator for active upload type */}\r\n              <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full text-xs flex items-center justify-center text-white font-bold ${\r\n                uploadState.type === 'pdf' ? 'bg-red-500' :\r\n                uploadState.type === 'mp3' ? 'bg-purple-500' :\r\n                uploadState.type === 'youtube' ? 'bg-red-600' :\r\n                uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'\r\n              }`}>\r\n                {uploadState.type === 'pdf' ? 'P' :\r\n                 uploadState.type === 'mp3' ? '♪' :\r\n                 uploadState.type === 'youtube' ? 'Y' :\r\n                 uploadState.type === 'article' ? 'A' : '?'}\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={handleClearUpload}\r\n              className=\"p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-full hover:bg-gray-200 dark:hover:bg-gray-600\"\r\n              title=\"Clear upload\"\r\n            >\r\n              <PiX className=\"w-4 h-4\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {isFileType && (\r\n          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>\r\n            <FileDropZone\r\n              acceptedTypes={uploadState.type === 'pdf' ? '.pdf,.doc,.docx,.txt,.rtf' : '.mp3,.wav,.m4a,.flac'}\r\n              onFilesSelected={handleFileUpload}\r\n              maxFiles={1}\r\n              selectedLanguage={selectedLanguage}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {isURLType && uploadState.type && (uploadState.type === 'youtube' || uploadState.type === 'article') && (\r\n          <div className={uploadState.isProcessing ? 'opacity-50 pointer-events-none' : ''}>\r\n            <URLInput\r\n              type={uploadState.type}\r\n              value={uploadState.url}\r\n              onChange={handleURLChange}\r\n              onSubmit={handleURLSubmit}\r\n              selectedLanguage={selectedLanguage}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Upload content area */}\r\n      {renderUploadContent()}\r\n\r\n      {/* Upload icon with improved alignment - hide when upload is active */}\r\n      {/* {!uploadState.isActive && (\r\n        <button\r\n          ref={iconRef}\r\n          type=\"button\"\r\n          onClick={() => setShowDropdown(!showDropdown)}\r\n          disabled={disabled}\r\n          className={`p-2.5 rounded-full transition-all duration-200 transform hover:scale-105 ${getIconColor()} ${\r\n            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 shadow-sm hover:shadow-md'\r\n          }`}\r\n          title={disabled ? 'Upload disabled' : 'Upload file or add URL'}\r\n        >\r\n          <PiPaperclip className=\"w-5 h-5\" />\r\n        </button>\r\n      )} */}\r\n\r\n      {/* Dropdown menu - positioned based on upload state */}\r\n      {showDropdown && !disabled && (\r\n        <div\r\n          ref={dropdownRef}\r\n          className={uploadState.isActive ? \"absolute top-full right-0 mt-2 z-50\" : \"\"}\r\n        >\r\n          <UploadDropdown\r\n            onSelect={handleUploadTypeSelect}\r\n            selectedLanguage={selectedLanguage}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInputUpload;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AALA;;;;;;;;;AA2BA,MAAM,kBAAkD,CAAC,EACvD,gBAAgB,EAChB,YAAY,EACZ,WAAW,EACX,mBAAmB,EACnB,cAAc,EACd,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,OAAO,EAAE;QACT,KAAK;QACL,UAAU;QACV,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC1C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAE1D,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,QAAQ,OAAO,IACf,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACtC;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,cAAc,KAAoB;YACzC,IAAI,MAAM,GAAG,KAAK,YAAY,cAAc;gBAC1C,gBAAgB;gBAChB,iCAAiC;gBACjC,QAAQ,OAAO,EAAE;YACnB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,WAAW;QAErC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;YAC1C;QACF;IACF,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB;YACvB,oBAAoB,YAAY,QAAQ,EAAE;QAC5C;IACF,GAAG;QAAC,YAAY,QAAQ;QAAE;QAAc;KAAoB;IAE5D,MAAM,yBAAyB,CAAC;QAC9B,2DAA2D;QAC3D,MAAM,kBAAkB,YAAY,QAAQ,IAAI,YAAY,IAAI,KAAK;QAErE,eAAe;YACb;YACA,OAAO,kBAAkB,EAAE,GAAG,YAAY,KAAK;YAC/C,KAAK,kBAAkB,KAAK,YAAY,GAAG;YAC3C,UAAU;YACV,cAAc;YACd,cAAc;QAChB;QACA,gBAAgB;QAEhB,6CAA6C;QAC7C,IAAI,iBAAiB;YACnB,IAAI,cAAc;gBAChB,aAAa,EAAE;YACjB;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,gEAAgE;QAChE,gBAAgB,CAAC;IACnB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,QAAQ,EAAE,OAAO;QAElC,yBAAyB;QACzB,IAAI,YAAY,YAAY,EAAE;YAC5B,OAAO;QACT;QAEA,4BAA4B;QAC5B,IAAI,YAAY,YAAY,EAAE;YAC5B,IAAI,YAAY,YAAY,CAAC,OAAO,EAAE;gBACpC,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,MAAM,YAAY,YAAY,KAAK,CAAC,MAAM;QAC1C,MAAM,SAAS,YAAY,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG;QAE/C,IAAI,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK,OAAO;YAC5D,OAAO,YAAY,IAAI,GAAG,UAAU,KAAK,EAAE,YAAY,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG;QACnF,OAAO;YACL,OAAO,SAAS,cAAc;QAChC;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,YAAY,IAAI,IAAK,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK,OAAQ;YACpG;QACF;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,gDAAgD;QAChD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,WAAW,KAAK,IAAI;QAC1B,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QAEzC,kBAAkB;QAClB,IAAI,WAAW,SAAS;YACtB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0CAA0C;QAC1C,IAAI,YAAY,IAAI,KAAK,OAAO;YAC9B,MAAM,QAAQ,SAAS,QAAQ,CAAC;YAChC,MAAM,aAAa,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,YAChD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;YAEjE,IAAI,CAAC,SAAS,CAAC,YAAY;gBACzB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF,OAAO,IAAI,YAAY,IAAI,KAAK,OAAO;YACrC,MAAM,UAAU,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC,WAChD,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;YAE9D,IAAI,CAAC,SAAS;gBACZ,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,gDAAgD;QAChD,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;gBACA,cAAc;gBACd,cAAc;YAChB,CAAC;QAED,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC,iBAAiB,CAAC,EAAE,KAAK,IAAI;YAEzE,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,IAAI,EAAE;YACpD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,2BAA2B;YAC3B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;gBAChB,CAAC;YAED,uDAAuD;YACvD,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW,YAAY,IAAI,KAAK,QAAQ,iBAAiB;gBAC/D,MAAM,eAAe,OAAO,OAAO,EAAE,SAAS,WAAW,kBAAkB;gBAC3E,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,SAAS,4BAA4B,EAAE,aAAa,2BAA2B,CAAC;gBAEjG,yCAAyC;gBACzC,IAAI,cAAc;oBAChB,aAAa;gBACf;gBAEA,iFAAiF;gBACjF,oBAAoB,OAAO,GAAG,WAAW;oBACvC;gBACF,GAAG,OAAO,uCAAuC;YACnD,OAAO;gBACL,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBAEZ,4DAA4D;gBAC5D,IAAI,aAAa,QAAQ,CAAC,oBAAoB,aAAa,QAAQ,CAAC,uBAAuB;oBACzF,IAAI,gBAAgB;wBAClB;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;wBAAE,SAAS;wBAAO,OAAO;oBAAqC;gBAC9E,CAAC;YACD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAEZ,0CAA0C;YAC1C,IAAI,gBAAgB;gBAClB;YACF;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP;YACF,CAAC;IACH;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,YAAY,IAAI,IAAK,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,WAAY;YAC3F;QACF;QAEA,uBAAuB;QACvB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,cAAc;YAAK,CAAC;QAE3E,IAAI;YACF,2CAA2C;YAC3C,MAAM,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,IAAI,EAAE;YAEpD,2BAA2B;YAC3B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;gBAChB,CAAC;YAED,2BAA2B;YAC3B,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,eAAe,OAAO,OAAO,EAAE,SAAS,WAAW,kBAAkB;gBAC3E,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,GAAG,YAAY,IAAI,KAAK,YAAY,kBAAkB,UAAU,uBAAuB,EAAE,aAAa,CAAC,CAAC;gBACtH,yCAAyC;gBACzC,IAAI,aAAa;oBACf,YAAY,KAAK,YAAY,IAAI;gBACnC;gBAEA,iFAAiF;gBACjF,oBAAoB,OAAO,GAAG,WAAW;oBACvC;gBACF,GAAG,OAAO,uCAAuC;YACnD,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAE5B,4DAA4D;gBAC5D,IAAI,OAAO,KAAK,EAAE,SAAS,oBAAoB,OAAO,KAAK,EAAE,SAAS,uBAAuB;oBAC3F,IAAI,gBAAgB;wBAClB;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,eAAe,CAAA,OAAQ,CAAC;oBACtB,GAAG,IAAI;oBACP,cAAc;oBACd,cAAc;wBAAE,SAAS;wBAAO,OAAO;oBAAgB;gBACzD,CAAC;YACD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAEZ,4DAA4D;YAC5D,IAAI,gBAAgB;gBAClB;YACF;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,uCAAuC;QACvC,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;YACxC,oBAAoB,OAAO,GAAG;QAChC;QAEA,eAAe;YACb,MAAM;YACN,OAAO,EAAE;YACT,KAAK;YACL,UAAU;YACV,cAAc;YACd,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU,OAAO;QAErB,OAAQ;YACN,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,oBAAoB;YACpD,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,mBAAmB;YACnD,KAAK;gBACH,OAAO,YAAY,QAAQ,GAAG,oBAAoB;YACpD;gBACE,OAAO,YAAY,QAAQ,GAAG,kBAAkB;QACpD;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,YAAY,QAAQ,IAAI,CAAC,YAAY,IAAI,EAAE,OAAO;QAEvD,MAAM,aAAa,YAAY,IAAI,KAAK,SAAS,YAAY,IAAI,KAAK;QACtE,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK;QAEzE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,2CAA2C,EAC1D,YAAY,IAAI,KAAK,QAAQ,eAC7B,YAAY,IAAI,KAAK,QAAQ,kBAC7B,YAAY,IAAI,KAAK,YAAY,eACjC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eACjD;8CACC,YAAY,IAAI,KAAK,QAAQ,QAC7B,YAAY,IAAI,KAAK,QAAQ,QAC7B,YAAY,IAAI,KAAK,YAAY,OACjC,YAAY,IAAI,KAAK,YAAY,QAAQ;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDACb,YAAY,IAAI,KAAK,SAAS;gDAC9B,YAAY,IAAI,KAAK,SAAS;gDAC9B,YAAY,IAAI,KAAK,aAAa;gDAClC,YAAY,IAAI,KAAK,aAAa;;;;;;;sDAErC,8OAAC;4CAAK,WAAW,CAAC,gCAAgC,EAChD,YAAY,YAAY,EAAE,UAAU,uCACpC,YAAY,YAAY,IAAI,CAAC,YAAY,YAAY,CAAC,OAAO,GAAG,mCAChE,YAAY,YAAY,GAAG,qCAC3B,oCACA;;gDACC,YAAY,YAAY,kBACvB,8OAAC;oDAAI,WAAU;;;;;;gDAEhB,YAAY,YAAY,EAAE,yBACzB;;sEACE,8OAAC,8IAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDACxB,YAAY,YAAY,CAAC,OAAO,EAAE,SAAS,0BAC1C,8OAAC,8IAAA,CAAA,aAAU;4DAAC,WAAU;4DAAwB,OAAM;;;;;;;;gDAIzD,YAAY,YAAY,IAAI,CAAC,YAAY,YAAY,CAAC,OAAO,kBAC5D,8OAAC,8IAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAEtB;;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,SAAS;4CACT,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;oDACtC,EAAE,cAAc;oDAChB;gDACF;4CACF;4CACA,UAAU;4CACV,WAAW,CAAC,yKAAyK,EAAE,eAAe,CAAC,EACrM,WAAW,8CAA8C,8GAC1D,CAAC,EAAE,eAAe,0BAA0B,CAC3C,qBAAqB,UAAU,0CAC/B,qBAAqB,WAAW,wCAChC,qBAAqB,YAAY,0CAA0C,mCAC7E,IACE,qBAAqB,UAAU,0BAC/B,qBAAqB,WAAW,yBAChC,qBAAqB,YAAY,0BAA0B,uBAC1D;4CACH,OAAO,WAAW,oBAAoB,eAAe,qCAAqC;4CAC1F,cAAY,WAAW,oBAAoB,eAAe,yBAAyB;4CACnF,iBAAe;4CACf,iBAAc;sDAEd,cAAA,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAW,CAAC,0CAA0C,EAAE,eAAe,cAAc,IAAI;;;;;;;;;;;sDAIxG,8OAAC;4CAAI,WAAW,CAAC,4GAA4G,EAC3H,YAAY,IAAI,KAAK,QAAQ,eAC7B,YAAY,IAAI,KAAK,QAAQ,kBAC7B,YAAY,IAAI,KAAK,YAAY,eACjC,YAAY,IAAI,KAAK,YAAY,gBAAgB,eACjD;sDACC,YAAY,IAAI,KAAK,QAAQ,MAC7B,YAAY,IAAI,KAAK,QAAQ,MAC7B,YAAY,IAAI,KAAK,YAAY,MACjC,YAAY,IAAI,KAAK,YAAY,MAAM;;;;;;;;;;;;8CAG5C,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,8IAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKpB,4BACC,8OAAC;oBAAI,WAAW,YAAY,YAAY,GAAG,mCAAmC;8BAC5E,cAAA,8OAAC,6IAAA,CAAA,UAAY;wBACX,eAAe,YAAY,IAAI,KAAK,QAAQ,8BAA8B;wBAC1E,iBAAiB;wBACjB,UAAU;wBACV,kBAAkB;;;;;;;;;;;gBAKvB,aAAa,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,aAAa,YAAY,IAAI,KAAK,SAAS,mBACjG,8OAAC;oBAAI,WAAW,YAAY,YAAY,GAAG,mCAAmC;8BAC5E,cAAA,8OAAC,yIAAA,CAAA,UAAQ;wBACP,MAAM,YAAY,IAAI;wBACtB,OAAO,YAAY,GAAG;wBACtB,UAAU;wBACV,UAAU;wBACV,kBAAkB;;;;;;;;;;;;;;;;;IAM9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ;YAmBA,gBAAgB,CAAC,0BAChB,8OAAC;gBACC,KAAK;gBACL,WAAW,YAAY,QAAQ,GAAG,wCAAwC;0BAE1E,cAAA,8OAAC,+IAAA,CAAA,UAAc;oBACb,UAAU;oBACV,kBAAkB;;;;;;;;;;;;;;;;;AAM9B;uCAEe"}}, {"offset": {"line": 2936, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2942, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/services/ApiService.ts"], "sourcesContent": ["import { CacheService } from './CacheService';\n\n// API configuration\nexport const API_CONFIG = {\n  // Production endpoint (previously used)\n  PROD_ENDPOINT: \"http://localhost:5010/financial_query\",\n  // Development endpoint for suggest.py (running locally)\n  DEV_ENDPOINT: \"http://localhost:5010/financial_query\",\n  // Use DEV_ENDPOINT for local development, PROD_ENDPOINT for production\n  ACTIVE_ENDPOINT: \"http://localhost:5010/financial_query\",\n  // FAISS collection endpoint (keeping PINE for backward compatibility)\n  PINE_COLLECTION_ENDPOINT: \"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS\"\n};\n\nexport interface ApiRequestBody {\n  query: string;\n  client_email?: string;\n  index_name?: string;\n  embed_model?: string;\n  upload_context?: string;\n  has_recent_uploads?: boolean;\n  target_language?: string;\n  enable_translation?: boolean;\n}\n\nexport interface ApiResponse {\n  ai_response: string;\n  related_questions?: string[];\n  pinecone_indexes?: string[];\n  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;\n  faiss_categories?: any[];\n  has_uploaded_content?: boolean;\n  upload_sources?: any[];\n  error?: string;\n  translation_applied?: boolean;\n  query_language?: string;\n  target_language?: string;\n  translation_metadata?: any;\n  source_language?: string;\n  translation_timestamp?: string;\n}\n\nexport class ApiService {\n\n  // Function to fetch PINE collection data for the current user\n  // Falls back to default configuration (default index) when:\n  // - No user email is found\n  // - API requests fail\n  // - No PINE data exists for user\n  // - Any errors occur\n  static async fetchPineCollection() {\n    try {\n      const userEmail = localStorage.getItem(\"user_email\");\n      console.log(\"local mail\", userEmail);\n      if (!userEmail) {\n        console.warn(\"No user email found in localStorage - using default configuration\");\n        // Set default configuration\n        const defaultConfig = {\n          api_key: \"pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua\",\n          index_name: \"default\",\n          embed_model: \"all-MiniLM-L6-v2\"\n        };\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\n        if (defaultConfig.embed_model) {\n          localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\n        }\n        return defaultConfig;\n      }\n\n      // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)\n      const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;\n\n      const response = await fetch(filterUrl, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'xxxid': 'FAISS'\n        }\n      });\n\n      if (!response.ok) {\n        console.warn(`Failed to fetch PINE collection: ${response.status} - using default configuration`);\n        // Set default FAISS configuration\n        const defaultConfig = {\n          index_name: \"default\",\n          embed_model: \"all-MiniLM-L6-v2\"\n        };\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\n        localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\n        return defaultConfig;\n      }\n\n      const data = await response.json();\n      console.log(\"FAISS collection response:\", data);\n\n      // Check if user exists in FAISS collection and extract all their indexes\n      if (data.statusCode === 200 && data.source && data.source.length > 0) {\n        // Parse each item in the source array (they are JSON strings)\n        const faissData = data.source.map((item: string) => JSON.parse(item));\n\n        // Extract all indexes and embedding models for this user\n        const userIndexes: string[] = [];\n        const userEmbedModels: string[] = [];\n        let firstUserEntry: any | null = null;\n\n        faissData.forEach((item: any) => {\n          if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {\n            if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return\n            if (item.index_name && !userIndexes.includes(item.index_name)) {\n              userIndexes.push(item.index_name);\n            }\n            if (item.embed_model && !userEmbedModels.includes(item.embed_model)) {\n              userEmbedModels.push(item.embed_model);\n            }\n          }\n        });\n\n        if (userIndexes.length > 0) {\n          // User exists in FAISS collection - store all their configurations\n          localStorage.setItem(\"faiss_index_name\", userIndexes[0]); // Store first index as default\n          localStorage.setItem(\"faiss_embed_model\", userEmbedModels[0] || \"all-MiniLM-L6-v2\"); // Store first embed model as default\n          localStorage.setItem('userFaissIndexes', JSON.stringify(userIndexes)); // Store all indexes\n          localStorage.setItem('userEmbedModels', JSON.stringify(userEmbedModels)); // Store all embed models\n          console.log(\"Found existing FAISS data for user:\", userEmail);\n          console.log(\"User indexes:\", userIndexes);\n          console.log(\"User embed models:\", userEmbedModels);\n          return firstUserEntry;\n        } else {\n          // User doesn't exist in FAISS collection - use default values without auto-creation\n          console.log(\"No FAISS data found for user:\", userEmail, \"- using default configuration without auto-creation\");\n\n          const defaultConfig = {\n            index_name: \"default\",\n            embed_model: \"all-MiniLM-L6-v2\"\n          };\n          localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\n          localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\n          localStorage.setItem(\"faiss_client_email\", userEmail);\n          console.log(\"Using default FAISS configuration for user:\", userEmail);\n          return defaultConfig;\n        }\n      } else {\n        // No FAISS data found - use default values without auto-creation\n        console.log(\"No FAISS data found for user:\", userEmail, \"- using default configuration without auto-creation\");\n\n        const defaultConfig = {\n          index_name: \"default\",\n          embed_model: \"all-MiniLM-L6-v2\"\n        };\n        localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\n        localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\n        localStorage.setItem(\"faiss_client_email\", userEmail);\n        console.log(\"Using default FAISS configuration for user:\", userEmail);\n        return defaultConfig;\n      }\n    } catch (error) {\n      console.warn(\"Error fetching FAISS collection - using default configuration:\", error);\n      // Fallback to default values on error\n      const defaultConfig = {\n        index_name: \"default\",\n        embed_model: \"all-MiniLM-L6-v2\"\n      };\n      localStorage.setItem(\"faiss_index_name\", defaultConfig.index_name);\n      localStorage.setItem(\"faiss_embed_model\", defaultConfig.embed_model);\n      return defaultConfig;\n    }\n  }\n\n  // Function to get current user's email from session storage\n  private static getCurrentUserEmail(): string | null {\n    try {\n      if (typeof window === 'undefined') return null;\n\n      // Try multiple sources for user email\n      const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\n      if (directEmail) return directEmail;\n\n      // Try from user session data\n      const userSession = sessionStorage.getItem('resultUser');\n      if (userSession) {\n        const userData = JSON.parse(userSession);\n        return userData.email || userData.username || null;\n      }\n\n      return null;\n    } catch (error) {\n      console.error('Error getting current user email:', error);\n      return null;\n    }\n  }\n\n  // Function to fetch user-specific FAISS indexes from the backend\n  static async fetchUserIndexes(): Promise<string[]> {\n    try {\n      console.log(\"Fetching user-specific FAISS indexes...\");\n\n      // Get current user's email\n      const userEmail = this.getCurrentUserEmail();\n\n      if (!userEmail) {\n        console.warn(\"⚠️ No user email found, fetching all available indexes\");\n        // Fall back to GET request for all indexes if no user email\n        const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success && data.indexes && data.indexes.length > 0) {\n            console.log(\"✅ Retrieved all FAISS indexes:\", data.indexes);\n            return data.indexes;\n          }\n        }\n\n        console.warn(\"⚠️ Falling back to default index\");\n        return [\"default\"];\n      }\n\n      console.log(`🔍 Fetching indexes for user: ${userEmail}`);\n\n      // Call the list-faiss-indexes endpoint with user email for filtering\n      const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email: userEmail\n        })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success && data.indexes && data.indexes.length > 0) {\n          console.log(`✅ Retrieved ${data.indexes.length} FAISS indexes for user ${userEmail}:`, data.indexes);\n          return data.indexes;\n        } else {\n          console.warn(`⚠️ No FAISS indexes found for user ${userEmail}:`, data.error || \"Unknown error\");\n          // Return empty array for users with no indexes instead of default\n          return [];\n        }\n      } else {\n        console.warn(\"⚠️ FAISS indexes API failed:\", response.status);\n      }\n\n      // Fallback to empty array if user-specific request fails\n      console.warn(\"⚠️ No indexes available for user\");\n      return [];\n    } catch (error) {\n      console.error(\"Error fetching user-specific FAISS indexes:\", error);\n      return [];\n    }\n  }\n\n  // Function to send query to API\n  static async sendQuery(requestBody: ApiRequestBody): Promise<ApiResponse> {\n    const response = await fetch(API_CONFIG.ACTIVE_ENDPOINT, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(requestBody),\n    });\n\n    if (!response.ok) {\n      // Check if the error is related to the index\n      if (response.status === 404) {\n        const errorData = await response.json();\n        if (errorData.error && errorData.error.includes(\"No matching documents found or index not available\")) {\n          throw new Error(`The selected index is not available or contains no relevant data. Please try another index.`);\n        }\n      }\n      throw new Error(`API response error: ${response.status}`);\n    }\n\n    return await response.json();\n  }\n}\n"], "names": [], "mappings": ";;;;AAGO,MAAM,aAAa;IACxB,wCAAwC;IACxC,eAAe;IACf,wDAAwD;IACxD,cAAc;IACd,uEAAuE;IACvE,iBAAiB;IACjB,sEAAsE;IACtE,0BAA0B;AAC5B;AA8BO,MAAM;IAEX,8DAA8D;IAC9D,4DAA4D;IAC5D,2BAA2B;IAC3B,sBAAsB;IACtB,iCAAiC;IACjC,qBAAqB;IACrB,aAAa,sBAAsB;QACjC,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,QAAQ,GAAG,CAAC,cAAc;YAC1B,IAAI,CAAC,WAAW;gBACd,QAAQ,IAAI,CAAC;gBACb,4BAA4B;gBAC5B,MAAM,gBAAgB;oBACpB,SAAS;oBACT,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,IAAI,cAAc,WAAW,EAAE;oBAC7B,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACrE;gBACA,OAAO;YACT;YAEA,uGAAuG;YACvG,MAAM,YAAY,CAAC,6GAA6G,EAAE,mBAAmB,UAAU,IAAI,KAAK;YAExK,MAAM,WAAW,MAAM,MAAM,WAAW;gBACtC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,SAAS;gBACX;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,8BAA8B,CAAC;gBAChG,kCAAkC;gBAClC,MAAM,gBAAgB;oBACpB,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACnE,OAAO;YACT;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,yEAAyE;YACzE,IAAI,KAAK,UAAU,KAAK,OAAO,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACpE,8DAA8D;gBAC9D,MAAM,YAAY,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAiB,KAAK,KAAK,CAAC;gBAE/D,yDAAyD;gBACzD,MAAM,cAAwB,EAAE;gBAChC,MAAM,kBAA4B,EAAE;gBACpC,IAAI,iBAA6B;gBAEjC,UAAU,OAAO,CAAC,CAAC;oBACjB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,WAAW,OAAO,UAAU,IAAI,GAAG,WAAW,IAAI;wBACtF,IAAI,CAAC,gBAAgB,iBAAiB,MAAM,8BAA8B;wBAC1E,IAAI,KAAK,UAAU,IAAI,CAAC,YAAY,QAAQ,CAAC,KAAK,UAAU,GAAG;4BAC7D,YAAY,IAAI,CAAC,KAAK,UAAU;wBAClC;wBACA,IAAI,KAAK,WAAW,IAAI,CAAC,gBAAgB,QAAQ,CAAC,KAAK,WAAW,GAAG;4BACnE,gBAAgB,IAAI,CAAC,KAAK,WAAW;wBACvC;oBACF;gBACF;gBAEA,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,mEAAmE;oBACnE,aAAa,OAAO,CAAC,oBAAoB,WAAW,CAAC,EAAE,GAAG,+BAA+B;oBACzF,aAAa,OAAO,CAAC,qBAAqB,eAAe,CAAC,EAAE,IAAI,qBAAqB,qCAAqC;oBAC1H,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC,eAAe,oBAAoB;oBAC3F,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,mBAAmB,yBAAyB;oBACnG,QAAQ,GAAG,CAAC,uCAAuC;oBACnD,QAAQ,GAAG,CAAC,iBAAiB;oBAC7B,QAAQ,GAAG,CAAC,sBAAsB;oBAClC,OAAO;gBACT,OAAO;oBACL,oFAAoF;oBACpF,QAAQ,GAAG,CAAC,iCAAiC,WAAW;oBAExD,MAAM,gBAAgB;wBACpB,YAAY;wBACZ,aAAa;oBACf;oBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;oBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;oBACnE,aAAa,OAAO,CAAC,sBAAsB;oBAC3C,QAAQ,GAAG,CAAC,+CAA+C;oBAC3D,OAAO;gBACT;YACF,OAAO;gBACL,iEAAiE;gBACjE,QAAQ,GAAG,CAAC,iCAAiC,WAAW;gBAExD,MAAM,gBAAgB;oBACpB,YAAY;oBACZ,aAAa;gBACf;gBACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;gBACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;gBACnE,aAAa,OAAO,CAAC,sBAAsB;gBAC3C,QAAQ,GAAG,CAAC,+CAA+C;gBAC3D,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kEAAkE;YAC/E,sCAAsC;YACtC,MAAM,gBAAgB;gBACpB,YAAY;gBACZ,aAAa;YACf;YACA,aAAa,OAAO,CAAC,oBAAoB,cAAc,UAAU;YACjE,aAAa,OAAO,CAAC,qBAAqB,cAAc,WAAW;YACnE,OAAO;QACT;IACF;IAEA,4DAA4D;IAC5D,OAAe,sBAAqC;QAClD,IAAI;YACF,wCAAmC,OAAO;;YAE1C,sCAAsC;YACtC,MAAM;YAGN,6BAA6B;YAC7B,MAAM;QAOR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEA,iEAAiE;IACjE,aAAa,mBAAsC;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,MAAM,YAAY,IAAI,CAAC,mBAAmB;YAE1C,IAAI,CAAC,WAAW;gBACd,QAAQ,IAAI,CAAC;gBACb,4DAA4D;gBAC5D,MAAM,WAAW,MAAM,MAAM,gDAAgD;oBAC3E,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;wBAC3D,QAAQ,GAAG,CAAC,kCAAkC,KAAK,OAAO;wBAC1D,OAAO,KAAK,OAAO;oBACrB;gBACF;gBAEA,QAAQ,IAAI,CAAC;gBACb,OAAO;oBAAC;iBAAU;YACpB;YAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,WAAW;YAExD,qEAAqE;YACrE,MAAM,WAAW,MAAM,MAAM,gDAAgD;gBAC3E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;gBACT;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC3D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,OAAO;oBACnG,OAAO,KAAK,OAAO;gBACrB,OAAO;oBACL,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI;oBAC/E,kEAAkE;oBAClE,OAAO,EAAE;gBACX;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,gCAAgC,SAAS,MAAM;YAC9D;YAEA,yDAAyD;YACzD,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO,EAAE;QACX;IACF;IAEA,gCAAgC;IAChC,aAAa,UAAU,WAA2B,EAAwB;QACxE,MAAM,WAAW,MAAM,MAAM,WAAW,eAAe,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,6CAA6C;YAC7C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK,CAAC,QAAQ,CAAC,uDAAuD;oBACrG,MAAM,IAAI,MAAM,CAAC,2FAA2F,CAAC;gBAC/G;YACF;YACA,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B;AACF"}}, {"offset": {"line": 3165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/services/TranslationService.ts"], "sourcesContent": ["export class TranslationService {\r\n  private static translationCache = new Map<string, string>();\r\n\r\n  // Language detection helper\r\n  static detectLanguage(text: string): string {\r\n    if (!text || !text.trim()) return 'en';\r\n\r\n    // Tamil detection using Unicode ranges\r\n    if (/[\\u0B80-\\u0BFF]/.test(text)) return 'ta';\r\n\r\n    // Telugu detection using Unicode ranges\r\n    if (/[\\u0C00-\\u0C7F]/.test(text)) return 'te';\r\n\r\n    // Kannada detection using Unicode ranges\r\n    if (/[\\u0C80-\\u0CFF]/.test(text)) return 'kn';\r\n\r\n    // Hindi detection using Unicode ranges\r\n    if (/[\\u0900-\\u097F]/.test(text)) return 'hi';\r\n\r\n    // Arabic detection\r\n    if (/[\\u0600-\\u06FF]/.test(text)) return 'ar';\r\n\r\n    // Chinese detection\r\n    if (/[\\u4e00-\\u9fff]/.test(text)) return 'zh';\r\n\r\n    // Default to English\r\n    return 'en';\r\n  }\r\n\r\n  // Function to translate text using backend translation service\r\n  static async translateText(text: string, sourceLang: string, targetLang: string): Promise<string> {\r\n    console.log(`🔄 Translating from ${sourceLang} to ${targetLang}: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);\r\n\r\n    // If source and target languages are the same, return original text\r\n    if (sourceLang === targetLang) {\r\n      console.log(\"⚠️ Source and target languages are the same, returning original text\");\r\n      return text;\r\n    }\r\n\r\n    // Check cache first\r\n    const cacheKey = `${sourceLang}-${targetLang}-${text}`;\r\n    const cachedTranslation = this.translationCache.get(cacheKey);\r\n    if (cachedTranslation) {\r\n      console.log(\"💾 Using cached translation\");\r\n      return cachedTranslation;\r\n    }\r\n\r\n    try {\r\n      console.log(\"🌐 Using backend translation service\");\r\n      \r\n      // Call our backend translation endpoint\r\n      const response = await fetch('http://localhost:5010/api/translate', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          text: text,\r\n          source_lang: sourceLang,\r\n          target_lang: targetLang\r\n     })\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        if (data.success && data.data && data.data.translated_text) {\r\n          const translatedText = data.data.translated_text;\r\n          console.log(`✅ Backend translation successful: ${translatedText.substring(0, 50)}${translatedText.length > 50 ? '...' : ''}`);\r\n\r\n          // Cache the translation\r\n          this.translationCache.set(cacheKey, translatedText);\r\n          return translatedText;\r\n        }\r\n      }\r\n\r\n      // If backend translation fails, try fallback patterns\r\n      console.log(\"🔄 Backend translation failed, trying fallback patterns\");\r\n      const fallbackTranslation = this.getFallbackTranslation(text, sourceLang, targetLang);\r\n      \r\n      if (fallbackTranslation !== text) {\r\n        console.log(`✅ Fallback translation successful: ${fallbackTranslation.substring(0, 50)}${fallbackTranslation.length > 50 ? '...' : ''}`);\r\n        this.translationCache.set(cacheKey, fallbackTranslation);\r\n        return fallbackTranslation;\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"❌ Translation error:\", error);\r\n    }\r\n    \r\n    // Final fallback - return original text\r\n    console.log(\"🔄 All translation methods failed, returning original text\");\r\n    this.translationCache.set(cacheKey, text);\r\n    return text;\r\n  }\r\n\r\n  // Fallback translation using pattern matching\r\n  static getFallbackTranslation(text: string, sourceLang: string, targetLang: string): string {\r\n    // Common translation patterns for basic phrases\r\n    const translationPatterns: Record<string, Record<string, string>> = {\r\n      'en-ta': {\r\n        'Hello': 'வணக்கம்',\r\n        'Thank you': 'நன்றி',\r\n        'Yes': 'ஆம்',\r\n        'No': 'இல்லை',\r\n        'Please': 'தயவுசெய்து',\r\n        'Sorry': 'மன்னிக்கவும்',\r\n        'Good morning': 'காலை வணக்கம்',\r\n        'Good evening': 'மாலை வணக்கம்',\r\n        'How are you?': 'நீங்கள் எப்படி இருக்கிறீர்கள்?',\r\n        'What is your name?': 'உங்கள் பெயர் என்ன?'\r\n      },\r\n      'en-te': {\r\n        'Hello': 'హలో',\r\n        'Thank you': 'ధన్యవాదాలు',\r\n        'Yes': 'అవును',\r\n        'No': 'లేదు',\r\n        'Please': 'దయచేసి',\r\n        'Sorry': 'క్షమించండి',\r\n        'Good morning': 'శుభోదయం',\r\n        'Good evening': 'శుభ సాయంత్రం',\r\n        'How are you?': 'మీరు ఎలా ఉన్నారు?',\r\n        'What is your name?': 'మీ పేరు ఏమిటి?'\r\n      },\r\n      'en-kn': {\r\n        'Hello': 'ಹಲೋ',\r\n        'Thank you': 'ಧನ್ಯವಾದಗಳು',\r\n        'Yes': 'ಹೌದು',\r\n        'No': 'ಇಲ್ಲ',\r\n        'Please': 'ದಯವಿಟ್ಟು',\r\n        'Sorry': 'ಕ್ಷಮಿಸಿ',\r\n        'Good morning': 'ಶುಭೋದಯ',\r\n        'Good evening': 'ಶುಭ ಸಂಜೆ',\r\n        'How are you?': 'ನೀವು ಹೇಗಿದ್ದೀರಿ?',\r\n        'What is your name?': 'ನಿಮ್ಮ ಹೆಸರು ಏನು?'\r\n      },\r\n      'ta-en': {\r\n        'வணக்கம்': 'Hello',\r\n        'நன்றி': 'Thank you',\r\n        'ஆம்': 'Yes',\r\n        'இல்லை': 'No',\r\n        'தயவுசெய்து': 'Please',\r\n        'மன்னிக்கவும்': 'Sorry',\r\n        'காலை வணக்கம்': 'Good morning',\r\n        'மாலை வணக்கம்': 'Good evening'\r\n      },\r\n      'te-en': {\r\n        'హలో': 'Hello',\r\n        'ధన్యవాదాలు': 'Thank you',\r\n        'అవును': 'Yes',\r\n        'లేదు': 'No',\r\n        'దయచేసి': 'Please',\r\n        'క్షమించండి': 'Sorry',\r\n        'శుభోదయం': 'Good morning',\r\n        'శుభ సాయంత్రం': 'Good evening'\r\n      },\r\n      'kn-en': {\r\n        'ಹಲೋ': 'Hello',\r\n        'ಧನ್ಯವಾದಗಳು': 'Thank you',\r\n        'ಹೌದು': 'Yes',\r\n        'ಇಲ್ಲ': 'No',\r\n        'ದಯವಿಟ್ಟು': 'Please',\r\n        'ಕ್ಷಮಿಸಿ': 'Sorry',\r\n        'ಶುಭೋದಯ': 'Good morning',\r\n        'ಶುಭ ಸಂಜೆ': 'Good evening'\r\n      }\r\n    };\r\n\r\n    const patternKey = `${sourceLang}-${targetLang}`;\r\n    const patterns = translationPatterns[patternKey];\r\n    \r\n    if (patterns) {\r\n      // Try exact match first\r\n      if (patterns[text]) {\r\n        return patterns[text];\r\n      }\r\n      \r\n      // Try partial matches for longer text\r\n      let translatedText = text;\r\n      for (const [source, target] of Object.entries(patterns)) {\r\n        if (text.includes(source)) {\r\n          translatedText = translatedText.replace(source, target);\r\n        }\r\n      }\r\n      \r\n      if (translatedText !== text) {\r\n        return translatedText;\r\n      }\r\n    }\r\n\r\n    return text; // Return original if no translation found\r\n  }\r\n\r\n  // Function to extract and preserve capital words during translation\r\n  static extractCapitalWords(text: string): { text: string; capitalWords: Array<{ word: string; placeholder: string }> } {\r\n    const capitalWordsMatches = text.match(/\\b[A-Z]{2,}\\b/g) || [];\r\n    const capitalWords = capitalWordsMatches.map((word: string) => ({\r\n      word,\r\n      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\r\n    }));\r\n\r\n    let textWithPlaceholders = text;\r\n    capitalWords.forEach((item) => {\r\n      textWithPlaceholders = textWithPlaceholders.replace(item.word, item.placeholder);\r\n    });\r\n\r\n    return { text: textWithPlaceholders, capitalWords };\r\n  }\r\n\r\n  // Function to restore capital words after translation\r\n  static restoreCapitalWords(text: string, capitalWords: Array<{ word: string; placeholder: string }>): string {\r\n    let restoredText = text;\r\n    capitalWords.forEach((item) => {\r\n      restoredText = restoredText.replace(item.placeholder, item.word);\r\n    });\r\n    return restoredText;\r\n  }\r\n\r\n  // Function to translate text while preserving capital words\r\n  static async translateWithCapitalWordsPreservation(\r\n    text: string,\r\n    sourceLang: string,\r\n    targetLang: string\r\n  ): Promise<string> {\r\n    const { text: textWithPlaceholders, capitalWords } = this.extractCapitalWords(text);\r\n    const translatedText = await this.translateText(textWithPlaceholders, sourceLang, targetLang);\r\n    return this.restoreCapitalWords(translatedText, capitalWords);\r\n  }\r\n\r\n  // Function to translate entire response objects\r\n  static async translateResponse(response: any, targetLang: string): Promise<any> {\r\n    if (!response || !targetLang) return response;\r\n\r\n    const translatedResponse = { ...response };\r\n\r\n    try {\r\n      // Detect source language from AI response\r\n      const sourceLang = response.ai_response ? this.detectLanguage(response.ai_response) : 'en';\r\n\r\n      // Skip translation if source and target are the same\r\n      if (sourceLang === targetLang) {\r\n        console.log(`⚠️ Source and target languages are the same (${targetLang}), skipping translation`);\r\n        return response;\r\n      }\r\n\r\n      console.log(`🌐 Translating response from ${sourceLang} to ${targetLang}`);\r\n\r\n      // Translate AI response\r\n      if (response.ai_response) {\r\n        translatedResponse.ai_response = await this.translateWithCapitalWordsPreservation(\r\n          response.ai_response,\r\n          sourceLang,\r\n          targetLang\r\n        );\r\n      }\r\n\r\n      // Translate related questions\r\n      if (response.related_questions && Array.isArray(response.related_questions)) {\r\n        translatedResponse.related_questions = await Promise.all(\r\n          response.related_questions.map((question: string) =>\r\n            this.translateWithCapitalWordsPreservation(question, sourceLang, targetLang)\r\n          )\r\n        );\r\n      }\r\n\r\n      // Add translation metadata\r\n      translatedResponse.translation_applied = true;\r\n      translatedResponse.source_language = sourceLang;\r\n      translatedResponse.target_language = targetLang;\r\n      translatedResponse.translation_timestamp = new Date().toISOString();\r\n\r\n      console.log(`✅ Response translation completed: ${sourceLang} -> ${targetLang}`);\r\n      return translatedResponse;\r\n\r\n    } catch (error) {\r\n      console.error('❌ Error translating response:', error);\r\n      // Return original response with error metadata\r\n      return {\r\n        ...response,\r\n        translation_applied: false,\r\n        translation_error: error instanceof Error ? error.message : 'Unknown translation error'\r\n      };\r\n    }\r\n  }\r\n\r\n  // Function to get language name from code\r\n  static getLanguageName(langCode: string): string {\r\n    const languageNames: Record<string, string> = {\r\n      'en': 'English',\r\n      'ta': 'Tamil',\r\n      'te': 'Telugu',\r\n      'kn': 'Kannada',\r\n      'hi': 'Hindi',\r\n      'es': 'Spanish',\r\n      'fr': 'French',\r\n      'de': 'German',\r\n      'zh': 'Chinese',\r\n      'ja': 'Japanese',\r\n      'ko': 'Korean',\r\n      'ar': 'Arabic'\r\n    };\r\n\r\n    return languageNames[langCode] || langCode;\r\n  }\r\n\r\n  // Function to clear translation cache\r\n  static clearCache(): void {\r\n    this.translationCache.clear();\r\n    console.log('🗑️ Translation cache cleared');\r\n  }\r\n\r\n  // Function to get cache statistics\r\n  static getCacheStats(): { size: number; keys: string[] } {\r\n    return {\r\n      size: this.translationCache.size,\r\n      keys: Array.from(this.translationCache.keys()).slice(0, 10) // Show first 10 keys\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM;IACX,OAAe,mBAAmB,IAAI,MAAsB;IAE5D,4BAA4B;IAC5B,OAAO,eAAe,IAAY,EAAU;QAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,OAAO;QAElC,uCAAuC;QACvC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,wCAAwC;QACxC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,yCAAyC;QACzC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,uCAAuC;QACvC,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,mBAAmB;QACnB,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,oBAAoB;QACpB,IAAI,kBAAkB,IAAI,CAAC,OAAO,OAAO;QAEzC,qBAAqB;QACrB,OAAO;IACT;IAEA,+DAA+D;IAC/D,aAAa,cAAc,IAAY,EAAE,UAAkB,EAAE,UAAkB,EAAmB;QAChG,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,EAAE,KAAK,SAAS,CAAC,GAAG,MAAM,KAAK,MAAM,GAAG,KAAK,QAAQ,IAAI;QAE1H,oEAAoE;QACpE,IAAI,eAAe,YAAY;YAC7B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM;QACtD,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACpD,IAAI,mBAAmB;YACrB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,wCAAwC;YACxC,MAAM,WAAW,MAAM,MAAM,uCAAuC;gBAClE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,aAAa;oBACb,aAAa;gBAClB;YACC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE;oBAC1D,MAAM,iBAAiB,KAAK,IAAI,CAAC,eAAe;oBAChD,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,eAAe,SAAS,CAAC,GAAG,MAAM,eAAe,MAAM,GAAG,KAAK,QAAQ,IAAI;oBAE5H,wBAAwB;oBACxB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;oBACpC,OAAO;gBACT;YACF;YAEA,sDAAsD;YACtD,QAAQ,GAAG,CAAC;YACZ,MAAM,sBAAsB,IAAI,CAAC,sBAAsB,CAAC,MAAM,YAAY;YAE1E,IAAI,wBAAwB,MAAM;gBAChC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,oBAAoB,SAAS,CAAC,GAAG,MAAM,oBAAoB,MAAM,GAAG,KAAK,QAAQ,IAAI;gBACvI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;gBACpC,OAAO;YACT;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;QAEA,wCAAwC;QACxC,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU;QACpC,OAAO;IACT;IAEA,8CAA8C;IAC9C,OAAO,uBAAuB,IAAY,EAAE,UAAkB,EAAE,UAAkB,EAAU;QAC1F,gDAAgD;QAChD,MAAM,sBAA8D;YAClE,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,sBAAsB;YACxB;YACA,SAAS;gBACP,WAAW;gBACX,SAAS;gBACT,OAAO;gBACP,SAAS;gBACT,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;YAClB;YACA,SAAS;gBACP,OAAO;gBACP,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,gBAAgB;YAClB;YACA,SAAS;gBACP,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,UAAU;gBACV,YAAY;YACd;QACF;QAEA,MAAM,aAAa,GAAG,WAAW,CAAC,EAAE,YAAY;QAChD,MAAM,WAAW,mBAAmB,CAAC,WAAW;QAEhD,IAAI,UAAU;YACZ,wBAAwB;YACxB,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,OAAO,QAAQ,CAAC,KAAK;YACvB;YAEA,sCAAsC;YACtC,IAAI,iBAAiB;YACrB,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,OAAO,OAAO,CAAC,UAAW;gBACvD,IAAI,KAAK,QAAQ,CAAC,SAAS;oBACzB,iBAAiB,eAAe,OAAO,CAAC,QAAQ;gBAClD;YACF;YAEA,IAAI,mBAAmB,MAAM;gBAC3B,OAAO;YACT;QACF;QAEA,OAAO,MAAM,0CAA0C;IACzD;IAEA,oEAAoE;IACpE,OAAO,oBAAoB,IAAY,EAAgF;QACrH,MAAM,sBAAsB,KAAK,KAAK,CAAC,qBAAqB,EAAE;QAC9D,MAAM,eAAe,oBAAoB,GAAG,CAAC,CAAC,OAAiB,CAAC;gBAC9D;gBACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;YAChF,CAAC;QAED,IAAI,uBAAuB;QAC3B,aAAa,OAAO,CAAC,CAAC;YACpB,uBAAuB,qBAAqB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;QACjF;QAEA,OAAO;YAAE,MAAM;YAAsB;QAAa;IACpD;IAEA,sDAAsD;IACtD,OAAO,oBAAoB,IAAY,EAAE,YAA0D,EAAU;QAC3G,IAAI,eAAe;QACnB,aAAa,OAAO,CAAC,CAAC;YACpB,eAAe,aAAa,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;QACjE;QACA,OAAO;IACT;IAEA,4DAA4D;IAC5D,aAAa,sCACX,IAAY,EACZ,UAAkB,EAClB,UAAkB,EACD;QACjB,MAAM,EAAE,MAAM,oBAAoB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC9E,MAAM,iBAAiB,MAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,YAAY;QAClF,OAAO,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;IAClD;IAEA,gDAAgD;IAChD,aAAa,kBAAkB,QAAa,EAAE,UAAkB,EAAgB;QAC9E,IAAI,CAAC,YAAY,CAAC,YAAY,OAAO;QAErC,MAAM,qBAAqB;YAAE,GAAG,QAAQ;QAAC;QAEzC,IAAI;YACF,0CAA0C;YAC1C,MAAM,aAAa,SAAS,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,WAAW,IAAI;YAEtF,qDAAqD;YACrD,IAAI,eAAe,YAAY;gBAC7B,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,WAAW,uBAAuB,CAAC;gBAC/F,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,IAAI,EAAE,YAAY;YAEzE,wBAAwB;YACxB,IAAI,SAAS,WAAW,EAAE;gBACxB,mBAAmB,WAAW,GAAG,MAAM,IAAI,CAAC,qCAAqC,CAC/E,SAAS,WAAW,EACpB,YACA;YAEJ;YAEA,8BAA8B;YAC9B,IAAI,SAAS,iBAAiB,IAAI,MAAM,OAAO,CAAC,SAAS,iBAAiB,GAAG;gBAC3E,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CACtD,SAAS,iBAAiB,CAAC,GAAG,CAAC,CAAC,WAC9B,IAAI,CAAC,qCAAqC,CAAC,UAAU,YAAY;YAGvE;YAEA,2BAA2B;YAC3B,mBAAmB,mBAAmB,GAAG;YACzC,mBAAmB,eAAe,GAAG;YACrC,mBAAmB,eAAe,GAAG;YACrC,mBAAmB,qBAAqB,GAAG,IAAI,OAAO,WAAW;YAEjE,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,WAAW,IAAI,EAAE,YAAY;YAC9E,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,+CAA+C;YAC/C,OAAO;gBACL,GAAG,QAAQ;gBACX,qBAAqB;gBACrB,mBAAmB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D;QACF;IACF;IAEA,0CAA0C;IAC1C,OAAO,gBAAgB,QAAgB,EAAU;QAC/C,MAAM,gBAAwC;YAC5C,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QAEA,OAAO,aAAa,CAAC,SAAS,IAAI;IACpC;IAEA,sCAAsC;IACtC,OAAO,aAAmB;QACxB,IAAI,CAAC,gBAAgB,CAAC,KAAK;QAC3B,QAAQ,GAAG,CAAC;IACd;IAEA,mCAAmC;IACnC,OAAO,gBAAkD;QACvD,OAAO;YACL,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAChC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,qBAAqB;QACnF;IACF;AACF"}}, {"offset": {"line": 3441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/services/ValidationService.ts"], "sourcesContent": ["export class ValidationService {\r\n  // Function to detect if text is likely Tamil\r\n  static isTamilText(text: string): boolean {\r\n    // Tamil Unicode range: \\u0B80-\\u0BFF\r\n    const tamilRegex = /[\\u0B80-\\u0BFF]/;\r\n    return tamilRegex.test(text);\r\n  }\r\n\r\n  // Function to detect if text is likely Telugu\r\n  static isTeluguText(text: string): boolean {\r\n    // Telugu Unicode range: \\u0C00-\\u0C7F\r\n    const teluguRegex = /[\\u0C00-\\u0C7F]/;\r\n    return teluguRegex.test(text);\r\n  }\r\n\r\n  // Function to detect if text is likely Kannada\r\n  static isKannadaText(text: string): boolean {\r\n    // Kannada Unicode range: \\u0C80-\\u0CFF\r\n    const kannadaRegex = /[\\u0C80-\\u0CFF]/;\r\n    return kannadaRegex.test(text);\r\n  }\r\n\r\n  // Function to validate if text matches the selected language\r\n  static validateLanguageMatch(text: string, language: string): boolean {\r\n    if (!text || text.trim() === \"\") return true; // Empty text is valid for any language\r\n\r\n    // First, remove continuous capital English words (acronyms, proper nouns, etc.)\r\n    // These should be preserved in any language and not affect validation\r\n    const textWithoutCapitalWords = text.replace(/\\b[A-Z]{2,}\\b/g, '');\r\n\r\n    // Check if the remaining text contains characters from different languages\r\n    const hasTamilChars = this.isTamilText(textWithoutCapitalWords);\r\n    const hasTeluguChars = this.isTeluguText(textWithoutCapitalWords);\r\n    const hasKannadaChars = this.isKannadaText(textWithoutCapitalWords);\r\n\r\n    // Count how many different language scripts are present\r\n    const scriptCount = (hasTamilChars ? 1 : 0) + (hasTeluguChars ? 1 : 0) + (hasKannadaChars ? 1 : 0);\r\n\r\n    // If there are multiple scripts, it's likely a mismatch\r\n    if (scriptCount > 1) {\r\n      return false;\r\n    }\r\n\r\n    // English can contain any characters, so we only validate non-English languages\r\n    if (language === \"English\") {\r\n      // For English, we consider it valid if it doesn't contain Tamil, Telugu, or Kannada characters\r\n      return !(hasTamilChars || hasTeluguChars || hasKannadaChars);\r\n    } else if (language === \"Tamil\") {\r\n      // For Tamil, it should contain Tamil characters\r\n      return hasTamilChars || textWithoutCapitalWords.trim() === '';\r\n    } else if (language === \"Telugu\") {\r\n      // For Telugu, it should contain Telugu characters\r\n      return hasTeluguChars || textWithoutCapitalWords.trim() === '';\r\n    } else if (language === \"Kannada\") {\r\n      // For Kannada, it should contain Kannada characters\r\n      return hasKannadaChars || textWithoutCapitalWords.trim() === '';\r\n    }\r\n\r\n    return true; // Default case\r\n  }\r\n\r\n  // Function to detect the language of the text\r\n  static detectLanguage(text: string): string {\r\n    if (this.isTamilText(text)) return \"Tamil\";\r\n    if (this.isTeluguText(text)) return \"Telugu\";\r\n    if (this.isKannadaText(text)) return \"Kannada\";\r\n    return \"English\";\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,MAAM;IACX,6CAA6C;IAC7C,OAAO,YAAY,IAAY,EAAW;QACxC,qCAAqC;QACrC,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,8CAA8C;IAC9C,OAAO,aAAa,IAAY,EAAW;QACzC,sCAAsC;QACtC,MAAM,cAAc;QACpB,OAAO,YAAY,IAAI,CAAC;IAC1B;IAEA,+CAA+C;IAC/C,OAAO,cAAc,IAAY,EAAW;QAC1C,uCAAuC;QACvC,MAAM,eAAe;QACrB,OAAO,aAAa,IAAI,CAAC;IAC3B;IAEA,6DAA6D;IAC7D,OAAO,sBAAsB,IAAY,EAAE,QAAgB,EAAW;QACpE,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI,OAAO,MAAM,uCAAuC;QAErF,gFAAgF;QAChF,sEAAsE;QACtE,MAAM,0BAA0B,KAAK,OAAO,CAAC,kBAAkB;QAE/D,2EAA2E;QAC3E,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC;QACvC,MAAM,iBAAiB,IAAI,CAAC,YAAY,CAAC;QACzC,MAAM,kBAAkB,IAAI,CAAC,aAAa,CAAC;QAE3C,wDAAwD;QACxD,MAAM,cAAc,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC;QAEjG,wDAAwD;QACxD,IAAI,cAAc,GAAG;YACnB,OAAO;QACT;QAEA,gFAAgF;QAChF,IAAI,aAAa,WAAW;YAC1B,+FAA+F;YAC/F,OAAO,CAAC,CAAC,iBAAiB,kBAAkB,eAAe;QAC7D,OAAO,IAAI,aAAa,SAAS;YAC/B,gDAAgD;YAChD,OAAO,iBAAiB,wBAAwB,IAAI,OAAO;QAC7D,OAAO,IAAI,aAAa,UAAU;YAChC,kDAAkD;YAClD,OAAO,kBAAkB,wBAAwB,IAAI,OAAO;QAC9D,OAAO,IAAI,aAAa,WAAW;YACjC,oDAAoD;YACpD,OAAO,mBAAmB,wBAAwB,IAAI,OAAO;QAC/D;QAEA,OAAO,MAAM,eAAe;IAC9B;IAEA,8CAA8C;IAC9C,OAAO,eAAe,IAAY,EAAU;QAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,OAAO;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,OAAO;QACpC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QACrC,OAAO;IACT;AACF"}}, {"offset": {"line": 3509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3515, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/services/CacheService.ts"], "sourcesContent": ["export interface CachedResponse {\r\n  ai_response: string;\r\n  related_questions?: string[];\r\n  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;\r\n  pinecone_indexes?: string[];\r\n  faiss_categories?: any[];\r\n  has_uploaded_content?: boolean;\r\n  upload_sources?: any[];\r\n  timestamp: number;\r\n  query: string;\r\n  context?: string; // For context-aware caching (index, email, etc.)\r\n  language?: string; // Language of the response\r\n  query_language?: string; // Language of the original query\r\n  translation_applied?: boolean; // Whether translation was applied\r\n  translation_metadata?: any; // Translation metadata\r\n}\r\n\r\nexport interface CacheStats {\r\n  totalQueries: number;\r\n  cacheHits: number;\r\n  cacheMisses: number;\r\n  hitRate: number;\r\n  cacheSize: number;\r\n}\r\n\r\nexport class CacheService {\r\n  private static readonly CACHE_KEY = 'financial_query_cache';\r\n  private static readonly MAX_CACHE_SIZE = 100; // Maximum number of cached responses\r\n  private static readonly CACHE_EXPIRY_HOURS = 24; // Cache expires after 24 hours\r\n  private static readonly STATS_KEY = 'financial_query_cache_stats';\r\n  public static readonly ARTIFICIAL_DELAY_MS = 2000; // 2-second delay for cached responses\r\n\r\n  /**\r\n   * Generate a cache key based on query, context, and language\r\n   */\r\n  private static generateCacheKey(query: string, context?: string, language?: string): string {\r\n    const normalizedQuery = query.trim().toLowerCase();\r\n    const contextStr = context || '';\r\n    const languageStr = language || 'en';\r\n    return `${normalizedQuery}|${contextStr}|${languageStr}`;\r\n  }\r\n\r\n  /**\r\n   * Get cached response for a query with language support\r\n   */\r\n  static getCachedResponse(query: string, context?: string, language?: string): CachedResponse | null {\r\n    try {\r\n      if (typeof window === 'undefined') return null;\r\n\r\n      const cacheKey = this.generateCacheKey(query, context, language);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n\r\n      if (!cacheData) {\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);\r\n      const cachedItem = cache[cacheKey];\r\n\r\n      if (!cachedItem) {\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      // Check if cache has expired\r\n      const now = Date.now();\r\n      const expiryTime = cachedItem.timestamp + (this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000);\r\n\r\n      if (now > expiryTime) {\r\n        // Remove expired item\r\n        delete cache[cacheKey];\r\n        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n        this.updateStats('miss');\r\n        return null;\r\n      }\r\n\r\n      this.updateStats('hit');\r\n      console.log(`🎯 Cache HIT for query: \"${query.substring(0, 50)}...\" (Language: ${language || 'default'})`);\r\n      console.log(`⚡ Cached response found - will apply ${this.ARTIFICIAL_DELAY_MS}ms delay for consistent UX`);\r\n      return cachedItem;\r\n    } catch (error) {\r\n      console.error('Error retrieving cached response:', error);\r\n      this.updateStats('miss');\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cache a response for a query with language support\r\n   */\r\n  static setCachedResponse(query: string, response: any, context?: string, language?: string): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const cacheKey = this.generateCacheKey(query, context, language);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n\r\n      let cache: Record<string, CachedResponse> = {};\r\n      if (cacheData) {\r\n        cache = JSON.parse(cacheData);\r\n      }\r\n\r\n      // Create cached response object with language metadata\r\n      const cachedResponse: CachedResponse = {\r\n        ai_response: response.ai_response || '',\r\n        related_questions: response.related_questions || [],\r\n        sentence_analysis: response.sentence_analysis || [],\r\n        pinecone_indexes: response.pinecone_indexes || [],\r\n        faiss_categories: response.faiss_categories || [],\r\n        has_uploaded_content: response.has_uploaded_content,\r\n        upload_sources: response.upload_sources || [],\r\n        timestamp: Date.now(),\r\n        query: query.trim(),\r\n        context: context,\r\n        language: language,\r\n        query_language: response.query_language,\r\n        translation_applied: response.translation_applied || false,\r\n        translation_metadata: response.translation_metadata\r\n      };\r\n\r\n      // Add to cache\r\n      cache[cacheKey] = cachedResponse;\r\n\r\n      // Implement LRU eviction if cache is too large\r\n      const cacheKeys = Object.keys(cache);\r\n      if (cacheKeys.length > this.MAX_CACHE_SIZE) {\r\n        // Sort by timestamp and remove oldest entries\r\n        const sortedEntries = cacheKeys\r\n          .map(key => ({ key, timestamp: cache[key].timestamp }))\r\n          .sort((a, b) => a.timestamp - b.timestamp);\r\n\r\n        // Remove oldest entries to make room\r\n        const entriesToRemove = sortedEntries.slice(0, cacheKeys.length - this.MAX_CACHE_SIZE + 1);\r\n        entriesToRemove.forEach(entry => {\r\n          delete cache[entry.key];\r\n        });\r\n      }\r\n\r\n      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n      console.log(`💾 Cached response for query: \"${query.substring(0, 50)}...\" (Language: ${language || 'default'})`);\r\n    } catch (error) {\r\n      console.error('Error caching response:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update cache statistics\r\n   */\r\n  private static updateStats(type: 'hit' | 'miss'): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const statsData = localStorage.getItem(this.STATS_KEY);\r\n      let stats = {\r\n        totalQueries: 0,\r\n        cacheHits: 0,\r\n        cacheMisses: 0\r\n      };\r\n\r\n      if (statsData) {\r\n        stats = JSON.parse(statsData);\r\n      }\r\n\r\n      stats.totalQueries++;\r\n      if (type === 'hit') {\r\n        stats.cacheHits++;\r\n      } else {\r\n        stats.cacheMisses++;\r\n      }\r\n\r\n      localStorage.setItem(this.STATS_KEY, JSON.stringify(stats));\r\n    } catch (error) {\r\n      console.error('Error updating cache stats:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  static getCacheStats(): CacheStats {\r\n    try {\r\n      if (typeof window === 'undefined') {\r\n        return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };\r\n      }\r\n\r\n      const statsData = localStorage.getItem(this.STATS_KEY);\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n      \r\n      let stats = {\r\n        totalQueries: 0,\r\n        cacheHits: 0,\r\n        cacheMisses: 0\r\n      };\r\n\r\n      if (statsData) {\r\n        stats = JSON.parse(statsData);\r\n      }\r\n\r\n      const cacheSize = cacheData ? Object.keys(JSON.parse(cacheData)).length : 0;\r\n      const hitRate = stats.totalQueries > 0 ? (stats.cacheHits / stats.totalQueries) * 100 : 0;\r\n\r\n      return {\r\n        totalQueries: stats.totalQueries,\r\n        cacheHits: stats.cacheHits,\r\n        cacheMisses: stats.cacheMisses,\r\n        hitRate: Math.round(hitRate * 100) / 100,\r\n        cacheSize\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting cache stats:', error);\r\n      return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clear all cached responses\r\n   */\r\n  static clearCache(): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n      \r\n      localStorage.removeItem(this.CACHE_KEY);\r\n      localStorage.removeItem(this.STATS_KEY);\r\n      console.log('🗑️ Cache cleared successfully');\r\n    } catch (error) {\r\n      console.error('Error clearing cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove expired cache entries\r\n   */\r\n  static cleanupExpiredCache(): void {\r\n    try {\r\n      if (typeof window === 'undefined') return;\r\n\r\n      const cacheData = localStorage.getItem(this.CACHE_KEY);\r\n      if (!cacheData) return;\r\n\r\n      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);\r\n      const now = Date.now();\r\n      const expiryTime = this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000;\r\n\r\n      let removedCount = 0;\r\n      Object.keys(cache).forEach(key => {\r\n        if (now - cache[key].timestamp > expiryTime) {\r\n          delete cache[key];\r\n          removedCount++;\r\n        }\r\n      });\r\n\r\n      if (removedCount > 0) {\r\n        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));\r\n        console.log(`🧹 Cleaned up ${removedCount} expired cache entries`);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error cleaning up expired cache:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Apply artificial delay for cached responses to ensure consistent UX\r\n   */\r\n  static async applyCachedResponseDelay(): Promise<void> {\r\n    console.log(`⏳ Applying ${this.ARTIFICIAL_DELAY_MS}ms artificial delay for cached response...`);\r\n    return new Promise(resolve => {\r\n      setTimeout(() => {\r\n        console.log(`✅ Artificial delay completed - returning cached response`);\r\n        resolve();\r\n      }, this.ARTIFICIAL_DELAY_MS);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get cached response with automatic delay application\r\n   */\r\n  static async getCachedResponseWithDelay(query: string, context?: string, language?: string): Promise<CachedResponse | null> {\r\n    const cachedResponse = this.getCachedResponse(query, context, language);\r\n\r\n    if (cachedResponse) {\r\n      // Apply artificial delay for consistent UX\r\n      await this.applyCachedResponseDelay();\r\n      return cachedResponse;\r\n    }\r\n\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAyBO,MAAM;IACX,OAAwB,YAAY,wBAAwB;IAC5D,OAAwB,iBAAiB,IAAI;IAC7C,OAAwB,qBAAqB,GAAG;IAChD,OAAwB,YAAY,8BAA8B;IAClE,OAAuB,sBAAsB,KAAK;IAElD;;GAEC,GACD,OAAe,iBAAiB,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAU;QAC1F,MAAM,kBAAkB,MAAM,IAAI,GAAG,WAAW;QAChD,MAAM,aAAa,WAAW;QAC9B,MAAM,cAAc,YAAY;QAChC,OAAO,GAAG,gBAAgB,CAAC,EAAE,WAAW,CAAC,EAAE,aAAa;IAC1D;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAyB;QAClG,IAAI;YACF,wCAAmC,OAAO;;YAE1C,MAAM;YACN,MAAM;YAON,MAAM;YACN,MAAM;YAON,6BAA6B;YAC7B,MAAM;YACN,MAAM;QAcR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAa,EAAE,QAAa,EAAE,OAAgB,EAAE,QAAiB,EAAQ;QAChG,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YACN,MAAM;YAEN,IAAI;YAKJ,uDAAuD;YACvD,MAAM;YAoBN,+CAA+C;YAC/C,MAAM;QAgBR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA;;GAEC,GACD,OAAe,YAAY,IAAoB,EAAQ;QACrD,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YACN,IAAI;QAkBN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;GAEC,GACD,OAAO,gBAA4B;QACjC,IAAI;YACF,wCAAmC;gBACjC,OAAO;oBAAE,cAAc;oBAAG,WAAW;oBAAG,aAAa;oBAAG,SAAS;oBAAG,WAAW;gBAAE;YACnF;;YAEA,MAAM;YACN,MAAM;YAEN,IAAI;YAUJ,MAAM;YACN,MAAM;QASR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBAAE,cAAc;gBAAG,WAAW;gBAAG,aAAa;gBAAG,SAAS;gBAAG,WAAW;YAAE;QACnF;IACF;IAEA;;GAEC,GACD,OAAO,aAAmB;QACxB,IAAI;YACF,wCAAmC;;QAKrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA;;GAEC,GACD,OAAO,sBAA4B;QACjC,IAAI;YACF,wCAAmC;;YAEnC,MAAM;YAGN,MAAM;YACN,MAAM;YACN,MAAM;YAEN,IAAI;QAYN,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA;;GAEC,GACD,aAAa,2BAA0C;QACrD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,0CAA0C,CAAC;QAC9F,OAAO,IAAI,QAAQ,CAAA;YACjB,WAAW;gBACT,QAAQ,GAAG,CAAC,CAAC,wDAAwD,CAAC;gBACtE;YACF,GAAG,IAAI,CAAC,mBAAmB;QAC7B;IACF;IAEA;;GAEC,GACD,aAAa,2BAA2B,KAAa,EAAE,OAAgB,EAAE,QAAiB,EAAkC;QAC1H,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,OAAO,SAAS;QAE9D,IAAI,gBAAgB;YAClB,2CAA2C;YAC3C,MAAM,IAAI,CAAC,wBAAwB;YACnC,OAAO;QACT;QAEA,OAAO;IACT;AACF"}}, {"offset": {"line": 3658, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3664, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/debug/ConnectionTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { testConnection, checkBackendHealth, processYouTubeURL } from '../../services/uploadService';\n\ninterface ConnectionTestProps {\n  onClose?: () => void;\n}\n\nconst ConnectionTest: React.FC<ConnectionTestProps> = ({ onClose }) => {\n  const [testResults, setTestResults] = useState<any[]>([]);\n  const [isRunning, setIsRunning] = useState(false);\n\n  const addResult = (test: string, result: any) => {\n    setTestResults(prev => [...prev, { test, result, timestamp: new Date().toISOString() }]);\n  };\n\n  const runTests = async () => {\n    setIsRunning(true);\n    setTestResults([]);\n\n    try {\n      // Test 1: Basic connection\n      console.log('🔌 Running connection test...');\n      const connectionResult = await testConnection();\n      addResult('Connection Test', connectionResult);\n\n      // Test 2: Health check\n      console.log('🏥 Running health check...');\n      const healthResult = await checkBackendHealth();\n      addResult('Health Check', healthResult);\n\n      // Test 3: YouTube URL processing (with a short test video)\n      console.log('🎥 Testing YouTube processing...');\n      const youtubeResult = await processYouTubeURL('https://youtu.be/dQw4w9WgXcQ', {\n        index_name: 'default',\n        client_email: '<EMAIL>'\n      });\n      addResult('YouTube Processing', youtubeResult);\n\n    } catch (error) {\n      console.error('❌ Test suite error:', error);\n      addResult('Test Suite Error', { success: false, error: error.message });\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const clearResults = () => {\n    setTestResults([]);\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto\">\n        <div className=\"flex justify-between items-center mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n            Backend Connection Test\n          </h2>\n          {onClose && (\n            <button\n              onClick={onClose}\n              className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n            >\n              ✕\n            </button>\n          )}\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex gap-2\">\n            <button\n              onClick={runTests}\n              disabled={isRunning}\n              className={`px-4 py-2 rounded-lg text-white font-medium ${\n                isRunning\n                  ? 'bg-gray-400 cursor-not-allowed'\n                  : 'bg-blue-500 hover:bg-blue-600'\n              }`}\n            >\n              {isRunning ? 'Running Tests...' : 'Run Tests'}\n            </button>\n            <button\n              onClick={clearResults}\n              className=\"px-4 py-2 rounded-lg bg-gray-500 hover:bg-gray-600 text-white font-medium\"\n            >\n              Clear Results\n            </button>\n          </div>\n\n          {testResults.length > 0 && (\n            <div className=\"space-y-3\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Test Results\n              </h3>\n              {testResults.map((result, index) => (\n                <div\n                  key={index}\n                  className={`p-4 rounded-lg border ${\n                    result.result.success\n                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'\n                      : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'\n                  }`}\n                >\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <span\n                      className={`w-3 h-3 rounded-full ${\n                        result.result.success ? 'bg-green-500' : 'bg-red-500'\n                      }`}\n                    />\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                      {result.test}\n                    </h4>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {new Date(result.timestamp).toLocaleTimeString()}\n                    </span>\n                  </div>\n                  \n                  {result.result.success ? (\n                    <p className=\"text-green-700 dark:text-green-300\">\n                      ✅ {result.result.message || 'Success'}\n                    </p>\n                  ) : (\n                    <p className=\"text-red-700 dark:text-red-300\">\n                      ❌ {result.result.error || 'Failed'}\n                    </p>\n                  )}\n\n                  {/* Show detailed result data */}\n                  <details className=\"mt-2\">\n                    <summary className=\"cursor-pointer text-sm text-gray-600 dark:text-gray-400\">\n                      Show Details\n                    </summary>\n                    <pre className=\"mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto\">\n                      {JSON.stringify(result.result, null, 2)}\n                    </pre>\n                  </details>\n                </div>\n              ))}\n            </div>\n          )}\n\n          {isRunning && (\n            <div className=\"flex items-center gap-2 text-blue-600 dark:text-blue-400\">\n              <div className=\"animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full\" />\n              <span>Running tests...</span>\n            </div>\n          )}\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n            Debug Information\n          </h4>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n            <p><strong>Backend URL:</strong> http://localhost:5010</p>\n            <p><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</p>\n            <p><strong>User Agent:</strong> {navigator.userAgent}</p>\n            <p><strong>Current Time:</strong> {new Date().toISOString()}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConnectionTest;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY,CAAC,MAAc;QAC/B,eAAe,CAAA,OAAQ;mBAAI;gBAAM;oBAAE;oBAAM;oBAAQ,WAAW,IAAI,OAAO,WAAW;gBAAG;aAAE;IACzF;IAEA,MAAM,WAAW;QACf,aAAa;QACb,eAAe,EAAE;QAEjB,IAAI;YACF,2BAA2B;YAC3B,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,MAAM,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;YAC5C,UAAU,mBAAmB;YAE7B,uBAAuB;YACvB,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD;YAC5C,UAAU,gBAAgB;YAE1B,2DAA2D;YAC3D,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,gCAAgC;gBAC5E,YAAY;gBACZ,cAAc;YAChB;YACA,UAAU,sBAAsB;QAElC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,UAAU,oBAAoB;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QACvE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,eAAe,EAAE;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;wBAG/D,yBACC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAW,CAAC,4CAA4C,EACtD,YACI,mCACA,iCACJ;8CAED,YAAY,qBAAqB;;;;;;8CAEpC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAKF,YAAY,MAAM,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;gCAGnE,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;wCAEC,WAAW,CAAC,sBAAsB,EAChC,OAAO,MAAM,CAAC,OAAO,GACjB,4EACA,mEACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,qBAAqB,EAC/B,OAAO,MAAM,CAAC,OAAO,GAAG,iBAAiB,cACzC;;;;;;kEAEJ,8OAAC;wDAAG,WAAU;kEACX,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;4CAIjD,OAAO,MAAM,CAAC,OAAO,iBACpB,8OAAC;gDAAE,WAAU;;oDAAqC;oDAC7C,OAAO,MAAM,CAAC,OAAO,IAAI;;;;;;qEAG9B,8OAAC;gDAAE,WAAU;;oDAAiC;oDACzC,OAAO,MAAM,CAAC,KAAK,IAAI;;;;;;;0DAK9B,8OAAC;gDAAQ,WAAU;;kEACjB,8OAAC;wDAAQ,WAAU;kEAA0D;;;;;;kEAG7E,8OAAC;wDAAI,WAAU;kEACZ,KAAK,SAAS,CAAC,OAAO,MAAM,EAAE,MAAM;;;;;;;;;;;;;uCArCpC;;;;;;;;;;;wBA6CZ,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAqB;;;;;;;8CAChC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAqB;wCAAE,mDAAwB;;;;;;;8CAC1D,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAoB;wCAAE,UAAU,SAAS;;;;;;;8CACpD,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAsB;wCAAE,IAAI,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrE;uCAEe"}}, {"offset": {"line": 4019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4025, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/ChatBox.tsx"], "sourcesContent": ["import React, { FormEvent, useState, useRef, useEffect, forwardRef, useImperativeHandle } from \"react\";\nimport { usePathname, useRouter } from \"next/navigation\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  PiArrowUp,\n  PiMicrophone,\n  PiPaperclip,\n  PiLightbulb,\n  PiSparkle,\n  PiGlobe,\n  PiStop,\n  PiWaveform,\n  PiPencilSimple,\n  PiCheck,\n  PiSpinner,\n  PiFile,\n  PiFileText,\n  PiMusicNote,\n  PiYoutubeLogo,\n  PiLink,\n  PiX\n} from \"react-icons/pi\";\nimport { useChatHandler } from \"@/stores/chatList\";\nimport SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';\n// Import components that are actually used\nimport ChatInputUpload from \"./ChatInputUpload\";\n// import UploadedContentIndicator from \"./UploadedContentIndicator\";\n// Import services that are actually used\nimport { ApiService } from \"./services/ApiService\";\nimport { TranslationService } from \"./services/TranslationService\";\nimport { ValidationService } from \"./services/ValidationService\";\nimport { CacheService } from \"./services/CacheService\";\n// Import debug component for connection testing\nimport ConnectionTest from \"../debug/ConnectionTest\";\n\n\n\ninterface ChatBoxProps {\n  onLanguageChange?: (language: string) => void;\n}\n\nexport interface ChatBoxRef {\n  setInputFromQuestion: (question: string) => void;\n}\n\nconst ChatBox = forwardRef<ChatBoxRef, ChatBoxProps>(({ onLanguageChange }, ref) => {\n  const [inputText, setInputText] = useState(\"\");\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  // Add language dropup state for responsive design\n  const [showLanguageMenu, setShowLanguageMenu] = useState(false);\n  const [selectedLanguage, setSelectedLanguage] = useState(\"English\");\n  const [isListening, setIsListening] = useState(false);\n  const [speaking, setSpeaking] = useState(false);\n  const [wordCount, setWordCount] = useState(0);\n  // Track recent words for word count\n  const [recentWords, setRecentWords] = useState<string[]>([]);\n  // Add state for transcript editing\n  const [isEditingTranscript, setIsEditingTranscript] = useState(false);\n  const [editedTranscript, setEditedTranscript] = useState(\"\");\n  // Add state for language validation\n  const [languageError, setLanguageError] = useState<string | null>(null);\n  // Add state to track if language buttons should be disabled\n  const [languageButtonsDisabled, setLanguageButtonsDisabled] = useState(false);\n  // Add state for user email from localStorage\n  const [userEmail, setUserEmail] = useState<string | null>(null);\n  // Add state for FAISS indexes - fetch from PINE collection\n  const [pineconeIndexes, setPineconeIndexes] = useState<string[]>([]);\n  const [selectedIndex, setSelectedIndex] = useState<string>('');\n  const [apiEnvironment, setApiEnvironment] = useState<'development' | 'production'>('production');\n  // Add state for loading indexes\n  const [indexesLoading, setIndexesLoading] = useState<boolean>(true);\n  // Add state for index selector dropdown visibility\n  const [showIndexSelector, setShowIndexSelector] = useState<boolean>(false);\n  // Add state for index selection confirmation\n  const [showIndexConfirmation, setShowIndexConfirmation] = useState<boolean>(false);\n  // Add state to track if user has made first request - check localStorage for persistence\n  const [hasUserMadeFirstRequest, setHasUserMadeFirstRequest] = useState<boolean>(() => {\n    if (typeof window !== 'undefined') {\n      const hasFirstRequest = localStorage.getItem('hasUserMadeFirstRequest') === 'true';\n      console.log('🔍 ChatBox: hasUserMadeFirstRequest initialized to:', hasFirstRequest);\n      return hasFirstRequest;\n    }\n    return false;\n  });\n  // Add state to track upload component state\n  const [uploadIsActive, setUploadIsActive] = useState<boolean>(false);\n  const [uploadDropdownVisible, setUploadDropdownVisible] = useState<boolean>(false);\n  // Add state for connection test dialog\n  const [showConnectionTest, setShowConnectionTest] = useState<boolean>(false);\n\n  // Handle upload state changes\n  const handleUploadStateChange = (isActive: boolean, showDropdown: boolean) => {\n    setUploadIsActive(isActive);\n    setUploadDropdownVisible(showDropdown);\n  };\n  // Add state for uploaded content display\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\n  const [uploadedURLs, setUploadedURLs] = useState<Array<{url: string, type: 'youtube' | 'article'}>>([]);\n  const [showUploadedContent, setShowUploadedContent] = useState<boolean>(false);\n  // Add state for response data sources\n  const [responseHasUploadedContent, setResponseHasUploadedContent] = useState<boolean>(false);\n  const [responseUploadSources, setResponseUploadSources] = useState<string[]>([]);\n  // Reference for the index selector dropdown\n  const indexSelectorRef = useRef<HTMLDivElement>(null);\n  const suggestionsRef = useRef<HTMLDivElement>(null);\n  // Add language menu ref for responsive design\n  const languageMenuRef = useRef<HTMLDivElement>(null);\n  const transcriptRef = useRef<HTMLDivElement>(null);\n  const editableTranscriptRef = useRef<HTMLTextAreaElement>(null);\n  const router = useRouter();\n  const path = usePathname();\n  const { userQuery, handleSubmit, addMessage, setUserQuery, isLoading, setIsLoading, chatList } = useChatHandler();\n\n  // Check if current chat has existing messages\n  useEffect(() => {\n    const chatIdUrl = path && path.includes(\"/chat/\") ? path.split(\"/chat/\")[1] : \"\";\n    if (chatIdUrl) {\n      const currentChat = chatList.find(chat => chat.id === chatIdUrl);\n      if (currentChat && currentChat.messages && currentChat.messages.length > 0) {\n        console.log(\"🔍 ChatBox: Found existing chat with messages, setting hasUserMadeFirstRequest to true\");\n        setHasUserMadeFirstRequest(true);\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('hasUserMadeFirstRequest', 'true');\n        }\n      }\n    }\n  }, [chatList, path]);\n\n  // Speech detection timer\n  const speakingTimerRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Expose methods to parent component\n  useImperativeHandle(ref, () => ({\n    setInputFromQuestion: (question: string) => {\n      console.log(\"🎯 ChatBox: setInputFromQuestion called with:\", question);\n      \n      // Update both state variables\n      setInputText(question);\n      setUserQuery(question);\n\n      // Use requestAnimationFrame to ensure DOM updates are complete\n      requestAnimationFrame(() => {\n        const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;\n        if (inputElement) {\n          // Set the value directly on the input element\n          inputElement.value = question;\n          // Create and dispatch input event\n          const inputEvent = new Event('input', { bubbles: true });\n          inputElement.dispatchEvent(inputEvent);\n          // Create and dispatch change event\n          const changeEvent = new Event('change', { bubbles: true });\n          inputElement.dispatchEvent(changeEvent);\n          // Focus the input\n          inputElement.focus();\n          // Set cursor to end of text\n          inputElement.setSelectionRange(question.length, question.length);\n          console.log(\"✅ ChatBox: Input element updated and focused\");\n        } else {\n          console.warn(\"❌ ChatBox: Could not find input element!\");\n        }\n      });\n    }\n  }));\n\n\n\n  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada\n  const languages = [\n    { name: \"English\", code: \"en-US\", color: \"blue\" },\n    { name: \"Tamil\", code: \"ta-IN\", color: \"purple\" },\n    { name: \"Telugu\", code: \"te-IN\", color: \"green\" },\n    { name: \"Kannada\", code: \"kn-IN\", color: \"orange\" }\n  ];\n\n  // Get the language code for the selected language\n  const getLanguageCode = () => {\n    const language = languages.find(lang => lang.name === selectedLanguage);\n    return language ? language.code : \"en-US\"; // Default to English if not found\n  };\n\n  // Initialize with default values for server-side rendering\n  const [transcript, setTranscript] = useState(\"\");\n  const [listening, setListening] = useState(false);\n  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);\n  const [isMicrophoneAvailable, setIsMicrophoneAvailable] = useState(false);\n\n  // Speech recognition setup - only run on client side\n  const {\n    transcript: clientTranscript,\n    listening: clientListening,\n    resetTranscript,\n    browserSupportsSpeechRecognition: clientBrowserSupport,\n    isMicrophoneAvailable: clientMicrophoneAvailable,\n    // We don't need these transcripts as we're using the combined transcript\n    // interimTranscript,\n    // finalTranscript\n  } = typeof window !== 'undefined' ? useSpeechRecognition({\n    clearTranscriptOnListen: false,\n    transcribing: true,\n    commands: [\n      {\n        command: '*',\n        callback: (command) => {\n          console.log('Voice command detected:', command);\n        }\n      }\n    ]\n  }) : {\n    transcript: \"\",\n    listening: false,\n    resetTranscript: () => {},\n    browserSupportsSpeechRecognition: false,\n    isMicrophoneAvailable: false\n  };\n\n  // Handle click outside to close dropdowns\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      // Close suggestions dropdown if clicked outside\n      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {\n        setShowSuggestions(false);\n      }\n\n      // Close language menu if clicked outside\n      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {\n        setShowLanguageMenu(false);\n      }\n\n      // Close index selector if clicked outside\n      if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target as Node)) {\n        setShowIndexSelector(false);\n      }\n    }\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, []);\n\n  // Auto-scroll transcript to bottom when it gets too long\n  useEffect(() => {\n    if (transcriptRef.current && transcript) {\n      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;\n    }\n  }, [transcript]);\n\n  // Update word count when transcript changes\n  useEffect(() => {\n    if (transcript) {\n      const words = transcript.trim().split(/\\s+/).filter(word => word !== \"\");\n      setWordCount(words.length);\n\n      // Track most recent words for animation\n      if (words.length > 0) {\n        const lastWord = words[words.length - 1];\n        if (lastWord && lastWord.length > 0) {\n          setRecentWords((prev: string[]) => {\n            const newWords = [...prev, lastWord];\n            return newWords.slice(-5); // Keep only the last 5 words\n          });\n        }\n      }\n\n      // Set speaking state when new words are detected\n      if (isListening) {\n        setSpeaking(true);\n\n        // Clear previous timer if it exists\n        if (speakingTimerRef.current) {\n          clearTimeout(speakingTimerRef.current);\n        }\n\n        // Set a timer to detect when speaking has paused\n        speakingTimerRef.current = setTimeout(() => {\n          setSpeaking(false);\n        }, 1500); // 1.5 seconds of silence is considered a pause\n      }\n    }\n  }, [transcript, isListening]);\n\n  // Update input text when transcript changes\n  useEffect(() => {\n    // Always update with the latest transcript when listening\n    if (listening || isListening) {\n      setInputText(transcript);\n      setUserQuery(transcript);\n\n      // Also update the edited transcript to match the current transcript\n      // This ensures that when we switch to edit mode, we have the latest transcript\n      setEditedTranscript(transcript);\n\n      console.log(\"Speech recognized:\", transcript);\n\n      if (transcript && !isListening) {\n        console.log(\"Got transcript but isListening was false. Fixing state...\");\n        setIsListening(true);\n      }\n    }\n  }, [transcript, listening, isListening, setUserQuery]);\n\n  // Separate effect to handle listening state changes\n  useEffect(() => {\n    if (isListening) {\n      if (inputText !== \"\") {\n        setInputText(\"\");\n        setUserQuery(\"\");\n      }\n    } else {\n      console.log(\"Stopped listening, transcript:\", transcript);\n\n      if (transcript && transcript.trim() !== \"\") {\n        setInputText(transcript);\n        setUserQuery(transcript);\n      } else if (typeof window !== 'undefined') {\n        resetTranscript();\n      }\n\n      // Reset speaking state and clear any timers\n      setSpeaking(false);\n      if (speakingTimerRef.current) {\n        clearTimeout(speakingTimerRef.current);\n        speakingTimerRef.current = null;\n      }\n\n      // No longer need to close language dropdown\n      // setShowLanguageDropdown(false);\n    }\n\n    console.log(\"Listening state changed:\", isListening);\n  }, [isListening, transcript, inputText]);\n\n  // Update listening state when speech recognition status changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return; // Skip on server-side\n\n    console.log(\"Speech recognition listening state changed:\", listening);\n\n    if (isListening !== listening) {\n      setIsListening(listening);\n    }\n\n    if (!listening && isListening) {\n      console.warn(\"Speech recognition stopped unexpectedly. Checking if we should restart...\");\n\n      // Only attempt to restart if we're still supposed to be listening\n      // This prevents infinite restart loops\n      const restartTimeout = setTimeout(() => {\n        if (isListening && typeof window !== 'undefined') {\n          console.log(\"Attempting to restart speech recognition...\");\n\n          // Try to restart speech recognition\n          const languageCode = getLanguageCode();\n          SpeechRecognition.startListening({\n            continuous: true,\n            language: languageCode,\n            interimResults: true\n          });\n        }\n      }, 1000);\n\n      return () => clearTimeout(restartTimeout);\n    }\n  }, [listening, isListening]);\n\n  // Effect to handle language changes\n  useEffect(() => {\n    if (typeof window === 'undefined') return; // Skip on server-side\n\n    console.log(`Language changed to: ${selectedLanguage}`);\n    // This effect can be used to update any UI elements when language changes\n    // The placeholder text and suggestions are already handled by the render logic\n\n    // If we're listening, we need to restart with the new language\n    if (isListening) {\n      const restartWithNewLanguage = async () => {\n        try {\n          await SpeechRecognition.stopListening();\n          await new Promise(resolve => setTimeout(resolve, 300));\n\n          const languageCode = getLanguageCode();\n          await SpeechRecognition.startListening({\n            continuous: true,\n            language: languageCode,\n            interimResults: true\n          });\n\n          console.log(`Restarted speech recognition with language: ${selectedLanguage} (${languageCode})`);\n        } catch (error) {\n          console.error(\"Error restarting speech recognition with new language:\", error);\n        }\n      };\n\n      restartWithNewLanguage();\n    }\n  }, [selectedLanguage]);\n\n  // Effect to hide index confirmation after 5 seconds\n  useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (showIndexConfirmation) {\n      timer = setTimeout(() => {\n        setShowIndexConfirmation(false);\n      }, 5000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [showIndexConfirmation]);\n\n  // Sample recommended suggestions\n  const englishSuggestions = [\n    \"How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?\",\n    \"How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?\",\n    \"What factors should a retail investor in India consider before investing in IPOs?\",\n    \"How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?\",\n    \"How are fintech startups like Zerodha and Groww changing the investment landscape for young Indians?\"\n  ];\n\n  // Tamil financial suggestions\n  const tamilSuggestions = [\n    \"இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?\",\n    \"நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?\",\n    \"சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?\",\n    \"பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?\",\n    \"இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?\"\n  ];\n\n  // Telugu financial suggestions\n  const teluguSuggestions = [\n    \"భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?\",\n    \"వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?\",\n    \"భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?\",\n    \"రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?\",\n    \"భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?\"\n  ];\n\n  // Kannada financial suggestions\n  const kannadaSuggestions = [\n    \"ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?\",\n    \"ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?\",\n    \"ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?\",\n    \"ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?\",\n    \"ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?\"\n  ];\n\n  // Get suggestions based on selected language\n  const getSuggestionsByLanguage = () => {\n    switch(selectedLanguage) {\n      case \"Tamil\":\n        return tamilSuggestions;\n      case \"Telugu\":\n        return teluguSuggestions;\n      case \"Kannada\":\n        return kannadaSuggestions;\n      default:\n        return englishSuggestions;\n    }\n  };\n\n  const recommendedSuggestions = getSuggestionsByLanguage();\n\n  // Extract chat ID from URL\n  const chatIdUrl = path && path.includes(\"/chat/\") ? path.split(\"/chat/\")[1] : \"\";\n\n  // Handle selecting a suggestion\n  const handleSelectSuggestion = (suggestion: string) => {\n    setInputText(suggestion);\n    setUserQuery(suggestion);\n    setShowSuggestions(false);\n  };\n\n\n  // Function to get language-specific text for uploaded content display\n  const getUploadDisplayText = () => {\n    switch(selectedLanguage) {\n      case \"Tamil\":\n        return {\n          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',\n          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',\n          removeFile: 'கோப்பை அகற்று',\n          removeUrl: 'இணைப்பை அகற்று',\n          pdfDocument: 'PDF ஆவணம்',\n          mp3Audio: 'MP3 ஆடியோ',\n          youtubeVideo: 'YouTube வீடியோ',\n          articleLink: 'கட்டுரை இணைப்பு'\n        };\n      case \"Telugu\":\n        return {\n          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',\n          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',\n          removeFile: 'ఫైల్‌ను తొలగించండి',\n          removeUrl: 'లింక్‌ను తొలగించండి',\n          pdfDocument: 'PDF డాక్యుమెంట్',\n          mp3Audio: 'MP3 ఆడియో',\n          youtubeVideo: 'YouTube వీడియో',\n          articleLink: 'వ్యాసం లింక్'\n        };\n      case \"Kannada\":\n        return {\n          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',\n          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',\n          removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',\n          removeUrl: 'ಲಿಂಕ್ ತೆಗೆದುಹಾಕಿ',\n          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',\n          mp3Audio: 'MP3 ಆಡಿಯೋ',\n          youtubeVideo: 'YouTube ವೀಡಿಯೊ',\n          articleLink: 'ಲೇಖನ ಲಿಂಕ್'\n        };\n      default:\n        return {\n          uploadedFiles: 'Uploaded Files',\n          uploadedUrls: 'Uploaded URLs',\n          removeFile: 'Remove file',\n          removeUrl: 'Remove URL',\n          pdfDocument: 'PDF Document',\n          mp3Audio: 'MP3 Audio',\n          youtubeVideo: 'YouTube Video',\n          articleLink: 'Article Link'\n        };\n    }\n  };\n\n  // Function to get file icon based on file type\n  const getFileIcon = (fileName: string) => {\n    const extension = fileName.split('.').pop()?.toLowerCase();\n\n    // Document files\n    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension || '')) {\n      return <PiFileText className=\"w-5 h-5\" />;\n    }\n\n    // Audio files\n    if (['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'].includes(extension || '')) {\n      return <PiMusicNote className=\"w-5 h-5\" />;\n    }\n\n    // Default file icon\n    return <PiFile className=\"w-5 h-5\" />;\n  };\n\n  // Function to get URL icon based on type\n  const getUrlIcon = (type: 'youtube' | 'article') => {\n    if (type === 'youtube') {\n      return <PiYoutubeLogo className=\"w-5 h-5\" />;\n    }\n    return <PiLink className=\"w-5 h-5\" />;\n  };\n\n  // Function to remove uploaded file\n  const removeUploadedFile = (index: number) => {\n    setUploadedFiles(prev => prev.filter((_, i) => i !== index));\n    if (uploadedFiles.length === 1 && uploadedURLs.length === 0) {\n      setShowUploadedContent(false);\n    }\n  };\n\n  // Function to remove uploaded URL\n  const removeUploadedURL = (index: number) => {\n    setUploadedURLs(prev => prev.filter((_, i) => i !== index));\n    if (uploadedURLs.length === 1 && uploadedFiles.length === 0) {\n      setShowUploadedContent(false);\n    }\n  };\n\n  // Handle selecting a language for voice input and UI\n  const handleSelectLanguage = async (e: React.MouseEvent, language: string) => {\n    // Prevent the event from bubbling up and triggering form submission\n    e.preventDefault();\n    e.stopPropagation();\n\n    setSelectedLanguage(language);\n    // Close language menu when a language is selected\n    setShowLanguageMenu(false);\n\n    // Clear any language error when the user changes the language\n    if (languageError) {\n      setLanguageError(null);\n    }\n\n    // Update placeholder and UI based on language\n    console.log(`Language set to: ${language}`);\n\n    // Notify parent component about language change if callback is provided\n    if (onLanguageChange) {\n      onLanguageChange(language);\n    }\n\n    // If suggestions are showing, close and reopen to refresh the content\n    if (showSuggestions) {\n      setShowSuggestions(false);\n      setTimeout(() => setShowSuggestions(true), 100);\n    }\n\n    // If currently listening, restart with new language\n    if (isListening && typeof window !== 'undefined') {\n      try {\n        await SpeechRecognition.stopListening();\n        console.log(\"Speech recognition stopped for language change\");\n\n        await new Promise(resolve => setTimeout(resolve, 300));\n        await startListening();\n      } catch (error) {\n        console.error(\"Error changing speech recognition language:\", error);\n      }\n    }\n  };\n\n  // Toggle voice recognition\n  const toggleListening = async () => {\n    // Skip on server-side\n    if (typeof window === 'undefined') {\n      console.warn(\"Attempted to toggle listening on server-side\");\n      return;\n    }\n\n    if (isListening) {\n      try {\n        await SpeechRecognition.stopListening();\n        setIsListening(false); // Explicitly set to false to ensure state is updated\n\n        // Don't reset transcript when stopping, so it can be edited\n        // resetTranscript();\n\n        console.log(\"Speech recognition stopped successfully\");\n      } catch (error) {\n        console.error(\"Error stopping speech recognition:\", error);\n      }\n    } else {\n      await startListening();\n    }\n  };\n\n  // Toggle between edit and view modes for the transcript\n  const toggleTranscriptEditMode = () => {\n    if (isEditingTranscript) {\n      // Save the edited transcript\n      if (editedTranscript.trim() !== \"\") {\n        setInputText(editedTranscript);\n        setUserQuery(editedTranscript);\n      }\n      setIsEditingTranscript(false);\n    } else {\n      // Enter edit mode\n      setIsEditingTranscript(true);\n\n      // Focus the editable textarea after a short delay to ensure it's rendered\n      setTimeout(() => {\n        if (editableTranscriptRef.current) {\n          editableTranscriptRef.current.focus();\n        }\n      }, 50);\n    }\n  };\n\n  // Handle changes to the editable transcript\n  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    setEditedTranscript(e.target.value);\n\n    // Update word count\n    const words = e.target.value.trim().split(/\\s+/).filter(word => word !== \"\");\n    setWordCount(words.length);\n\n    // Clear any language error when the user edits the transcript\n    if (languageError) {\n      setLanguageError(null);\n    }\n  };\n\n  // Start listening with the selected language\n  const startListening = async () => {\n    // Skip on server-side\n    if (typeof window === 'undefined') {\n      console.warn(\"Attempted to start listening on server-side\");\n      return;\n    }\n\n    // Only reset transcript if we're starting fresh\n    // If we have edited transcript, keep it\n    if (!editedTranscript || editedTranscript.trim() === \"\") {\n      resetTranscript();\n      setInputText(\"\");\n      setUserQuery(\"\");\n      setWordCount(0);\n      setRecentWords([]);\n    }\n\n    const languageCode = getLanguageCode();\n    console.log(`Starting speech recognition in ${selectedLanguage} (${languageCode})`);\n\n    try {\n      setIsListening(true);\n\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n      if (!browserSupportsSpeechRecognition) {\n        console.error(\"Browser doesn't support speech recognition\");\n        alert(\"Your browser doesn't support speech recognition. Please try using Chrome.\");\n        setIsListening(false);\n        return;\n      }\n\n      if (!isMicrophoneAvailable) {\n        console.error(\"Microphone is not available\");\n        alert(\"Microphone is not available. Please check your microphone permissions.\");\n        setIsListening(false);\n        return;\n      }\n\n      await SpeechRecognition.startListening({\n        continuous: true,\n        language: languageCode,\n        interimResults: true\n      });\n\n      console.log(\"Speech recognition started successfully\");\n\n      setTimeout(() => {\n        if (!listening && typeof window !== 'undefined') {\n          console.warn(\"Speech recognition may not have started properly. Trying again...\");\n          SpeechRecognition.startListening({\n            continuous: true,\n            language: languageCode,\n            interimResults: true\n          });\n        }\n      }, 500);\n\n    } catch (error) {\n      console.error(\"Error starting speech recognition:\", error);\n      setIsListening(false);\n      alert(\"There was an error starting speech recognition. Please try again.\");\n    }\n  };\n\n\n\n\n\n\n\n\n\n\n  const handleSendMessage = async (e: FormEvent) => {\n    e.preventDefault();\n\n    // If we're in edit mode, use the edited transcript\n    // Otherwise, use the transcript if listening, or the input text\n    const textToSend = isEditingTranscript ? editedTranscript : (isListening ? transcript : inputText);\n\n    if (!textToSend || !textToSend.trim()) {\n      return;\n    }\n\n    // Validate that the input text matches the selected language\n    if (!ValidationService.validateLanguageMatch(textToSend, selectedLanguage)) {\n      setLanguageError(\"Please select the proper language or type in the currently selected language.\");\n      return;\n    }\n\n    // Clear any previous language errors\n    setLanguageError(null);\n\n    // If we're in edit mode, exit edit mode\n    if (isEditingTranscript) {\n      setIsEditingTranscript(false);\n    }\n\n    if (isListening && typeof window !== 'undefined') {\n      SpeechRecognition.stopListening();\n      setIsListening(false); // Explicitly set to false\n      setSpeaking(false); // Reset speaking state\n      await new Promise(resolve => setTimeout(resolve, 100));\n    }\n\n    // Close any open dropdowns/popups\n    setShowLanguageMenu(false);\n    setShowSuggestions(false);\n\n    // Disable language buttons during query processing\n    setLanguageButtonsDisabled(true);\n\n    // Mark that user has made their first request and persist it\n    setHasUserMadeFirstRequest(true);\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('hasUserMadeFirstRequest', 'true');\n    }\n\n    setIsLoading(true);\n\n    const currentChatId = chatIdUrl || uuidv4();\n\n    if (!chatIdUrl) {\n      router.push(`/chat/${currentChatId}`);\n    }\n\n    const queryText = textToSend.trim();\n\n    // Determine the language of the query\n    const isTamil = ValidationService.isTamilText(queryText) || selectedLanguage === \"Tamil\";\n    const isTelugu = ValidationService.isTeluguText(queryText) || selectedLanguage === \"Telugu\";\n    const isKannada = ValidationService.isKannadaText(queryText) || selectedLanguage === \"Kannada\";\n\n    // Log language detection for Tamil support\n    if (isTamil) {\n      console.log(`🌏 Tamil language detected - Selected: ${selectedLanguage}, Text detection: ${ValidationService.isTamilText(queryText)}`);\n    }\n\n    // Store the original query for display purposes (commented out as it's not currently used)\n    // const originalQuery = queryText;\n    let translatedQuery = queryText;\n    let needsTranslation = false;\n\n    // Use the financial_query endpoint for all languages\n    // For Tamil, Telugu, and Kannada, we'll translate the query and response\n\n    // Extract continuous capital English words that should not be translated\n    const capitalWordsMatches = queryText.match(/\\b[A-Z]{2,}\\b/g) || [];\n    const capitalWords = capitalWordsMatches.map((word: string) => ({\n      word,\n      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\n    }));\n\n    // Create a version of the query with placeholders for capital words\n    let queryWithPlaceholders = queryText;\n    capitalWords.forEach((item: { word: string, placeholder: string }) => {\n      queryWithPlaceholders = queryWithPlaceholders.replace(item.word, item.placeholder);\n    });\n\n    if (isTamil) {\n      // For Tamil, we need to translate the query to English before sending\n      needsTranslation = true;\n\n      // Translate Tamil to English\n      try {\n        console.log(`Translating Tamil query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"ta\", \"en\");\n\n        // After translation, restore the capital words\n        capitalWords.forEach(item => {\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\n        });\n      } catch (error) {\n        console.error(\"Error translating Tamil query:\", error);\n      }\n    } else if (isTelugu) {\n      // For Telugu, we need to translate the query to English before sending\n      needsTranslation = true;\n\n      // Translate Telugu to English\n      try {\n        console.log(`Translating Telugu query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"te\", \"en\");\n\n        // After translation, restore the capital words\n        capitalWords.forEach(item => {\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\n        });\n      } catch (error) {\n        console.error(\"Error translating Telugu query:\", error);\n      }\n    } else if (isKannada) {\n      // For Kannada, we need to translate the query to English before sending\n      needsTranslation = true;\n\n      // Translate Kannada to English\n      try {\n        console.log(`Translating Kannada query to English (preserving capital words): \"${queryWithPlaceholders}\"`);\n        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, \"kn\", \"en\");\n\n        // After translation, restore the capital words\n        capitalWords.forEach(item => {\n          translatedQuery = translatedQuery.replace(item.placeholder, item.word);\n        });\n      } catch (error) {\n        console.error(\"Error translating Kannada query:\", error);\n      }\n    }\n\n    console.log(`Query detected as ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : isKannada ? 'Kannada' : 'English'}`);\n\n    setInputText(\"\");\n    setUserQuery(\"\");\n    resetTranscript();\n    setWordCount(0);\n    setRecentWords([]);\n\n    // Note: Uploaded content is intentionally NOT cleared here to allow users to send multiple messages\n    // with the same uploaded files/URLs. Content will only be cleared when explicitly removed by user\n    // or when starting a new chat session.\n\n    const userMessageTimestamp = new Date().toISOString();\n\n    // Convert uploaded files to the format expected by the Message interface\n    const messageUploadedFiles = uploadedFiles.map(file => ({\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      lastModified: file.lastModified\n    }));\n\n    addMessage({\n      isUser: true,\n      text: queryText,\n      timestamp: userMessageTimestamp,\n      uploadedFiles: messageUploadedFiles.length > 0 ? messageUploadedFiles : undefined,\n      uploadedURLs: uploadedURLs.length > 0 ? uploadedURLs : undefined\n    }, currentChatId);\n\n    const loadingMessageTimestamp = new Date().toISOString();\n    const loadingMessageId = `loading-${currentChatId}-${Date.now()}`;\n\n    addMessage({\n      isUser: false,\n      text: \"__LOADING__\",\n      timestamp: loadingMessageTimestamp,\n      messageId: loadingMessageId\n    }, currentChatId);\n\n    try {\n      // Use the translated query for Telugu, otherwise use the original query\n      const queryToSend = needsTranslation ? translatedQuery : queryText;\n\n      // Get FAISS configuration from localStorage if available\n      const faissIndexName = typeof window !== 'undefined' ? localStorage.getItem('faiss_index_name') : null;\n      const faissEmbedModel = typeof window !== 'undefined' ? localStorage.getItem('faiss_embed_model') : null;\n      const faissClientEmail = typeof window !== 'undefined' ? localStorage.getItem('faiss_client_email') : null;\n\n      // Prepare request body with all available data including language preference\n      const requestBody: any = {\n        query: queryToSend,\n        language: selectedLanguage  // Add language preference for Tamil support\n      };\n\n      // Add client email if available\n      if (userEmail) {\n        requestBody.client_email = userEmail;\n        requestBody.user_email = userEmail; // Also add for direct FAISS query validation\n        console.log(`Including user email in request: ${userEmail}`);\n      }\n\n      // Determine which index to use for FAISS\n      const indexToUse = selectedIndex || faissIndexName;\n      console.log(`🎯 Selected index from UI: ${selectedIndex}`);\n      console.log(`💾 Stored index from localStorage: ${faissIndexName}`);\n      console.log(`📌 Final index to use: ${indexToUse}`);\n\n      // Add context about uploaded content if any\n      if (uploadedFiles.length > 0 || uploadedURLs.length > 0) {\n        const uploadContext = [];\n\n        if (uploadedFiles.length > 0) {\n          uploadContext.push(`Recently uploaded files: ${uploadedFiles.map(f => f.name).join(', ')}`);\n        }\n\n        if (uploadedURLs.length > 0) {\n          uploadContext.push(`Recently processed URLs: ${uploadedURLs.map(u => u.url).join(', ')}`);\n        }\n\n        requestBody.upload_context = uploadContext.join('. ');\n        requestBody.has_recent_uploads = true;\n        console.log(`📎 Including upload context: ${requestBody.upload_context}`);\n      }\n\n      // For FAISS, we don't need API keys, just the index name\n      if (indexToUse) {\n        requestBody.index_name = indexToUse;\n        console.log(`✅ SENDING REQUEST WITH - Selected FAISS Index: \"${indexToUse}\"`);\n      }\n      // If no index selected, fall back to default index\n      else {\n        requestBody.index_name = 'default';\n        console.log(`🔁 FALLING BACK TO DEFAULT INDEX - Using: \"default\"`);\n        console.log(`💡 Note: Default index is accessible to all users`);\n      }\n\n      // Detect query language for better caching and translation\n      const queryLanguage = TranslationService.detectLanguage(queryToSend);\n      const targetLanguage = selectedLanguage === 'Tamil' ? 'ta' :\n                           selectedLanguage === 'Telugu' ? 'te' :\n                           selectedLanguage === 'Kannada' ? 'kn' : 'en';\n\n      // 🚀 CACHE-FIRST APPROACH: Check cache before making API call with language support\n      const cacheContext = `${requestBody.index_name || 'default'}|${userEmail || 'anonymous'}|${requestBody.upload_context || ''}`;\n      const cachedResponse = await CacheService.getCachedResponseWithDelay(queryToSend, cacheContext, targetLanguage);\n\n      let data;\n      if (cachedResponse) {\n        // Use cached response (delay already applied by getCachedResponseWithDelay)\n        console.log(`⚡ Using cached response for query: \"${queryToSend.substring(0, 50)}...\" (Language: ${targetLanguage})`);\n\n        data = {\n          ai_response: cachedResponse.ai_response,\n          related_questions: cachedResponse.related_questions,\n          sentence_analysis: cachedResponse.sentence_analysis,\n          pinecone_indexes: cachedResponse.pinecone_indexes,\n          faiss_categories: cachedResponse.faiss_categories,\n          has_uploaded_content: cachedResponse.has_uploaded_content,\n          upload_sources: cachedResponse.upload_sources,\n          translation_applied: cachedResponse.translation_applied,\n          query_language: cachedResponse.query_language\n        };\n\n        console.log(`✅ Cached response ready with language support`);\n      } else {\n        // No cache hit - make API call\n        console.log(`🌐 Making API call for query: \"${queryToSend.substring(0, 50)}...\" (Language: ${selectedLanguage})`);\n\n        // Add translation parameters to request if needed\n        if (targetLanguage !== 'en') {\n          requestBody.target_language = targetLanguage;\n          requestBody.enable_translation = true;\n        }\n\n        data = await ApiService.sendQuery(requestBody);\n\n        // Apply client-side translation if backend translation wasn't applied\n        if (!data.translation_applied && targetLanguage !== 'en') {\n          console.log(`🌐 Applying client-side translation to ${targetLanguage}`);\n          data = await TranslationService.translateResponse(data, targetLanguage);\n        }\n\n        // Cache the response for future use with language context\n        CacheService.setCachedResponse(queryToSend, data, cacheContext, targetLanguage);\n      }\n      console.log(\"API Response received:\", data);\n\n      // Explicitly check for related_questions\n      if (data.related_questions) {\n        console.log(\"Found related_questions in API response:\", data.related_questions);\n      } else {\n        console.warn(\"No related_questions found in API response\");\n      }\n\n      // Check if the response includes a list of FAISS indexes/categories\n      if ((data as any).faiss_categories && Array.isArray((data as any).faiss_categories) && (data as any).faiss_categories.length > 0) {\n        console.log(\"Received FAISS categories from API:\", (data as any).faiss_categories);\n        // Update the dropdown options with the available categories\n        setPineconeIndexes((data as any).faiss_categories.map((cat: any) => cat.index_name || cat));\n      }\n\n      let aiResponse = data.ai_response;\n      console.log(\"Original AI Response:\", aiResponse);\n\n      // For Tamil, Telugu, or Kannada, translate the response from English\n      if ((isTamil || isTelugu || isKannada) && aiResponse) {\n        try {\n          // Extract continuous capital English words that should not be translated\n          const responseCapitalWordsMatches = aiResponse.match(/\\b[A-Z]{2,}\\b/g) || [];\n          const responseCapitalWords = responseCapitalWordsMatches.map((word: string) => ({\n            word,\n            placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`\n          }));\n\n          // Create a version of the response with placeholders for capital words\n          let responseWithPlaceholders = aiResponse;\n          responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\n            responseWithPlaceholders = responseWithPlaceholders.replace(item.word, item.placeholder);\n          });\n\n          // Translate the response based on the detected language\n          if (isTamil) {\n            console.log(`Translating response from English to Tamil (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"ta\");\n            aiResponse = translatedResponse;\n\n            // After translation, restore the capital words\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\n            });\n          } else if (isTelugu) {\n            console.log(`Translating response from English to Telugu (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"te\");\n            aiResponse = translatedResponse;\n\n            // After translation, restore the capital words\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\n            });\n          } else if (isKannada) {\n            console.log(`Translating response from English to Kannada (preserving capital words): \"${responseWithPlaceholders.substring(0, 50)}...\"`);\n            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, \"en\", \"kn\");\n            aiResponse = translatedResponse;\n\n            // After translation, restore the capital words\n            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {\n              aiResponse = aiResponse.replace(item.placeholder, item.word);\n            });\n          }\n        } catch (error) {\n          console.error(`Error translating response to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);\n        }\n      }\n\n      console.log(\"AI Response to display:\", aiResponse);\n\n      // Update response data sources state\n      const extendedData = data as any;\n      if (extendedData.has_uploaded_content !== undefined) {\n        setResponseHasUploadedContent(extendedData.has_uploaded_content);\n      }\n      if (extendedData.upload_sources && Array.isArray(extendedData.upload_sources)) {\n        setResponseUploadSources(extendedData.upload_sources);\n      }\n\n      if (data.sentence_analysis && Array.isArray(data.sentence_analysis)) {\n        console.log(\"Sentence analysis data:\", data.sentence_analysis);\n\n        // For Tamil, Telugu, or Kannada, we might want to translate the sentence analysis too\n        if (isTamil || isTelugu || isKannada) {\n          // In a real app, you would translate each sentence and summary\n          // This is just a placeholder for the actual implementation\n          console.log(`Would translate sentence analysis for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);\n\n          // Example of how you might translate each item in a real implementation:\n          // for (let i = 0; i < data.sentence_analysis.length; i++) {\n          //   const item = data.sentence_analysis[i];\n          //   if (item.summary) {\n          //     item.summary = await translateText(item.summary, \"en\", isTamil ? \"ta\" : \"te\");\n          //   }\n          //   if (item.sentence) {\n          //     item.sentence = await translateText(item.sentence, \"en\", isTamil ? \"ta\" : \"te\");\n          //   }\n          // }\n        }\n\n        data.sentence_analysis.forEach((item: { sentence: string; url: string; summary?: string }, index: number) => {\n          const sentence = item.sentence;\n          const url = item.url;\n          const summary = item.summary || \"\";\n          console.log(`Sentence ${index + 1}: ${sentence}`);\n          console.log(`URL ${index + 1}: ${url}`);\n          console.log(`Summary ${index + 1}: ${summary}`);\n        });\n      } else {\n        console.log(\"No sentence_analysis data available.\");\n      }\n\n      // Log related questions if available\n      if (data.related_questions && Array.isArray(data.related_questions)) {\n        console.log(\"Related questions data:\", data.related_questions);\n\n        // For Tamil, Telugu, or Kannada, we might want to translate the related questions too\n        if (isTamil || isTelugu || isKannada) {\n          console.log(`Would translate related questions for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);\n        }\n\n        data.related_questions.forEach((question: string, index: number) => {\n          console.log(`Related Question ${index + 1}: ${question}`);\n        });\n      } else {\n        console.log(\"No related_questions data available.\");\n      }\n\n      if (aiResponse === undefined || aiResponse === null) {\n        console.warn(\"API returned null/undefined ai_response\");\n        handleSubmit(queryText, currentChatId, \"Sorry, I couldn't process your request properly.\");\n        return;\n      }\n\n      // Create a properly structured response object with all fields\n      const responseObject = {\n        ai_response: aiResponse,\n        sentence_analysis: data.sentence_analysis || [],\n        related_questions: data.related_questions || []\n      };\n\n      // Explicitly log the related_questions in the response object\n      console.log(\"Related questions in response object:\", responseObject.related_questions);\n      console.log(\"Sending structured response to handleSubmit:\", responseObject);\n      handleSubmit(queryText, currentChatId, responseObject);\n    } catch (error) {\n      console.error(\"Error fetching AI response:\", error);\n\n      // Provide a more specific error message if it's related to the FAISS index\n      let errorMessage = \"I'm sorry, I couldn't process your request at the moment. Please try again later.\";\n\n      if (error instanceof Error) {\n        const errorText = error.message;\n\n        // Check if the error is related to the FAISS index\n        if (errorText.includes(\"index\") || errorText.includes(\"Index\") || errorText.includes(\"FAISS\")) {\n          errorMessage = errorText;\n          console.log(\"Index-related error detected:\", errorText);\n        }\n      }\n\n      handleSubmit(queryText, currentChatId, errorMessage);\n    } finally {\n      setIsLoading(false);\n      // Re-enable language buttons once the response is received or if there's an error\n      setLanguageButtonsDisabled(false);\n    }\n  };\n\n  // Initialize client-side values after mount to prevent hydration errors\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Update state with client-side values\n      setTranscript(clientTranscript);\n      setListening(clientListening);\n      setBrowserSupportsSpeechRecognition(clientBrowserSupport);\n      setIsMicrophoneAvailable(clientMicrophoneAvailable);\n\n      // Clean up expired cache entries on component mount\n      CacheService.cleanupExpiredCache();\n\n      console.log(\"SpeechRecognition API check:\", {\n        webkitSpeechRecognition: 'webkitSpeechRecognition' in window,\n        SpeechRecognition: 'SpeechRecognition' in window,\n        mozSpeechRecognition: 'mozSpeechRecognition' in window,\n        msSpeechRecognition: 'msSpeechRecognition' in window,\n        clientBrowserSupport\n      });\n\n      // Fetch available indexes and initialize selected index\n      const fetchIndexesAndInitialize = async () => {\n        setIndexesLoading(true);\n        console.log(\"Fetching available FAISS indexes from PINE collection...\");\n\n        try {\n          // Fetch user's available indexes from PINE collection\n          const availableIndexes = await ApiService.fetchUserIndexes();\n          console.log(\"Available indexes for user:\", availableIndexes);\n\n          // Update the indexes state\n          setPineconeIndexes(availableIndexes);\n\n          // Initialize selectedIndex from localStorage, fallback to first available or 'default'\n          const savedIndex = localStorage.getItem('selectedFaissIndex') || localStorage.getItem('faiss_index_name');\n          let indexToSelect = 'default';\n\n          if (savedIndex && availableIndexes.includes(savedIndex)) {\n            indexToSelect = savedIndex;\n          } else if (availableIndexes.includes('default')) {\n            indexToSelect = 'default';\n          } else if (availableIndexes.length > 0) {\n            indexToSelect = availableIndexes[0];\n          }\n\n          setSelectedIndex(indexToSelect);\n          localStorage.setItem('selectedFaissIndex', indexToSelect);\n          localStorage.setItem('faiss_index_name', indexToSelect);\n\n          console.log(`Selected index: ${indexToSelect}`);\n        } catch (error) {\n          console.error(\"Error fetching indexes:\", error);\n          // Fallback to default configuration\n          setPineconeIndexes(['default']);\n          setSelectedIndex('default');\n          localStorage.setItem('selectedFaissIndex', 'default');\n          localStorage.setItem('faiss_index_name', 'default');\n        } finally {\n          setIndexesLoading(false);\n        }\n      };\n\n      fetchIndexesAndInitialize();\n    }\n  }, [clientBrowserSupport, clientMicrophoneAvailable, clientListening, clientTranscript]);\n\n  // Get user email from localStorage and set up API environment\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Get the logged-in user's email from localStorage\n      const email = localStorage.getItem('user_email');\n      setUserEmail(email);\n\n      // Check if we should use the development environment\n      const useDevEnv = localStorage.getItem('use_dev_environment');\n      if (useDevEnv === 'true') {\n        console.log('Using development API endpoint');\n        setApiEnvironment('development');\n      } else {\n        console.log('Using production API endpoint');\n        setApiEnvironment('production');\n      }\n    }\n  }, []);\n\n  // Clear uploaded content when navigating to a different chat\n  useEffect(() => {\n    // Clear uploaded content when chat ID changes (new chat session)\n    setUploadedFiles([]);\n    setUploadedURLs([]);\n    setShowUploadedContent(false);\n    console.log('Cleared uploaded content for new chat session:', chatIdUrl);\n  }, [chatIdUrl]);\n\n  // Check for browser support and microphone availability\n  useEffect(() => {\n    if (!browserSupportsSpeechRecognition && typeof window !== 'undefined') {\n      console.warn(\"Browser doesn't support speech recognition.\");\n    }\n\n    if (!isMicrophoneAvailable && typeof window !== 'undefined') {\n      console.warn(\"Microphone is not available.\");\n    }\n\n    // Cleanup function to ensure microphone is stopped when component unmounts\n    return () => {\n      if (isListening && typeof window !== 'undefined') {\n        console.log(\"Component unmounting, stopping speech recognition\");\n        SpeechRecognition.stopListening();\n      }\n\n      // Clear any timers\n      if (speakingTimerRef.current) {\n        clearTimeout(speakingTimerRef.current);\n        speakingTimerRef.current = null;\n      }\n    };\n  }, [browserSupportsSpeechRecognition, isMicrophoneAvailable, isListening]);\n\n  // Function to log speech recognition state for debugging - uncomment the debug button below to use\n  // const logSpeechRecognitionState = () => {\n  //   console.group(\"Speech Recognition Debug Info\");\n  //   console.log(\"Browser supports speech recognition:\", browserSupportsSpeechRecognition);\n  //   console.log(\"Microphone available:\", isMicrophoneAvailable);\n  //   console.log(\"Current listening state (from hook):\", listening);\n  //   console.log(\"Current isListening state (component):\", isListening);\n  //   console.log(\"Current transcript:\", transcript);\n  //   console.log(\"Selected language:\", selectedLanguage);\n  //   console.log(\"Language code:\", getLanguageCode());\n  //   console.groupEnd();\n  // };\n\n  // Generate sound wave animation elements\n  // Debug log for hasUserMadeFirstRequest state\n  console.log('🔍 ChatBox render: hasUserMadeFirstRequest =', hasUserMadeFirstRequest);\n\n  const renderSoundWave = () => {\n    // Use different colors based on selected language\n    let waveColor = \"bg-red-500\"; // Default color\n\n    if (selectedLanguage === \"Tamil\") {\n      waveColor = \"bg-purple-500\";\n    } else if (selectedLanguage === \"Telugu\") {\n      waveColor = \"bg-green-500\";\n    } else if (selectedLanguage === \"Kannada\") {\n      waveColor = \"bg-orange-500\";\n    }\n\n    return (\n      <div className=\"flex items-center gap-[2px] h-5\">\n        {Array.from({ length: 5 }).map((_, i) => {\n          const isActive = speaking || (i === 2); // Middle bar always active when not speaking\n          const delay = i * 0.1;\n          const duration = isActive ? (0.5 + (i * 0.1)) : 1; // Use deterministic duration\n          const height = isActive ?\n            (i % 2 === 0 ? 10 + (i * 2) : 6 + (i * 1.5)) : // Use deterministic height based on index\n            (i === 2 ? 6 : 4);\n\n          return (\n            <div\n              key={`wave-${i}`} // Use stable key\n              className={`w-1 ${waveColor} rounded-full transition-all`}\n              style={{\n                height: `${height}px`,\n                animationName: isActive ? 'soundWavePulse' : 'none',\n                animationDuration: `${duration}s`,\n                animationIterationCount: 'infinite',\n                animationDirection: 'alternate',\n                animationDelay: `${delay}s`,\n                opacity: isActive ? 1 : 0.5\n              }}\n            ></div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"w-full max-w-[1070px] mx-auto px-2 sm:px-4 md:px-6\">\n      <style jsx global>{`\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(-5px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n        .animate-fadeIn {\n          animation: fadeIn 0.3s ease-out forwards;\n        }\n      `}</style>\n\n\n\n      {isListening && (\n        <div className={`mb-4 p-3 rounded-lg flex items-center justify-between shadow-sm relative z-[10]\n          ${selectedLanguage === \"Tamil\"\n            ? \"bg-gradient-to-r from-purple-50 to-purple-50/70 border border-purple-200\"\n            : selectedLanguage === \"Telugu\"\n              ? \"bg-gradient-to-r from-green-50 to-green-50/70 border border-green-200\"\n              : selectedLanguage === \"Kannada\"\n                ? \"bg-gradient-to-r from-orange-50 to-orange-50/70 border border-orange-200\"\n                : \"bg-gradient-to-r from-red-50 to-red-50/70 border border-red-200\"}`}>\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center bg-white p-1 rounded-full shadow-sm\">\n              {renderSoundWave()}\n            </div>\n            <div>\n              <span className={`text-sm font-medium ${\n                selectedLanguage === \"Tamil\"\n                  ? \"text-purple-700\"\n                  : selectedLanguage === \"Telugu\"\n                    ? \"text-green-700\"\n                    : selectedLanguage === \"Kannada\"\n                      ? \"text-orange-700\"\n                      : \"text-red-700\"\n              }`}>\n                {selectedLanguage === \"Tamil\"\n                  ? \"குரல் பதிவு செயலில் உள்ளது\"\n                  : selectedLanguage === \"Telugu\"\n                    ? \"వాయిస్ రికార్డింగ్ యాక్టివ్\"\n                    : selectedLanguage === \"Kannada\"\n                      ? \"ಧ್ವನಿ ರೆಕಾರ್ಡಿಂಗ್ ಸಕ್ರಿಯವಾಗಿದೆ\"\n                      : \"Voice recording active in\"} <strong>{selectedLanguage}</strong>\n              </span>\n              {wordCount > 0 && (\n                <div className={`text-xs mt-0.5 ${\n                  selectedLanguage === \"Tamil\"\n                    ? \"text-purple-500/80\"\n                    : selectedLanguage === \"Telugu\"\n                      ? \"text-green-500/80\"\n                      : selectedLanguage === \"Kannada\"\n                        ? \"text-orange-500/80\"\n                        : \"text-red-500/80\"\n                }`}>\n                  {selectedLanguage === \"Tamil\"\n                    ? `இதுவரை ${wordCount} சொற்கள் பதிவு செய்யப்பட்டுள்ளன`\n                    : selectedLanguage === \"Telugu\"\n                      ? `ఇప్పటివరకు ${wordCount} పదాలు క్యాప్చర్ చేయబడ్డాయి`\n                      : selectedLanguage === \"Kannada\"\n                        ? `ಇಲ್ಲಿಯವರೆಗೆ ${wordCount} ಪದಗಳನ್ನು ಸೆರೆಹಿಡಿಯಲಾಗಿದೆ`\n                        : `Captured ${wordCount} ${wordCount === 1 ? 'word' : 'words'} so far`}\n                </div>\n              )}\n            </div>\n          </div>\n          <button\n            onClick={() => {\n              toggleListening();\n              // Additional cleanup to ensure UI is reset\n              setIsListening(false);\n              resetTranscript();\n              setSpeaking(false);\n            }}\n            className={`text-xs px-3 py-1.5 bg-white rounded-full transition-colors shadow-sm hover:shadow flex items-center gap-1.5\n              ${selectedLanguage === \"Tamil\"\n                ? \"border border-purple-300 text-purple-600 hover:bg-purple-100\"\n                : selectedLanguage === \"Telugu\"\n                  ? \"border border-green-300 text-green-600 hover:bg-green-100\"\n                  : selectedLanguage === \"Kannada\"\n                    ? \"border border-orange-300 text-orange-600 hover:bg-orange-100\"\n                    : \"border border-red-300 text-red-600 hover:bg-red-100\"}`}\n          >\n            <PiStop className={\n              selectedLanguage === \"Tamil\"\n                ? \"text-purple-600\"\n                : selectedLanguage === \"Telugu\"\n                  ? \"text-green-600\"\n                  : selectedLanguage === \"Kannada\"\n                    ? \"text-orange-600\"\n                    : \"text-red-600\"\n            } />\n            <span>{\n              selectedLanguage === \"Tamil\"\n                ? \"பதிவை மீட்டமை\"\n                : selectedLanguage === \"Telugu\"\n                  ? \"రీసెట్ రికార్డింగ్\"\n                  : selectedLanguage === \"Kannada\"\n                    ? \"ರಿಸೆಟ್ ರೆಕಾರ್ಡಿಂಗ್\"\n                    : \"Reset Recording\"\n            }</span>\n          </button>\n        </div>\n      )}\n      {/* Index Selector and Indicator - Only show after first request */}\n      {hasUserMadeFirstRequest && (\n        <div className=\"w-full flex justify-between items-center mb-2\">\n          {/* Index Selector Dropdown */}\n          <div className=\"relative\" ref={indexSelectorRef}>\n            {/* <button\n              onClick={() => setShowIndexSelector(!showIndexSelector)}\n              disabled={indexesLoading}\n              className=\"px-3 py-1.5 bg-gray-50 dark:bg-gray-800 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {indexesLoading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600\"></div>\n                  <span>Loading...</span>\n                </>\n              ) : (\n                <>\n                  <span>Select Index</span>\n                  <svg className={`w-4 h-4 transition-transform ${showIndexSelector ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </>\n              )}\n            </button> */}\n\n            {showIndexSelector && !indexesLoading && (\n              <div className=\"absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[50] max-h-60 overflow-y-auto\">\n                {pineconeIndexes.length > 0 ? (\n                  pineconeIndexes.map((index) => (\n                    <button\n                      key={index}\n                      onClick={() => {\n                        setSelectedIndex(index);\n                        setShowIndexSelector(false);\n                        setShowIndexConfirmation(true);\n                        localStorage.setItem('selectedFaissIndex', index);\n                        localStorage.setItem('faiss_index_name', index);\n                        console.log(`Selected index: ${index}`);\n                      }}\n                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between\n                        ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}\n                    >\n                      <span>{index}</span>\n                      {selectedIndex === index && (\n                        <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                      )}\n                    </button>\n                  ))\n                ) : (\n                  <div className=\"px-3 py-2 text-sm text-gray-500 dark:text-gray-400\">\n                    No indexes available\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Index Indicator - Shows which index is currently selected */}\n          <div className=\"px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 rounded-md text-xs font-medium text-gray-600 dark:text-gray-400 border border-blue-200 dark:border-blue-800/50 shadow-sm flex items-center gap-1\">\n            <span>Active Category:</span>\n            <span className=\"text-blue-600 dark:text-blue-400 font-semibold\">\n              {selectedIndex || \"Default\"}\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Index Selection Confirmation */}\n      {showIndexConfirmation && (\n        <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg animate-fadeIn\">\n          <div className=\"flex items-center gap-2 text-green-700 dark:text-green-400\">\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"text-sm font-medium\">\n              Index \"{selectedIndex}\" selected! Your responses will be filtered based on this index.\n            </span>\n          </div>\n        </div>\n      )}\n\n      {/* Data Sources Indicator */}\n      {/* <UploadedContentIndicator\n        hasUploadedContent={responseHasUploadedContent}\n        selectedIndex={selectedIndex}\n        uploadSources={responseUploadSources}\n        selectedLanguage={selectedLanguage.toLowerCase()}\n      /> */}\n\n      <form\n        onSubmit={handleSendMessage}\n        className=\"w-full bg-primaryColor/5 p-2 sm:p-3 lg:p-4 rounded-xl border border-primaryColor/20\"\n      >\n        {/* Language validation error message */}\n        {languageError && (\n          <div className=\"mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            {languageError}\n          </div>\n        )}\n\n\n        <div className=\"w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0 relative z-[5]\">\n\n\n          {isListening || transcript ? (\n            <div className=\"w-full p-4 min-h-[56px] max-h-[200px] overflow-y-auto relative flex flex-col\">\n              {isEditingTranscript ? (\n                // Editable textarea for transcript\n                <div className=\"flex-grow relative\">\n                  <textarea\n                    ref={editableTranscriptRef}\n                    className=\"w-full h-full min-h-[80px] p-2 border border-primaryColor/30 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor/50 text-gray-800 resize-none\"\n                    value={editedTranscript}\n                    onChange={handleTranscriptChange}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Enter' && !e.shiftKey) {\n                        e.preventDefault();\n                        // Only submit if there's text to send and not currently loading\n                        if (editedTranscript.trim() && !isLoading) {\n                          // Create a synthetic form event to trigger handleSendMessage\n                          const syntheticEvent = {\n                            preventDefault: () => {},\n                            stopPropagation: () => {},\n                            nativeEvent: e.nativeEvent,\n                            target: e.target,\n                            currentTarget: e.currentTarget,\n                            bubbles: false,\n                            cancelable: false,\n                            defaultPrevented: false,\n                            eventPhase: 0,\n                            isTrusted: false,\n                            timeStamp: Date.now(),\n                            type: 'submit',\n                            isDefaultPrevented: () => false,\n                            isPropagationStopped: () => false,\n                            persist: () => {}\n                          } as unknown as FormEvent;\n                          handleSendMessage(syntheticEvent);\n                        }\n                      }\n                    }}\n                    placeholder=\"Edit your transcribed text here...\"\n                  />\n                  <button\n                    onClick={toggleTranscriptEditMode}\n                    className={`absolute top-2 right-2 p-1.5 rounded-full transition-colors\n                      ${selectedLanguage === \"Tamil\"\n                        ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'\n                        : selectedLanguage === \"Telugu\"\n                          ? 'bg-green-100 text-green-600 hover:bg-green-200'\n                          : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\n                      }`}\n                    title=\"Save edits\"\n                  >\n                    <PiCheck className=\"text-lg\" />\n                  </button>\n                </div>\n              ) : (\n                // View mode for transcript\n                <div\n                  ref={transcriptRef}\n                  className=\"flex-grow text-gray-800 dark:text-white break-words relative\"\n                >\n                  {transcript && transcript.trim() !== \"\" ? (\n                    // Show transcript with animated cursor\n                    <div className=\"relative\">\n                      <p className=\"m-0 leading-relaxed\">\n                        {transcript ? transcript : \"\"}\n                        {speaking && (\n                          <span className=\"inline-block w-1 h-4 bg-primaryColor ml-1\"\n                            style={{\n                              animationName: 'pulse',\n                              animationDuration: '1s',\n                              animationIterationCount: 'infinite',\n                              animationDirection: 'alternate'\n                            }}\n                          ></span>\n                        )}\n                      </p>\n\n                      {/* Action buttons - only show when not actively listening */}\n                      {!isListening && transcript && transcript.trim() !== \"\" && (\n                        <div className=\"absolute top-0 right-0 flex gap-1\">\n                          <button\n                            onClick={() => {\n                              setTranscript(\"\");\n                              setInputText(\"\");\n                              setUserQuery(\"\");\n                              resetTranscript();\n                              setWordCount(0);\n                              setRecentWords([]);\n                            }}\n                            className={`p-1.5 rounded-full transition-colors\n                              ${selectedLanguage === \"Tamil\"\n                                ? 'bg-red-100 text-red-600 hover:bg-red-200'\n                                : selectedLanguage === \"Telugu\"\n                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'\n                                  : 'bg-red-100 text-red-600 hover:bg-red-200'\n                              }`}\n                            title=\"Clear transcript\"\n                          >\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                            </svg>\n                          </button>\n                          <button\n                            onClick={toggleTranscriptEditMode}\n                            className={`p-1.5 rounded-full transition-colors\n                              ${selectedLanguage === \"Tamil\"\n                                ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'\n                                : selectedLanguage === \"Telugu\"\n                                  ? 'bg-green-100 text-green-600 hover:bg-green-200'\n                                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'\n                              }`}\n                            title=\"Edit transcript\"\n                          >\n                            <PiPencilSimple className=\"text-lg\" />\n                          </button>\n                        </div>\n                      )}\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center gap-2 text-gray-400 italic\">\n                      <span>\n                        {selectedLanguage === \"Tamil\"\n                          ? \"தமிழில் கேட்கிறது... இப்போது பேசவும்\"\n                          : selectedLanguage === \"Telugu\"\n                            ? \"తెలుగులో వింటున్నాము... ఇప్పుడు మాట్లాడండి\"\n                            : selectedLanguage === \"Kannada\"\n                              ? \"ಕನ್ನಡದಲ್ಲಿ ಆಲಿಸುತ್ತಿದ್ದೇವೆ... ಈಗ ಮಾತನಾಡಿ\"\n                              : `Listening in ${selectedLanguage}... Speak now`}\n                      </span>\n                      <div className=\"flex space-x-1 ml-2\">\n                        {[0, 1, 2].map((i) => (\n                          <div\n                            key={`dot-${i}`} /* Use stable key */\n                            className={`h-2 w-2 rounded-full ${\n                              selectedLanguage === \"Tamil\"\n                                ? \"bg-purple-500\"\n                                : selectedLanguage === \"Telugu\"\n                                  ? \"bg-green-500\"\n                                  : selectedLanguage === \"Kannada\"\n                                    ? \"bg-orange-500\"\n                                    : \"bg-red-500\"\n                            }`}\n                            style={{\n                              animationName: 'pulse',\n                              animationDuration: '1s',\n                              animationIterationCount: 'infinite',\n                              animationDirection: 'alternate',\n                              animationDelay: `${i * 0.2}s`\n                            }}\n                          ></div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Speaking indicator */}\n              {(transcript && transcript.trim() !== \"\") && (\n                <div className=\"mt-2 flex items-center justify-between text-xs text-gray-500 border-t pt-2 border-gray-100\">\n                  <div className=\"flex items-center gap-2\">\n                    {isEditingTranscript ? (\n                      <span className=\"text-blue-500 flex items-center gap-1\">\n                        <PiPencilSimple />\n                        {selectedLanguage === \"Tamil\"\n                          ? \"திருத்துகிறது\"\n                          : selectedLanguage === \"Telugu\"\n                            ? \"సవరిస్తోంది\"\n                            : selectedLanguage === \"Kannada\"\n                              ? \"ಸಂಪಾದಿಸುತ್ತಿದೆ\"\n                              : \"Editing\"}\n                      </span>\n                    ) : speaking ? (\n                      <span className=\"text-green-500 flex items-center gap-1\">\n                        <PiWaveform className=\"animate-pulse\" />\n                        {selectedLanguage === \"Tamil\"\n                          ? \"செயலில்\"\n                          : selectedLanguage === \"Telugu\"\n                            ? \"యాక్టివ్\"\n                            : selectedLanguage === \"Kannada\"\n                              ? \"ಸಕ್ರಿಯ\"\n                              : \"Active\"}\n                      </span>\n                    ) : (\n                      <span className=\"text-gray-400 flex items-center gap-1\">\n                        <PiWaveform />\n                        {selectedLanguage === \"Tamil\"\n                          ? \"இடைநிறுத்தப்பட்டது\"\n                          : selectedLanguage === \"Telugu\"\n                            ? \"నిలిపివేయబడింది\"\n                            : selectedLanguage === \"Kannada\"\n                              ? \"ವಿರಾಮಗೊಳಿಸಲಾಗಿದೆ\"\n                              : \"Paused\"}\n                      </span>\n                    )}\n                  </div>\n                  <span className=\"bg-gray-100 px-2 py-1 rounded-full text-xs\">\n                    {selectedLanguage === \"Tamil\"\n                      ? `${wordCount} சொற்கள்`\n                      : selectedLanguage === \"Telugu\"\n                        ? `${wordCount} పదాలు`\n                        : selectedLanguage === \"Kannada\"\n                          ? `${wordCount} ಪದಗಳು`\n                          : `${wordCount} words`}\n                  </span>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"relative w-full\">\n              {/* Uploaded content display for regular input mode */}\n              {showUploadedContent && (uploadedFiles.length > 0 || uploadedURLs.length > 0) && (\n                <div className=\"px-4 pt-3 pb-2 border-b border-gray-100 dark:border-gray-700\">\n                  <div className=\"space-y-2\">\n                    {/* Files */}\n                    {uploadedFiles.map((file, index) => (\n                      <div\n                        key={`file-${index}`}\n                        className=\"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300\"\n                      >\n                        <div className={`flex-shrink-0 ${\n                          selectedLanguage === \"Tamil\"\n                            ? 'text-purple-600'\n                            : selectedLanguage === \"Telugu\"\n                              ? 'text-green-600'\n                              : selectedLanguage === \"Kannada\"\n                                ? 'text-orange-600'\n                                : 'text-blue-600'\n                        }`}>\n                          {getFileIcon(file.name)}\n                        </div>\n                        <span className=\"flex-grow truncate font-medium\">\n                          {file.name}\n                        </span>\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          {(file.size / 1024 / 1024).toFixed(1)}MB\n                        </span>\n                        <button\n                          onClick={() => removeUploadedFile(index)}\n                          className=\"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded\"\n                          title={getUploadDisplayText().removeFile}\n                        >\n                          <PiX className=\"w-3 h-3\" />\n                        </button>\n                      </div>\n                    ))}\n\n                    {/* URLs */}\n                    {uploadedURLs.map((urlItem, index) => (\n                      <div\n                        key={`url-${index}`}\n                        className=\"flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300\"\n                      >\n                        <div className={`flex-shrink-0 ${\n                          urlItem.type === 'youtube' ? 'text-red-600' :\n                          selectedLanguage === \"Tamil\"\n                            ? 'text-purple-600'\n                            : selectedLanguage === \"Telugu\"\n                              ? 'text-green-600'\n                              : selectedLanguage === \"Kannada\"\n                                ? 'text-orange-600'\n                                : 'text-blue-600'\n                        }`}>\n                          {getUrlIcon(urlItem.type)}\n                        </div>\n                        <span className=\"flex-grow truncate font-medium\">\n                          {urlItem.type === 'youtube'\n                            ? getUploadDisplayText().youtubeVideo\n                            : getUploadDisplayText().articleLink}\n                        </span>\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 truncate max-w-[100px]\">\n                          {urlItem.url.replace(/^https?:\\/\\//, '')}\n                        </span>\n                        <button\n                          onClick={() => removeUploadedURL(index)}\n                          className=\"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded\"\n                          title={getUploadDisplayText().removeUrl}\n                        >\n                          <PiX className=\"w-3 h-3\" />\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              <div className=\"relative flex items-center w-full\">\n                <input\n                  className=\"w-full outline-none p-4 pr-12 bg-transparent relative z-[1]\"\n                placeholder={\n                  selectedLanguage === \"Tamil\"\n                    ? \"நிதி தொடர்பான கேள்வியை தமிழில் கேளுங்கள்...\"\n                    : selectedLanguage === \"Telugu\"\n                      ? \"ఒక ఆర్థిక విషయంపై నేను ఒక ప్రశ్నను అడగదలుచుకున్నాను...\"\n                      : selectedLanguage === \"Kannada\"\n                        ? \"ಹಣಕಾಸು ವಿಷಯದ ಬಗ್ಗೆ ಪ್ರಶ్ನೆಯನ್ನು ಕೇಳಿ...\"\n                        : \"ask question based financial topic..\"\n                }\n                value={inputText}\n                onChange={(e) => {\n                  setUserQuery(e.target.value);\n                  setInputText(e.target.value);\n                  // Clear any language error when the user starts typing\n                  if (languageError) {\n                    setLanguageError(null);\n                  }\n                }}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter' && !e.shiftKey) {\n                    e.preventDefault();\n                    // Only submit if there's text to send and not currently loading\n                    if (inputText.trim() && !isLoading) {\n                      // Create a synthetic form event to trigger handleSendMessage\n                          const syntheticEvent = {\n                            preventDefault: () => {},\n                            stopPropagation: () => {},\n                            nativeEvent: e.nativeEvent,\n                            target: e.target,\n                            currentTarget: e.currentTarget,\n                            bubbles: false,\n                            cancelable: false,\n                            defaultPrevented: false,\n                            eventPhase: 0,\n                            isTrusted: false,\n                            timeStamp: Date.now(),\n                            type: 'submit',\n                            isDefaultPrevented: () => false,\n                            isPropagationStopped: () => false,\n                            persist: () => {}\n                          } as unknown as FormEvent;\n                      handleSendMessage(syntheticEvent);\n                    }\n                  }\n                }}\n                disabled={isLoading}\n                onClick={() => {\n                  // Close any open dropdowns when clicking in the input field\n                  // No longer need to close language dropdown since we're using horizontal buttons\n                  // setShowLanguageDropdown(false);\n                  setShowSuggestions(false);\n                }}\n              />\n\n              {/* Upload component positioned to the right of the input with improved alignment */}\n                <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 z-[2]\">\n                  <ChatInputUpload\n                    selectedLanguage={selectedLanguage}\n                    disabled={isLoading}\n                    onUploadStateChange={handleUploadStateChange}\n                    onNetworkError={() => setShowConnectionTest(true)}\n                    onFileUpload={(files: File[]) => {\n                      console.log('Files uploaded:', files);\n                      // Only process if we have valid files\n                      if (files && files.length > 0) {\n                        // Prevent duplicates by checking if files already exist\n                        setUploadedFiles(prevFiles => {\n                          const newFiles = files.filter(newFile =>\n                            !prevFiles.some(existingFile =>\n                              existingFile.name === newFile.name &&\n                              existingFile.size === newFile.size &&\n                              existingFile.lastModified === newFile.lastModified\n                            )\n                          );\n                          if (newFiles.length > 0) {\n                            setShowUploadedContent(true);\n                            return [...prevFiles, ...newFiles];\n                          }\n                          return prevFiles;\n                        });\n                      }\n                    }}\n                    onURLSubmit={(url: string, type: 'youtube' | 'article') => {\n                      console.log('URL submitted:', url, 'Type:', type);\n                      // Validate URL before adding\n                      if (url && url.trim()) {\n                        // Prevent duplicate URLs\n                        setUploadedURLs(prev => {\n                          const urlExists = prev.some(existingUrl => existingUrl.url === url.trim());\n                          if (urlExists) {\n                            console.log('URL already exists:', url);\n                            return prev;\n                          }\n                          setShowUploadedContent(true);\n                          return [...prev, { url: url.trim(), type }];\n                        });\n                      }\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 sm:pt-4\">\n          <div className=\"flex justify-start items-center gap-3 relative\">\n            <button\n              type=\"button\"\n              onClick={() => setShowSuggestions(!showSuggestions)}\n              className={`bg-white px-3 py-2 rounded-lg flex items-center gap-2 border\n                ${showSuggestions ? 'border-primaryColor bg-primaryColor/5' : 'border-primaryColor/20'}\n                hover:bg-primaryColor/5 hover:border-primaryColor transition-all shadow-sm hover:shadow dark:bg-n0\n                text-sm font-medium\n                ${selectedLanguage === \"Tamil\"\n                  ? 'text-purple-700'\n                  : selectedLanguage === \"Telugu\"\n                    ? 'text-green-700'\n                    : selectedLanguage === \"Kannada\"\n                      ? 'text-orange-700'\n                      : 'text-gray-700'}\n                dark:text-gray-300`}>\n              <PiLightbulb className={`${showSuggestions\n                ? 'text-primaryColor'\n                : selectedLanguage === \"Tamil\"\n                  ? 'text-purple-500'\n                  : selectedLanguage === \"Telugu\"\n                    ? 'text-green-500'\n                    : selectedLanguage === \"Kannada\"\n                      ? 'text-orange-500'\n                      : 'text-amber-500'}`} />\n              <span>{\n                selectedLanguage === \"Tamil\"\n                  ? 'பரிந்துரைகள்'\n                  : selectedLanguage === \"Telugu\"\n                    ? 'సూచనలు'\n                    : selectedLanguage === \"Kannada\"\n                      ? 'ಸಲಹೆಗಳು'\n                      : 'Suggestions'\n              }</span>\n              <PiSparkle className={`${showSuggestions\n                ? 'text-primaryColor'\n                : selectedLanguage === \"Tamil\"\n                  ? 'text-purple-400'\n                  : selectedLanguage === \"Telugu\"\n                    ? 'text-green-400'\n                    : selectedLanguage === \"Kannada\"\n                      ? 'text-orange-400'\n                      : 'text-amber-400'}\n                ${!showSuggestions ? 'animate-pulse' : ''}`} />\n            </button>\n\n            {/* Suggestions popup with professional styling and animations */}\n            {showSuggestions && (\n              <div\n                className={`fixed inset-0 flex items-center justify-center z-[60] animate-fadeIn ${\n                  selectedLanguage === \"Tamil\"\n                    ? 'bg-gradient-to-br from-purple-900/30 to-black/40'\n                    : selectedLanguage === \"Telugu\"\n                      ? 'bg-gradient-to-br from-green-900/30 to-black/40'\n                      : selectedLanguage === \"Kannada\"\n                        ? 'bg-gradient-to-br from-orange-900/30 to-black/40'\n                        : 'bg-gradient-to-br from-blue-900/30 to-black/40'\n                }`}\n                onClick={() => setShowSuggestions(false)}\n                style={{ backdropFilter: 'blur(3px)', animationDuration: '0.2s' }}\n              >\n                <div\n                  ref={suggestionsRef}\n                  className={`rounded-xl shadow-2xl border max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden animate-scaleIn ${\n                    selectedLanguage === \"Tamil\"\n                      ? 'border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30'\n                      : selectedLanguage === \"Telugu\"\n                        ? 'border-green-200/50 bg-gradient-to-b from-white to-green-50/30'\n                        : selectedLanguage === \"Kannada\"\n                          ? 'border-orange-200/50 bg-gradient-to-b from-white to-orange-50/30'\n                          : 'border-blue-200/50 bg-gradient-to-b from-white to-blue-50/30'\n                  }`}\n                  onClick={(e) => e.stopPropagation()}\n                  style={{\n                    animationDuration: '0.3s',\n                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'\n                  }}\n                >\n                  <div className={`p-5 border-b flex justify-between items-center rounded-t-xl ${\n                    selectedLanguage === \"Tamil\"\n                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'\n                      : selectedLanguage === \"Telugu\"\n                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'\n                        : selectedLanguage === \"Kannada\"\n                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'\n                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'\n                  }`}>\n                    <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n                      <div className={`p-2 rounded-full ${\n                        selectedLanguage === \"Tamil\"\n                          ? 'bg-purple-100'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'bg-green-100'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'bg-orange-100'\n                              : 'bg-blue-100'\n                      }`}>\n                        <PiLightbulb className={`text-xl ${\n                          selectedLanguage === \"Tamil\"\n                            ? 'text-purple-500'\n                            : selectedLanguage === \"Telugu\"\n                              ? 'text-green-500'\n                              : selectedLanguage === \"Kannada\"\n                                ? 'text-orange-500'\n                                : 'text-blue-500'\n                        }`} />\n                      </div>\n                      <span className={\n                        selectedLanguage === \"Tamil\"\n                          ? 'text-purple-800'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'text-green-800'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'text-orange-800'\n                              : 'text-blue-800'\n                      }>\n                        {selectedLanguage === \"Tamil\"\n                          ? 'பரிந்துரைக்கப்பட்ட நிதி கேள்விகள்'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'సిఫార్సు చేయబడిన ఆర్థిక ప్రశ్నలు'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'ಶಿఫಾರಸು ಮಾಡಲಾದ ಹಣಕಾಸು ಪ್ರಶ್ನೆಗಳು'\n                              : 'Recommended Financial Questions'}\n                      </span>\n                    </h3>\n                    <button\n                      onClick={() => setShowSuggestions(false)}\n                      className={`p-2 rounded-full transition-colors ${\n                        selectedLanguage === \"Tamil\"\n                          ? 'bg-purple-100/50 text-purple-500 hover:bg-purple-200 hover:text-purple-700'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'bg-green-100/50 text-green-500 hover:bg-green-200 hover:text-green-700'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'bg-orange-100/50 text-orange-500 hover:bg-orange-200 hover:text-orange-700'\n                              : 'bg-blue-100/50 text-blue-500 hover:bg-blue-200 hover:text-blue-700'\n                      }`}\n                      aria-label=\"Close\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                      </svg>\n                    </button>\n                  </div>\n\n                  <div className=\"p-5 overflow-y-auto max-h-[60vh]\">\n                    <div className={`rounded-lg p-3 mb-5 ${\n                      selectedLanguage === \"Tamil\"\n                        ? 'bg-gradient-to-r from-purple-50 to-white border border-purple-100'\n                        : selectedLanguage === \"Telugu\"\n                          ? 'bg-gradient-to-r from-green-50 to-white border border-green-100'\n                          : selectedLanguage === \"Kannada\"\n                            ? 'bg-gradient-to-r from-orange-50 to-white border border-orange-100'\n                            : 'bg-gradient-to-r from-blue-50 to-white border border-blue-100'\n                    }`}>\n                      <p className={`text-sm font-medium ${\n                        selectedLanguage === \"Tamil\"\n                          ? 'text-purple-700'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'text-green-700'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'text-orange-700'\n                              : 'text-blue-700'\n                      }`}>\n                        {selectedLanguage === \"Tamil\"\n                          ? 'உங்கள் நிதி தேவைகளுக்கான பரிந்துரைக்கப்பட்ட கேள்விகளைப் பார்க்கவும்'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'మీ ఆర్థిక అవసరాలకు సిఫార్సు చేయబడిన ప్రశ్నలను చూడండి'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'ನಿಮ್ಮ ಹಣಕಾಸು ಅಗತ್ಯಗಳಿಗಾಗಿ ಈ ಶಿಫಾರಸು ಮಾಡಲಾದ ಪ್ರಶ್ನೆಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ'\n                              : 'Select from these recommended questions for your financial needs'}\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      {recommendedSuggestions.map((suggestion, index) => (\n                        <button\n                          key={index}\n                          onClick={() => {\n                            handleSelectSuggestion(suggestion);\n                            setShowSuggestions(false);\n                          }}\n                          className={`w-full text-left px-5 py-4 text-sm rounded-lg border shadow-sm hover:shadow-md transition-all\n                            ${selectedLanguage === \"Tamil\"\n                              ? 'border-purple-200 bg-white hover:bg-gradient-to-r hover:from-purple-50 hover:to-white hover:border-purple-300'\n                              : selectedLanguage === \"Telugu\"\n                                ? 'border-green-200 bg-white hover:bg-gradient-to-r hover:from-green-50 hover:to-white hover:border-green-300'\n                                : selectedLanguage === \"Kannada\"\n                                  ? 'border-orange-200 bg-white hover:bg-gradient-to-r hover:from-orange-50 hover:to-white hover:border-orange-300'\n                                  : 'border-blue-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-white hover:border-blue-300'}\n                            text-gray-700 animate-fadeInUp`}\n                          style={{ animationDelay: `${index * 0.05}s`, animationDuration: '0.3s' }}\n                        >\n                          <div className=\"flex items-start\">\n                            <span className={`inline-block p-1.5 rounded-full mr-3 mt-0.5 ${\n                              selectedLanguage === \"Tamil\"\n                                ? 'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600'\n                                : selectedLanguage === \"Telugu\"\n                                  ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600'\n                                  : selectedLanguage === \"Kannada\"\n                                    ? 'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-600'\n                                    : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'\n                            }`}>\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\" clipRule=\"evenodd\" />\n                              </svg>\n                            </span>\n                            <span className=\"font-medium\">{suggestion}</span>\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div className={`p-4 border-t rounded-b-xl text-right ${\n                    selectedLanguage === \"Tamil\"\n                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'\n                      : selectedLanguage === \"Telugu\"\n                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'\n                        : selectedLanguage === \"Kannada\"\n                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'\n                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'\n                  }`}>\n                    <button\n                      onClick={() => setShowSuggestions(false)}\n                      className={`px-5 py-2 rounded-lg text-white font-medium shadow-sm transition-all hover:shadow-md\n                        ${selectedLanguage === \"Tamil\"\n                          ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'\n                              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'}`}\n                    >\n                      {selectedLanguage === \"Tamil\"\n                        ? 'மூடு'\n                        : selectedLanguage === \"Telugu\"\n                          ? 'మూసివేయండి'\n                          : selectedLanguage === \"Kannada\"\n                            ? 'ಮುಚ್ಚಿರಿ'\n                            : 'Close'}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n          <div className=\"flex justify-between items-center gap-4\">\n            {/* Language selection - responsive design */}\n            <div className=\"flex-grow\">\n              {/* Desktop view - horizontal buttons */}\n              <div className=\"hidden md:flex md:items-center\">\n                {/* <div className=\"flex items-center mr-4\">\n                  <PiGlobe className=\"text-gray-600 mr-2 text-lg\" />\n                  <span className=\"text-sm font-medium text-gray-700\">Select Language:</span>\n                </div> */}\n\n                <div className=\"flex space-x-4\">\n                  {/* Show English, Tamil, and Telugu buttons as prominent language selectors on desktop */}\n                  {languages.map((language, index) => {\n                    const isSelected = selectedLanguage === language.name;\n\n                    // Get color classes based on language\n                    const getColorClass = () => {\n                      if (language.name === \"Tamil\") return \"text-purple-700 border-purple-300 bg-purple-50\";\n                      if (language.name === \"Telugu\") return \"text-green-700 border-green-300 bg-green-50\";\n                      if (language.name === \"Kannada\") return \"text-orange-700 border-orange-300 bg-orange-50\";\n                      return \"text-blue-700 border-blue-300 bg-blue-50\";\n                    };\n\n                    const getHoverClass = () => {\n                      if (language.name === \"Tamil\") return \"hover:bg-purple-100 hover:border-purple-400\";\n                      if (language.name === \"Telugu\") return \"hover:bg-green-100 hover:border-green-400\";\n                      if (language.name === \"Kannada\") return \"hover:bg-orange-100 hover:border-orange-400\";\n                      return \"hover:bg-blue-100 hover:border-blue-400\";\n                    };\n\n                    const getIconColor = () => {\n                      if (language.name === \"Tamil\") return \"text-purple-600\";\n                      if (language.name === \"Telugu\") return \"text-green-600\";\n                      if (language.name === \"Kannada\") return \"text-orange-600\";\n                      return \"text-blue-600\";\n                    };\n\n                    const getBorderColor = () => {\n                      if (language.name === \"Tamil\") return \"bg-purple-500\";\n                      if (language.name === \"Telugu\") return \"bg-green-500\";\n                      if (language.name === \"Kannada\") return \"bg-orange-500\";\n                      return \"bg-blue-500\";\n                    };\n\n                    // Using type=\"button\" to prevent form submission when clicking language buttons\n                    return (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        onClick={(e) => handleSelectLanguage(e, language.name)}\n                        disabled={languageButtonsDisabled || isLoading}\n                        className={`px-5 py-2 rounded-lg text-sm font-medium transition-all border relative\n                          ${isSelected\n                            ? getColorClass()\n                            : 'bg-white text-gray-600 border-gray-200 ' + getHoverClass()}\n                          transform transition-all duration-300 ease-in-out\n                          ${isSelected ? 'scale-105 shadow-md' : 'hover:scale-105 hover:shadow-sm'}\n                          ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\n                          animate-fadeIn\n                        `}\n                        style={{\n                          animationDelay: `${index * 0.05}s`,\n                          animationDuration: '0.3s'\n                        }}\n                      >\n                        <div className=\"flex items-center gap-2\">\n                          <PiGlobe className={`${getIconColor()} text-lg ${isSelected ? 'animate-fadeIn' : ''}`}\n                            style={{ animationDuration: '0.3s' }}\n                          />\n                          <span className=\"font-medium\">{language.name}</span>\n                        </div>\n\n                        {/* Bottom border animation for selected language */}\n                        {isSelected && (\n                          <div\n                            className={`absolute bottom-0 left-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}\n                            style={{\n                              width: '100%',\n                              transformOrigin: 'left',\n                              animationDuration: '0.4s'\n                            }}\n                          ></div>\n                        )}\n\n                        {/* Top border animation for selected language */}\n                        {isSelected && (\n                          <div\n                            className={`absolute top-0 right-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}\n                            style={{\n                              width: '100%',\n                              transformOrigin: 'right',\n                              animationDuration: '0.4s',\n                              animationDelay: '0.1s'\n                            }}\n                          ></div>\n                        )}\n                      </button>\n                    );\n                  })}\n                </div>\n              </div>\n\n              {/* Mobile view - dropup menu */}\n              <div className=\"md:hidden relative\" ref={languageMenuRef}>\n                <button\n                  type=\"button\"\n                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}\n                  disabled={languageButtonsDisabled || isLoading}\n                  className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all\n                    ${selectedLanguage === \"Tamil\"\n                      ? \"text-purple-700 border-purple-300 bg-purple-50\"\n                      : selectedLanguage === \"Telugu\"\n                        ? \"text-green-700 border-green-300 bg-green-50\"\n                        : selectedLanguage === \"Kannada\"\n                          ? \"text-orange-700 border-orange-300 bg-orange-50\"\n                          : \"text-blue-700 border-blue-300 bg-blue-50\"\n                    }\n                    hover:shadow-sm\n                    ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\n                  `}\n                >\n                  <PiGlobe className={`text-lg ${\n                    selectedLanguage === \"Tamil\"\n                      ? \"text-purple-600\"\n                      : selectedLanguage === \"Telugu\"\n                        ? \"text-green-600\"\n                        : selectedLanguage === \"Kannada\"\n                          ? \"text-orange-600\"\n                          : \"text-blue-600\"\n                  }`} />\n                  <span className=\"font-medium text-sm\">{selectedLanguage}</span>\n                  <span className={`transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`}>\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 15l7-7 7 7\" />\n                    </svg>\n                  </span>\n                </button>\n\n                {/* Dropup menu */}\n                {showLanguageMenu && (\n                  <div className=\"absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-n0 rounded-lg border shadow-lg z-[40] overflow-hidden animate-fadeIn\"\n                    style={{ animationDuration: '0.2s' }}\n                  >\n                    {languages.map((language, index) => {\n                      const isSelected = selectedLanguage === language.name;\n\n                      // Get color classes based on language\n                      const getItemColorClass = () => {\n                        if (language.name === \"Tamil\") return isSelected ? \"bg-purple-50 text-purple-700\" : \"hover:bg-purple-50\";\n                        if (language.name === \"Telugu\") return isSelected ? \"bg-green-50 text-green-700\" : \"hover:bg-green-50\";\n                        if (language.name === \"Kannada\") return isSelected ? \"bg-orange-50 text-orange-700\" : \"hover:bg-orange-50\";\n                        return isSelected ? \"bg-blue-50 text-blue-700\" : \"hover:bg-blue-50\";\n                      };\n\n                      const getIconColor = () => {\n                        if (language.name === \"Tamil\") return \"text-purple-600\";\n                        if (language.name === \"Telugu\") return \"text-green-600\";\n                        if (language.name === \"Kannada\") return \"text-orange-600\";\n                        return \"text-blue-600\";\n                      };\n\n                      return (\n                        <button\n                          key={index}\n                          type=\"button\"\n                          onClick={(e) => {\n                            handleSelectLanguage(e, language.name);\n                            setShowLanguageMenu(false);\n                          }}\n                          disabled={languageButtonsDisabled || isLoading}\n                          className={`w-full flex items-center gap-2 px-4 py-3 text-left text-sm ${getItemColorClass()} transition-colors\n                            ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}\n                          `}\n                          style={{\n                            animationDelay: `${index * 0.05}s`,\n                          }}\n                        >\n                          <PiGlobe className={`${getIconColor()} text-lg`} />\n                          <span className=\"font-medium\">{language.name}</span>\n                          {isSelected && (\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                            </svg>\n                          )}\n                        </button>\n                      );\n                    })}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Action buttons container with improved spacing */}\n            <div className=\"flex items-center gap-4\">\n              {/* Microphone button with enhanced alignment - hide when upload is active or dropdown is visible */}\n              {!uploadIsActive && !uploadDropdownVisible && (\n                <div className=\"relative z-[3] flex items-center justify-center\">\n                  {browserSupportsSpeechRecognition ? (\n                  <button\n                    type=\"button\"\n                    onClick={isListening ? toggleListening : toggleListening}\n                    className={`p-3 rounded-full flex justify-center items-center border-2 transition-all duration-200 shadow-md hover:shadow-lg relative z-[3] min-w-[48px] min-h-[48px]\n                      ${isListening\n                        ? 'border-red-500 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 scale-105'\n                        : selectedLanguage === \"Tamil\"\n                          ? 'border-purple-400 bg-white hover:bg-purple-50 hover:border-purple-500 hover:scale-105'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'border-green-400 bg-white hover:bg-green-50 hover:border-green-500 hover:scale-105'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'border-orange-400 bg-white hover:bg-orange-50 hover:border-orange-500 hover:scale-105'\n                              : 'border-blue-400 bg-white hover:bg-blue-50 hover:border-blue-500 hover:scale-105'\n                      }\n                      dark:bg-n0 transform`}\n                    title={isListening ? \"Stop voice recording\" : `Start voice recording in ${selectedLanguage}`}\n                  >\n                    {isListening ? (\n                      <div className=\"relative flex items-center justify-center\">\n                        <div\n                          className=\"absolute -inset-2 bg-red-200 rounded-full opacity-60\"\n                          style={{\n                            animationName: 'pulse',\n                            animationDuration: '1.5s',\n                            animationIterationCount: 'infinite',\n                            animationDirection: 'alternate'\n                          }}\n                        ></div>\n                        <PiStop className=\"text-red-600 relative z-[4] w-5 h-5\" />\n                        <span\n                          className=\"absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500\"\n                          style={{\n                            animationName: 'ping',\n                            animationDuration: '1.5s',\n                            animationIterationCount: 'infinite'\n                          }}\n                        ></span>\n                      </div>\n                    ) : (\n                      <PiMicrophone className={`w-5 h-5 ${\n                        selectedLanguage === \"Tamil\"\n                          ? 'text-purple-600'\n                          : selectedLanguage === \"Telugu\"\n                            ? 'text-green-600'\n                            : selectedLanguage === \"Kannada\"\n                              ? 'text-orange-600'\n                              : 'text-blue-600'\n                      }`} />\n                    )}\n                  </button>\n                ) : (\n                  <button\n                    type=\"button\"\n                    className=\"bg-white p-3 rounded-full flex justify-center items-center border-2 border-gray-300 text-gray-400 cursor-not-allowed min-w-[48px] min-h-[48px] shadow-md\"\n                    title=\"Speech recognition not supported in this browser\"\n                    disabled\n                  >\n                    <PiMicrophone className=\"w-5 h-5\" />\n                  </button>\n                )}\n                </div>\n              )}\n\n              {/* Submit button with enhanced alignment */}\n              <button\n                type=\"submit\"\n                className={`rounded-full flex justify-center items-center border-2 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 min-h-[48px]\n                  ${selectedLanguage === \"Tamil\"\n                    ? \"px-5 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border-purple-600\"\n                    : selectedLanguage === \"Telugu\"\n                      ? \"px-5 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-green-600\"\n                      : selectedLanguage === \"Kannada\"\n                        ? \"px-5 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-orange-600\"\n                        : \"px-5 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-blue-600\"}\n                  ${isLoading ? 'opacity-80 cursor-not-allowed scale-100' : ''}`}\n                disabled={isLoading}\n                title={\n                  isLoading\n                    ? selectedLanguage === \"Tamil\"\n                      ? \"அனுப்புகிறது...\"\n                      : selectedLanguage === \"Telugu\"\n                        ? \"పంపుతోంది...\"\n                        : selectedLanguage === \"Kannada\"\n                          ? \"ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ...\"\n                          : \"Sending...\"\n                    : selectedLanguage === \"Tamil\"\n                      ? \"கேள்வியை அனுப்பு\"\n                      : selectedLanguage === \"Telugu\"\n                        ? \"ప్రశ్నను పంపండి\"\n                        : selectedLanguage === \"Kannada\"\n                          ? \"ಪ್ರಶ್ನೆಯನ್ನು ಕಳುಹಿಸಿ\"\n                          : \"Send question\"\n                }\n              >\n                {isLoading ? (\n                  // Loading state with spinner\n                  selectedLanguage === \"Tamil\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      அனுப்புகிறது... <PiSpinner className=\"animate-spin\" />\n                    </span>\n                  ) : selectedLanguage === \"Telugu\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      పంపుతోంది... <PiSpinner className=\"animate-spin\" />\n                    </span>\n                  ) : selectedLanguage === \"Kannada\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ... <PiSpinner className=\"animate-spin\" />\n                    </span>\n                  ) : (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      Sending... <PiSpinner className=\"animate-spin\" />\n                    </span>\n                  )\n                ) : (\n                  // Normal state\n                  selectedLanguage === \"Tamil\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      அனுப்பு <PiArrowUp />\n                    </span>\n                  ) : selectedLanguage === \"Telugu\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      పంపండి <PiArrowUp />\n                    </span>\n                  ) : selectedLanguage === \"Kannada\" ? (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      ಕಳುಹಿಸಿ <PiArrowUp />\n                    </span>\n                  ) : (\n                    <span className=\"text-sm font-medium flex items-center gap-1.5\">\n                      Send <PiArrowUp />\n                    </span>\n                  )\n                )}\n              </button>\n            </div>\n\n            {/* Debug button - only visible in development */}\n            {/* {process.env.NODE_ENV === 'development' && (\n              <button\n                type=\"button\"\n                onClick={() => setShowConnectionTest(true)}\n                className=\"text-xs px-2 py-1 bg-blue-100 border border-blue-300 rounded text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-800\"\n                title=\"Test backend connection\"\n              >\n                🔌 Test Connection\n              </button>\n            )} */}\n          </div>\n        </div>\n      </form>\n\n      {/* Add style for animation and z-index fixes */}\n      <style jsx>{`\n        @keyframes pulse {\n          0% { height: 4px; }\n          100% { height: 16px; }\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* Spinner animation */\n        .animate-spin {\n          animation: spin 1s linear infinite;\n        }\n\n        /* Ensure microphone elements are above other UI elements */\n        .relative.z-50 {\n          position: relative;\n          z-index: 50;\n        }\n\n        /* Fix for overlapping elements */\n        .absolute.z-50 {\n          position: absolute;\n          z-index: 50;\n        }\n\n        /* Ensure input is always clickable */\n        input {\n          position: relative;\n          z-index: 30;\n        }\n\n        /* Style for editable transcript textarea */\n        textarea {\n          font-family: inherit;\n          line-height: 1.5;\n          transition: all 0.2s ease-in-out;\n        }\n\n        /* Animation for edit button */\n        button:hover svg {\n          transform: scale(1.1);\n          transition: transform 0.2s ease-in-out;\n        }\n\n        /* Dropup animations */\n        @keyframes fadeIn {\n          from { opacity: 0; transform: translateY(10px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n\n        .animate-fadeIn {\n          animation: fadeIn 0.2s ease-out forwards;\n        }\n\n        /* Scale in animation for horizontal borders */\n        @keyframes scaleInHorizontal {\n          from { transform: scaleX(0); }\n          to { transform: scaleX(1); }\n        }\n\n        .animate-scaleInHorizontal {\n          animation: scaleInHorizontal 0.4s ease-out forwards;\n        }\n\n        /* Additional animations for upload components */\n        @keyframes fadeInUp {\n          from { opacity: 0; transform: translateY(20px); }\n          to { opacity: 1; transform: translateY(0); }\n        }\n\n        .animate-fadeInUp {\n          animation: fadeInUp 0.3s ease-out forwards;\n        }\n\n        @keyframes scaleIn {\n          from { opacity: 0; transform: scale(0.95); }\n          to { opacity: 1; transform: scale(1); }\n        }\n\n        .animate-scaleIn {\n          animation: scaleIn 0.2s ease-out forwards;\n        }\n      `}</style>\n\n      {/* Connection Test Dialog */}\n      {showConnectionTest && (\n        <ConnectionTest onClose={() => setShowConnectionTest(false)} />\n      )}\n    </div>\n  );\n});\n\nChatBox.displayName = 'ChatBox';\n\nexport default ChatBox;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAqBA;AACA;AACA,2CAA2C;AAC3C;AACA,qEAAqE;AACrE,yCAAyC;AACzC;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AA9BA;AADA;;;;;;;;;;;;;;;AA2CA,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAA4B,CAAC,EAAE,gBAAgB,EAAE,EAAE;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,kDAAkD;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,oCAAoC;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,mCAAmC;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,oCAAoC;IACpC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,4DAA4D;IAC5D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,6CAA6C;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,2DAA2D;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,gCAAgC;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,mDAAmD;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,6CAA6C;IAC7C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,yFAAyF;IACzF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;QAC9E,uCAAmC;;QAInC;QACA,OAAO;IACT;IACA,4CAA4C;IAC5C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5E,uCAAuC;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,8BAA8B;IAC9B,MAAM,0BAA0B,CAAC,UAAmB;QAClD,kBAAkB;QAClB,yBAAyB;IAC3B;IACA,yCAAyC;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqD,EAAE;IACtG,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACxE,sCAAsC;IACtC,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/E,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,8CAA8C;IAC9C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC7C,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAC1D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAE9G,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,QAAQ,KAAK,QAAQ,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG;QAC9E,IAAI,WAAW;YACb,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACtD,IAAI,eAAe,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC1E,QAAQ,GAAG,CAAC;gBACZ,2BAA2B;gBAC3B,uCAAmC;;gBAEnC;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAK;IAEnB,yBAAyB;IACzB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B,sBAAsB,CAAC;gBACrB,QAAQ,GAAG,CAAC,iDAAiD;gBAE7D,8BAA8B;gBAC9B,aAAa;gBACb,aAAa;gBAEb,+DAA+D;gBAC/D,sBAAsB;oBACpB,MAAM,eAAe,SAAS,aAAa,CAAC;oBAC5C,IAAI,cAAc;wBAChB,8CAA8C;wBAC9C,aAAa,KAAK,GAAG;wBACrB,kCAAkC;wBAClC,MAAM,aAAa,IAAI,MAAM,SAAS;4BAAE,SAAS;wBAAK;wBACtD,aAAa,aAAa,CAAC;wBAC3B,mCAAmC;wBACnC,MAAM,cAAc,IAAI,MAAM,UAAU;4BAAE,SAAS;wBAAK;wBACxD,aAAa,aAAa,CAAC;wBAC3B,kBAAkB;wBAClB,aAAa,KAAK;wBAClB,4BAA4B;wBAC5B,aAAa,iBAAiB,CAAC,SAAS,MAAM,EAAE,SAAS,MAAM;wBAC/D,QAAQ,GAAG,CAAC;oBACd,OAAO;wBACL,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF;QACF,CAAC;IAID,sGAAsG;IACtG,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,MAAM;YAAS,OAAO;QAAO;QAChD;YAAE,MAAM;YAAS,MAAM;YAAS,OAAO;QAAS;QAChD;YAAE,MAAM;YAAU,MAAM;YAAS,OAAO;QAAQ;QAChD;YAAE,MAAM;YAAW,MAAM;YAAS,OAAO;QAAS;KACnD;IAED,kDAAkD;IAClD,MAAM,kBAAkB;QACtB,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACtD,OAAO,WAAW,SAAS,IAAI,GAAG,SAAS,kCAAkC;IAC/E;IAEA,2DAA2D;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kCAAkC,oCAAoC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,qDAAqD;IACrD,MAAM,EACJ,YAAY,gBAAgB,EAC5B,WAAW,eAAe,EAC1B,eAAe,EACf,kCAAkC,oBAAoB,EACtD,uBAAuB,yBAAyB,EAIjD,GAAG,6EAWC;QACH,YAAY;QACZ,WAAW;QACX,iBAAiB,KAAO;QACxB,kCAAkC;QAClC,uBAAuB;IACzB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,gDAAgD;YAChD,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACpF,mBAAmB;YACrB;YAEA,yCAAyC;YACzC,IAAI,gBAAgB,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACtF,oBAAoB;YACtB;YAEA,0CAA0C;YAC1C,IAAI,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACxF,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,IAAI,YAAY;YACvC,cAAc,OAAO,CAAC,SAAS,GAAG,cAAc,OAAO,CAAC,YAAY;QACtE;IACF,GAAG;QAAC;KAAW;IAEf,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,MAAM,QAAQ,WAAW,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,SAAS;YACrE,aAAa,MAAM,MAAM;YAEzB,wCAAwC;YACxC,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;gBACxC,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;oBACnC,eAAe,CAAC;wBACd,MAAM,WAAW;+BAAI;4BAAM;yBAAS;wBACpC,OAAO,SAAS,KAAK,CAAC,CAAC,IAAI,6BAA6B;oBAC1D;gBACF;YACF;YAEA,iDAAiD;YACjD,IAAI,aAAa;gBACf,YAAY;gBAEZ,oCAAoC;gBACpC,IAAI,iBAAiB,OAAO,EAAE;oBAC5B,aAAa,iBAAiB,OAAO;gBACvC;gBAEA,iDAAiD;gBACjD,iBAAiB,OAAO,GAAG,WAAW;oBACpC,YAAY;gBACd,GAAG,OAAO,+CAA+C;YAC3D;QACF;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,aAAa,aAAa;YAC5B,aAAa;YACb,aAAa;YAEb,oEAAoE;YACpE,+EAA+E;YAC/E,oBAAoB;YAEpB,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,cAAc,CAAC,aAAa;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAY;QAAW;QAAa;KAAa;IAErD,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,IAAI,cAAc,IAAI;gBACpB,aAAa;gBACb,aAAa;YACf;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,kCAAkC;YAE9C,IAAI,cAAc,WAAW,IAAI,OAAO,IAAI;gBAC1C,aAAa;gBACb,aAAa;YACf,OAAO,uCAAmC;;YAE1C;YAEA,4CAA4C;YAC5C,YAAY;YACZ,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;gBACrC,iBAAiB,OAAO,GAAG;YAC7B;QAEA,4CAA4C;QAC5C,kCAAkC;QACpC;QAEA,QAAQ,GAAG,CAAC,4BAA4B;IAC1C,GAAG;QAAC;QAAa;QAAY;KAAU;IAEvC,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC,QAAQ,sBAAsB;;IA6BnE,GAAG;QAAC;QAAW;KAAY;IAE3B,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC,QAAQ,sBAAsB;;IA4BnE,GAAG;QAAC;KAAiB;IAErB,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,uBAAuB;YACzB,QAAQ,WAAW;gBACjB,yBAAyB;YAC3B,GAAG;QACL;QACA,OAAO;YACL,IAAI,OAAO,aAAa;QAC1B;IACF,GAAG;QAAC;KAAsB;IAE1B,iCAAiC;IACjC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IAED,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;KACD;IAED,gCAAgC;IAChC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,6CAA6C;IAC7C,MAAM,2BAA2B;QAC/B,OAAO;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,yBAAyB;IAE/B,2BAA2B;IAC3B,MAAM,YAAY,QAAQ,KAAK,QAAQ,CAAC,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG;IAE9E,gCAAgC;IAChC,MAAM,yBAAyB,CAAC;QAC9B,aAAa;QACb,aAAa;QACb,mBAAmB;IACrB;IAGA,sEAAsE;IACtE,MAAM,uBAAuB;QAC3B,OAAO;YACL,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,eAAe;oBACf,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,cAAc;oBACd,aAAa;gBACf;QACJ;IACF;IAEA,+CAA+C;IAC/C,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QAE7C,iBAAiB;QACjB,IAAI;YAAC;YAAO;YAAO;YAAQ;YAAO;YAAO;SAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;YACzE,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B;QAEA,cAAc;QACd,IAAI;YAAC;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;YAChF,qBAAO,8OAAC,8IAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,oBAAoB;QACpB,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,yCAAyC;IACzC,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,WAAW;YACtB,qBAAO,8OAAC,8IAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;QAClC;QACA,qBAAO,8OAAC,8IAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,IAAI,cAAc,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,GAAG;YAC3D,uBAAuB;QACzB;IACF;IAEA,kCAAkC;IAClC,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACpD,IAAI,aAAa,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,GAAG;YAC3D,uBAAuB;QACzB;IACF;IAEA,qDAAqD;IACrD,MAAM,uBAAuB,OAAO,GAAqB;QACvD,oEAAoE;QACpE,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,oBAAoB;QACpB,kDAAkD;QAClD,oBAAoB;QAEpB,8DAA8D;QAC9D,IAAI,eAAe;YACjB,iBAAiB;QACnB;QAEA,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU;QAE1C,wEAAwE;QACxE,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;QAEA,sEAAsE;QACtE,IAAI,iBAAiB;YACnB,mBAAmB;YACnB,WAAW,IAAM,mBAAmB,OAAO;QAC7C;QAEA,oDAAoD;QACpD,uCAAkD;;QAUlD;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,sBAAsB;QACtB,wCAAmC;YACjC,QAAQ,IAAI,CAAC;YACb;QACF;;IAiBF;IAEA,wDAAwD;IACxD,MAAM,2BAA2B;QAC/B,IAAI,qBAAqB;YACvB,6BAA6B;YAC7B,IAAI,iBAAiB,IAAI,OAAO,IAAI;gBAClC,aAAa;gBACb,aAAa;YACf;YACA,uBAAuB;QACzB,OAAO;YACL,kBAAkB;YAClB,uBAAuB;YAEvB,0EAA0E;YAC1E,WAAW;gBACT,IAAI,sBAAsB,OAAO,EAAE;oBACjC,sBAAsB,OAAO,CAAC,KAAK;gBACrC;YACF,GAAG;QACL;IACF;IAEA,4CAA4C;IAC5C,MAAM,yBAAyB,CAAC;QAC9B,oBAAoB,EAAE,MAAM,CAAC,KAAK;QAElC,oBAAoB;QACpB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,SAAS;QACzE,aAAa,MAAM,MAAM;QAEzB,8DAA8D;QAC9D,IAAI,eAAe;YACjB,iBAAiB;QACnB;IACF;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,sBAAsB;QACtB,wCAAmC;YACjC,QAAQ,IAAI,CAAC;YACb;QACF;;QAYA,MAAM;IA8CR;IAWA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,mDAAmD;QACnD,gEAAgE;QAChE,MAAM,aAAa,sBAAsB,mBAAoB,cAAc,aAAa;QAExF,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI;YACrC;QACF;QAEA,6DAA6D;QAC7D,IAAI,CAAC,6JAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAAC,YAAY,mBAAmB;YAC1E,iBAAiB;YACjB;QACF;QAEA,qCAAqC;QACrC,iBAAiB;QAEjB,wCAAwC;QACxC,IAAI,qBAAqB;YACvB,uBAAuB;QACzB;QAEA,uCAAkD;;QAKlD;QAEA,kCAAkC;QAClC,oBAAoB;QACpB,mBAAmB;QAEnB,mDAAmD;QACnD,2BAA2B;QAE3B,6DAA6D;QAC7D,2BAA2B;QAC3B,uCAAmC;;QAEnC;QAEA,aAAa;QAEb,MAAM,gBAAgB,aAAa,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;QAExC,IAAI,CAAC,WAAW;YACd,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,eAAe;QACtC;QAEA,MAAM,YAAY,WAAW,IAAI;QAEjC,sCAAsC;QACtC,MAAM,UAAU,6JAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,cAAc,qBAAqB;QACjF,MAAM,WAAW,6JAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,cAAc,qBAAqB;QACnF,MAAM,YAAY,6JAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,cAAc,qBAAqB;QAErF,2CAA2C;QAC3C,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,iBAAiB,kBAAkB,EAAE,6JAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,YAAY;QACvI;QAEA,2FAA2F;QAC3F,mCAAmC;QACnC,IAAI,kBAAkB;QACtB,IAAI,mBAAmB;QAEvB,qDAAqD;QACrD,yEAAyE;QAEzE,yEAAyE;QACzE,MAAM,sBAAsB,UAAU,KAAK,CAAC,qBAAqB,EAAE;QACnE,MAAM,eAAe,oBAAoB,GAAG,CAAC,CAAC,OAAiB,CAAC;gBAC9D;gBACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;YAChF,CAAC;QAED,oEAAoE;QACpE,IAAI,wBAAwB;QAC5B,aAAa,OAAO,CAAC,CAAC;YACpB,wBAAwB,sBAAsB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;QACnF;QAEA,IAAI,SAAS;YACX,sEAAsE;YACtE,mBAAmB;YAEnB,6BAA6B;YAC7B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,gEAAgE,EAAE,sBAAsB,CAAC,CAAC;gBACvG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF,OAAO,IAAI,UAAU;YACnB,uEAAuE;YACvE,mBAAmB;YAEnB,8BAA8B;YAC9B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,iEAAiE,EAAE,sBAAsB,CAAC,CAAC;gBACxG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF,OAAO,IAAI,WAAW;YACpB,wEAAwE;YACxE,mBAAmB;YAEnB,+BAA+B;YAC/B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,kEAAkE,EAAE,sBAAsB,CAAC,CAAC;gBACzG,kBAAkB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,uBAAuB,MAAM;gBAEtF,+CAA+C;gBAC/C,aAAa,OAAO,CAAC,CAAA;oBACnB,kBAAkB,gBAAgB,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,UAAU,WAAW,WAAW,YAAY,YAAY,WAAW;QAE9G,aAAa;QACb,aAAa;QACb;QACA,aAAa;QACb,eAAe,EAAE;QAEjB,oGAAoG;QACpG,kGAAkG;QAClG,uCAAuC;QAEvC,MAAM,uBAAuB,IAAI,OAAO,WAAW;QAEnD,yEAAyE;QACzE,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACtD,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,cAAc,KAAK,YAAY;YACjC,CAAC;QAED,WAAW;YACT,QAAQ;YACR,MAAM;YACN,WAAW;YACX,eAAe,qBAAqB,MAAM,GAAG,IAAI,uBAAuB;YACxE,cAAc,aAAa,MAAM,GAAG,IAAI,eAAe;QACzD,GAAG;QAEH,MAAM,0BAA0B,IAAI,OAAO,WAAW;QACtD,MAAM,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,IAAI;QAEjE,WAAW;YACT,QAAQ;YACR,MAAM;YACN,WAAW;YACX,WAAW;QACb,GAAG;QAEH,IAAI;YACF,wEAAwE;YACxE,MAAM,cAAc,mBAAmB,kBAAkB;YAEzD,yDAAyD;YACzD,MAAM,iBAAiB,6EAA2E;YAClG,MAAM,kBAAkB,6EAA4E;YACpG,MAAM,mBAAmB,6EAA6E;YAEtG,6EAA6E;YAC7E,MAAM,cAAmB;gBACvB,OAAO;gBACP,UAAU,iBAAkB,4CAA4C;YAC1E;YAEA,gCAAgC;YAChC,IAAI,WAAW;gBACb,YAAY,YAAY,GAAG;gBAC3B,YAAY,UAAU,GAAG,WAAW,6CAA6C;gBACjF,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;YAC7D;YAEA,yCAAyC;YACzC,MAAM,aAAa,iBAAiB;YACpC,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,eAAe;YACzD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,gBAAgB;YAClE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY;YAElD,4CAA4C;YAC5C,IAAI,cAAc,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG;gBACvD,MAAM,gBAAgB,EAAE;gBAExB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,cAAc,IAAI,CAAC,CAAC,yBAAyB,EAAE,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;gBAC5F;gBAEA,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,cAAc,IAAI,CAAC,CAAC,yBAAyB,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;gBAC1F;gBAEA,YAAY,cAAc,GAAG,cAAc,IAAI,CAAC;gBAChD,YAAY,kBAAkB,GAAG;gBACjC,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,cAAc,EAAE;YAC1E;YAEA,yDAAyD;YACzD,IAAI,YAAY;gBACd,YAAY,UAAU,GAAG;gBACzB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,WAAW,CAAC,CAAC;YAC9E,OAEK;gBACH,YAAY,UAAU,GAAG;gBACzB,QAAQ,GAAG,CAAC,CAAC,mDAAmD,CAAC;gBACjE,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;YACjE;YAEA,2DAA2D;YAC3D,MAAM,gBAAgB,8JAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YACxD,MAAM,iBAAiB,qBAAqB,UAAU,OACjC,qBAAqB,WAAW,OAChC,qBAAqB,YAAY,OAAO;YAE7D,oFAAoF;YACpF,MAAM,eAAe,GAAG,YAAY,UAAU,IAAI,UAAU,CAAC,EAAE,aAAa,YAAY,CAAC,EAAE,YAAY,cAAc,IAAI,IAAI;YAC7H,MAAM,iBAAiB,MAAM,wJAAA,CAAA,eAAY,CAAC,0BAA0B,CAAC,aAAa,cAAc;YAEhG,IAAI;YACJ,IAAI,gBAAgB;gBAClB,4EAA4E;gBAC5E,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,YAAY,SAAS,CAAC,GAAG,IAAI,gBAAgB,EAAE,eAAe,CAAC,CAAC;gBAEnH,OAAO;oBACL,aAAa,eAAe,WAAW;oBACvC,mBAAmB,eAAe,iBAAiB;oBACnD,mBAAmB,eAAe,iBAAiB;oBACnD,kBAAkB,eAAe,gBAAgB;oBACjD,kBAAkB,eAAe,gBAAgB;oBACjD,sBAAsB,eAAe,oBAAoB;oBACzD,gBAAgB,eAAe,cAAc;oBAC7C,qBAAqB,eAAe,mBAAmB;oBACvD,gBAAgB,eAAe,cAAc;gBAC/C;gBAEA,QAAQ,GAAG,CAAC,CAAC,6CAA6C,CAAC;YAC7D,OAAO;gBACL,+BAA+B;gBAC/B,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,YAAY,SAAS,CAAC,GAAG,IAAI,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;gBAEhH,kDAAkD;gBAClD,IAAI,mBAAmB,MAAM;oBAC3B,YAAY,eAAe,GAAG;oBAC9B,YAAY,kBAAkB,GAAG;gBACnC;gBAEA,OAAO,MAAM,sJAAA,CAAA,aAAU,CAAC,SAAS,CAAC;gBAElC,sEAAsE;gBACtE,IAAI,CAAC,KAAK,mBAAmB,IAAI,mBAAmB,MAAM;oBACxD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,gBAAgB;oBACtE,OAAO,MAAM,8JAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC,MAAM;gBAC1D;gBAEA,0DAA0D;gBAC1D,wJAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,aAAa,MAAM,cAAc;YAClE;YACA,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,yCAAyC;YACzC,IAAI,KAAK,iBAAiB,EAAE;gBAC1B,QAAQ,GAAG,CAAC,4CAA4C,KAAK,iBAAiB;YAChF,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,oEAAoE;YACpE,IAAI,AAAC,KAAa,gBAAgB,IAAI,MAAM,OAAO,CAAC,AAAC,KAAa,gBAAgB,KAAK,AAAC,KAAa,gBAAgB,CAAC,MAAM,GAAG,GAAG;gBAChI,QAAQ,GAAG,CAAC,uCAAuC,AAAC,KAAa,gBAAgB;gBACjF,4DAA4D;gBAC5D,mBAAmB,AAAC,KAAa,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAa,IAAI,UAAU,IAAI;YACxF;YAEA,IAAI,aAAa,KAAK,WAAW;YACjC,QAAQ,GAAG,CAAC,yBAAyB;YAErC,qEAAqE;YACrE,IAAI,CAAC,WAAW,YAAY,SAAS,KAAK,YAAY;gBACpD,IAAI;oBACF,yEAAyE;oBACzE,MAAM,8BAA8B,WAAW,KAAK,CAAC,qBAAqB,EAAE;oBAC5E,MAAM,uBAAuB,4BAA4B,GAAG,CAAC,CAAC,OAAiB,CAAC;4BAC9E;4BACA,aAAa,CAAC,eAAe,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,EAAE,CAAC;wBAChF,CAAC;oBAED,uEAAuE;oBACvE,IAAI,2BAA2B;oBAC/B,qBAAqB,OAAO,CAAC,CAAC;wBAC5B,2BAA2B,yBAAyB,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,WAAW;oBACzF;oBAEA,wDAAwD;oBACxD,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC,CAAC,wEAAwE,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACtI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF,OAAO,IAAI,UAAU;wBACnB,QAAQ,GAAG,CAAC,CAAC,yEAAyE,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACvI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF,OAAO,IAAI,WAAW;wBACpB,QAAQ,GAAG,CAAC,CAAC,0EAA0E,EAAE,yBAAyB,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;wBACxI,MAAM,qBAAqB,MAAM,8JAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,0BAA0B,MAAM;wBAClG,aAAa;wBAEb,+CAA+C;wBAC/C,qBAAqB,OAAO,CAAC,CAAC;4BAC5B,aAAa,WAAW,OAAO,CAAC,KAAK,WAAW,EAAE,KAAK,IAAI;wBAC7D;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,CAAC,CAAC,EAAE;gBACzG;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,qCAAqC;YACrC,MAAM,eAAe;YACrB,IAAI,aAAa,oBAAoB,KAAK,WAAW;gBACnD,8BAA8B,aAAa,oBAAoB;YACjE;YACA,IAAI,aAAa,cAAc,IAAI,MAAM,OAAO,CAAC,aAAa,cAAc,GAAG;gBAC7E,yBAAyB,aAAa,cAAc;YACtD;YAEA,IAAI,KAAK,iBAAiB,IAAI,MAAM,OAAO,CAAC,KAAK,iBAAiB,GAAG;gBACnE,QAAQ,GAAG,CAAC,2BAA2B,KAAK,iBAAiB;gBAE7D,sFAAsF;gBACtF,IAAI,WAAW,YAAY,WAAW;oBACpC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,KAAK,CAAC;gBAE/G,yEAAyE;gBACzE,4DAA4D;gBAC5D,4CAA4C;gBAC5C,wBAAwB;gBACxB,qFAAqF;gBACrF,MAAM;gBACN,yBAAyB;gBACzB,uFAAuF;gBACvF,MAAM;gBACN,IAAI;gBACN;gBAEA,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAA2D;oBACzF,MAAM,WAAW,KAAK,QAAQ;oBAC9B,MAAM,MAAM,KAAK,GAAG;oBACpB,MAAM,UAAU,KAAK,OAAO,IAAI;oBAChC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU;oBAChD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK;oBACtC,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS;gBAChD;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,qCAAqC;YACrC,IAAI,KAAK,iBAAiB,IAAI,MAAM,OAAO,CAAC,KAAK,iBAAiB,GAAG;gBACnE,QAAQ,GAAG,CAAC,2BAA2B,KAAK,iBAAiB;gBAE7D,sFAAsF;gBACtF,IAAI,WAAW,YAAY,WAAW;oBACpC,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,UAAU,UAAU,WAAW,WAAW,UAAU,KAAK,CAAC;gBACjH;gBAEA,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,UAAkB;oBAChD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU;gBAC1D;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,eAAe,aAAa,eAAe,MAAM;gBACnD,QAAQ,IAAI,CAAC;gBACb,aAAa,WAAW,eAAe;gBACvC;YACF;YAEA,+DAA+D;YAC/D,MAAM,iBAAiB;gBACrB,aAAa;gBACb,mBAAmB,KAAK,iBAAiB,IAAI,EAAE;gBAC/C,mBAAmB,KAAK,iBAAiB,IAAI,EAAE;YACjD;YAEA,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,yCAAyC,eAAe,iBAAiB;YACrF,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,aAAa,WAAW,eAAe;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,2EAA2E;YAC3E,IAAI,eAAe;YAEnB,IAAI,iBAAiB,OAAO;gBAC1B,MAAM,YAAY,MAAM,OAAO;gBAE/B,mDAAmD;gBACnD,IAAI,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;oBAC7F,eAAe;oBACf,QAAQ,GAAG,CAAC,iCAAiC;gBAC/C;YACF;YAEA,aAAa,WAAW,eAAe;QACzC,SAAU;YACR,aAAa;YACb,kFAAkF;YAClF,2BAA2B;QAC7B;IACF;IAEA,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QA6DnC;IACF,GAAG;QAAC;QAAsB;QAA2B;QAAiB;KAAiB;IAEvF,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAcnC;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iEAAiE;QACjE,iBAAiB,EAAE;QACnB,gBAAgB,EAAE;QAClB,uBAAuB;QACvB,QAAQ,GAAG,CAAC,kDAAkD;IAChE,GAAG;QAAC;KAAU;IAEd,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAwE;;QAExE;QAEA,uCAA6D;;QAE7D;QAEA,2EAA2E;QAC3E,OAAO;YACL,uCAAkD;;YAGlD;YAEA,mBAAmB;YACnB,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;gBACrC,iBAAiB,OAAO,GAAG;YAC7B;QACF;IACF,GAAG;QAAC;QAAkC;QAAuB;KAAY;IAEzE,mGAAmG;IACnG,4CAA4C;IAC5C,oDAAoD;IACpD,2FAA2F;IAC3F,iEAAiE;IACjE,oEAAoE;IACpE,wEAAwE;IACxE,oDAAoD;IACpD,yDAAyD;IACzD,sDAAsD;IACtD,wBAAwB;IACxB,KAAK;IAEL,yCAAyC;IACzC,8CAA8C;IAC9C,QAAQ,GAAG,CAAC,gDAAgD;IAE5D,MAAM,kBAAkB;QACtB,kDAAkD;QAClD,IAAI,YAAY,cAAc,gBAAgB;QAE9C,IAAI,qBAAqB,SAAS;YAChC,YAAY;QACd,OAAO,IAAI,qBAAqB,UAAU;YACxC,YAAY;QACd,OAAO,IAAI,qBAAqB,WAAW;YACzC,YAAY;QACd;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG;gBACjC,MAAM,WAAW,YAAa,MAAM,GAAI,6CAA6C;gBACrF,MAAM,QAAQ,IAAI;gBAClB,MAAM,WAAW,WAAY,MAAO,IAAI,MAAQ,GAAG,6BAA6B;gBAChF,MAAM,SAAS,WACZ,IAAI,MAAM,IAAI,KAAM,IAAI,IAAK,IAAK,IAAI,MACtC,MAAM,IAAI,IAAI;gBAEjB,qBACE,8OAAC;oBAEC,WAAW,CAAC,IAAI,EAAE,UAAU,4BAA4B,CAAC;oBACzD,OAAO;wBACL,QAAQ,GAAG,OAAO,EAAE,CAAC;wBACrB,eAAe,WAAW,mBAAmB;wBAC7C,mBAAmB,GAAG,SAAS,CAAC,CAAC;wBACjC,yBAAyB;wBACzB,oBAAoB;wBACpB,gBAAgB,GAAG,MAAM,CAAC,CAAC;wBAC3B,SAAS,WAAW,IAAI;oBAC1B;mBAVK,CAAC,KAAK,EAAE,GAAG;;;;;YAatB;;;;;;IAGN;IAEA,qBACE,8OAAC;kDAAc;;;;;;YAaZ,6BACC,8OAAC;0DAAe,CAAC;UACf,EAAE,qBAAqB,UACnB,6EACA,qBAAqB,WACnB,0EACA,qBAAqB,YACnB,6EACA,mEAAmE;;kCAC3E,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;0CACZ;;;;;;0CAEH,8OAAC;;;kDACC,8OAAC;kFAAgB,CAAC,oBAAoB,EACpC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,gBACR;;4CACC,qBAAqB,UAClB,+BACA,qBAAqB,WACnB,gCACA,qBAAqB,YACnB,mCACA;4CAA4B;0DAAC,8OAAC;;0DAAQ;;;;;;;;;;;;oCAE/C,YAAY,mBACX,8OAAC;kFAAe,CAAC,eAAe,EAC9B,qBAAqB,UACjB,uBACA,qBAAqB,WACnB,sBACA,qBAAqB,YACnB,uBACA,mBACR;kDACC,qBAAqB,UAClB,CAAC,OAAO,EAAE,UAAU,+BAA+B,CAAC,GACpD,qBAAqB,WACnB,CAAC,WAAW,EAAE,UAAU,2BAA2B,CAAC,GACpD,qBAAqB,YACnB,CAAC,YAAY,EAAE,UAAU,yBAAyB,CAAC,GACnD,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,cAAc,IAAI,SAAS,QAAQ,OAAO,CAAC;;;;;;;;;;;;;;;;;;kCAKpF,8OAAC;wBACC,SAAS;4BACP;4BACA,2CAA2C;4BAC3C,eAAe;4BACf;4BACA,YAAY;wBACd;kEACW,CAAC;cACV,EAAE,qBAAqB,UACnB,iEACA,qBAAqB,WACnB,8DACA,qBAAqB,YACnB,iEACA,uDAAuD;;0CAEjE,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WACN,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA;;;;;;0CAEV,8OAAC;;0CACC,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,uBACA,qBAAqB,YACnB,uBACA;;;;;;;;;;;;;;;;;;YAMf,yCACC,8OAAC;0DAAc;;kCAEb,8OAAC;wBAAyB,KAAK;kEAAhB;kCAqBZ,qBAAqB,CAAC,gCACrB,8OAAC;sEAAc;sCACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,sBACnB,8OAAC;oCAEC,SAAS;wCACP,iBAAiB;wCACjB,qBAAqB;wCACrB,yBAAyB;wCACzB,aAAa,OAAO,CAAC,sBAAsB;wCAC3C,aAAa,OAAO,CAAC,oBAAoB;wCACzC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO;oCACxC;8EACW,CAAC;wBACV,EAAE,kBAAkB,QAAQ,oEAAoE,oCAAoC;;sDAEtI,8OAAC;;sDAAM;;;;;;wCACN,kBAAkB,uBACjB,8OAAC;4CAAwB,MAAK;4CAAe,SAAQ;sFAAtC;sDACb,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;;;mCAfxJ;;;;0DAqBT,8OAAC;0EAAc;0CAAqD;;;;;;;;;;;;;;;;kCAS5E,8OAAC;kEAAc;;0CACb,8OAAC;;0CAAK;;;;;;0CACN,8OAAC;0EAAe;0CACb,iBAAiB;;;;;;;;;;;;;;;;;;YAOzB,uCACC,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;4BAAwB,MAAK;4BAAe,SAAQ;sEAAtC;sCACb,cAAA,8OAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAwI,UAAS;;;;;;;;;;;;sCAE9K,8OAAC;sEAAe;;gCAAsB;gCAC5B;gCAAc;;;;;;;;;;;;;;;;;;0BAc9B,8OAAC;gBACC,UAAU;0DACA;;oBAGT,+BACC,8OAAC;kEAAc;;0CACb,8OAAC;gCAAI,OAAM;gCAAoE,SAAQ;gCAAY,MAAK;0EAAtD;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;;4BAEzJ;;;;;;;kCAKL,8OAAC;kEAAc;kCAGZ,eAAe,2BACd,8OAAC;sEAAc;;gCACZ,sBACC,mCAAmC;8CACnC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,KAAK;4CAEL,OAAO;4CACP,UAAU;4CACV,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oDACpC,EAAE,cAAc;oDAChB,gEAAgE;oDAChE,IAAI,iBAAiB,IAAI,MAAM,CAAC,WAAW;wDACzC,6DAA6D;wDAC7D,MAAM,iBAAiB;4DACrB,gBAAgB,KAAO;4DACvB,iBAAiB,KAAO;4DACxB,aAAa,EAAE,WAAW;4DAC1B,QAAQ,EAAE,MAAM;4DAChB,eAAe,EAAE,aAAa;4DAC9B,SAAS;4DACT,YAAY;4DACZ,kBAAkB;4DAClB,YAAY;4DACZ,WAAW;4DACX,WAAW,KAAK,GAAG;4DACnB,MAAM;4DACN,oBAAoB,IAAM;4DAC1B,sBAAsB,IAAM;4DAC5B,SAAS,KAAO;wDAClB;wDACA,kBAAkB;oDACpB;gDACF;4CACF;4CACA,aAAY;sFA9BF;;;;;;sDAgCZ,8OAAC;4CACC,SAAS;4CAQT,OAAM;sFAPK,CAAC;sBACV,EAAE,qBAAqB,UACnB,sDACA,qBAAqB,WACnB,mDACA,+CACJ;sDAGJ,cAAA,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;2CAIvB,2BAA2B;8CAC3B,8OAAC;oCACC,KAAK;8EACK;8CAET,cAAc,WAAW,IAAI,OAAO,KACnC,uCAAuC;kDACvC,8OAAC;kFAAc;;0DACb,8OAAC;0FAAY;;oDACV,aAAa,aAAa;oDAC1B,0BACC,8OAAC;wDACC,OAAO;4DACL,eAAe;4DACf,mBAAmB;4DACnB,yBAAyB;4DACzB,oBAAoB;wDACtB;kGANc;;;;;;;;;;;;4CAYnB,CAAC,eAAe,cAAc,WAAW,IAAI,OAAO,oBACnD,8OAAC;0FAAc;;kEACb,8OAAC;wDACC,SAAS;4DACP,cAAc;4DACd,aAAa;4DACb,aAAa;4DACb;4DACA,aAAa;4DACb,eAAe,EAAE;wDACnB;wDAQA,OAAM;kGAPK,CAAC;8BACV,EAAE,qBAAqB,UACnB,6CACA,qBAAqB,WACnB,6CACA,4CACJ;kEAGJ,cAAA,8OAAC;4DAAI,OAAM;4DAAiD,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAjD;sEAChD,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDACC,SAAS;wDAQT,OAAM;kGAPK,CAAC;8BACV,EAAE,qBAAqB,UACnB,sDACA,qBAAqB,WACnB,mDACA,+CACJ;kEAGJ,cAAA,8OAAC,8IAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;6DAMlC,8OAAC;kFAAc;;0DACb,8OAAC;;0DACE,qBAAqB,UAClB,yCACA,qBAAqB,WACnB,+CACA,qBAAqB,YACnB,6CACA,CAAC,aAAa,EAAE,iBAAiB,aAAa,CAAC;;;;;;0DAEzD,8OAAC;0FAAc;0DACZ;oDAAC;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;wDAWC,OAAO;4DACL,eAAe;4DACf,mBAAmB;4DACnB,yBAAyB;4DACzB,oBAAoB;4DACpB,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;wDAC/B;kGAfW,CAAC,qBAAqB,EAC/B,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,kBACA,cACR;uDATG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;gCA0B3B,cAAc,WAAW,IAAI,OAAO,oBACpC,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACZ,oCACC,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,iBAAc;;;;;oDACd,qBAAqB,UAClB,kBACA,qBAAqB,WACnB,gBACA,qBAAqB,YACnB,mBACA;;;;;;uDAER,yBACF,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,qBAAqB,UAClB,YACA,qBAAqB,WACnB,aACA,qBAAqB,YACnB,WACA;;;;;;qEAGV,8OAAC;0FAAe;;kEACd,8OAAC,8IAAA,CAAA,aAAU;;;;;oDACV,qBAAqB,UAClB,uBACA,qBAAqB,WACnB,oBACA,qBAAqB,YACnB,qBACA;;;;;;;;;;;;sDAId,8OAAC;sFAAe;sDACb,qBAAqB,UAClB,GAAG,UAAU,QAAQ,CAAC,GACtB,qBAAqB,WACnB,GAAG,UAAU,MAAM,CAAC,GACpB,qBAAqB,YACnB,GAAG,UAAU,MAAM,CAAC,GACpB,GAAG,UAAU,MAAM,CAAC;;;;;;;;;;;;;;;;;iDAMpC,8OAAC;sEAAc;;gCAEZ,uBAAuB,CAAC,cAAc,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,CAAC,mBAC1E,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;4CAEZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;8FAEW;;sEAEV,8OAAC;sGAAe,CAAC,cAAc,EAC7B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;sEACC,YAAY,KAAK,IAAI;;;;;;sEAExB,8OAAC;sGAAe;sEACb,KAAK,IAAI;;;;;;sEAEZ,8OAAC;sGAAe;;gEACb,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,8OAAC;4DACC,SAAS,IAAM,mBAAmB;4DAElC,OAAO,uBAAuB,UAAU;sGAD9B;sEAGV,cAAA,8OAAC,8IAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;mDAzBZ,CAAC,KAAK,EAAE,OAAO;;;;;4CA+BvB,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;8FAEW;;sEAEV,8OAAC;sGAAe,CAAC,cAAc,EAC7B,QAAQ,IAAI,KAAK,YAAY,iBAC7B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;sEACC,WAAW,QAAQ,IAAI;;;;;;sEAE1B,8OAAC;sGAAe;sEACb,QAAQ,IAAI,KAAK,YACd,uBAAuB,YAAY,GACnC,uBAAuB,WAAW;;;;;;sEAExC,8OAAC;sGAAe;sEACb,QAAQ,GAAG,CAAC,OAAO,CAAC,gBAAgB;;;;;;sEAEvC,8OAAC;4DACC,SAAS,IAAM,kBAAkB;4DAEjC,OAAO,uBAAuB,SAAS;sGAD7B;sEAGV,cAAA,8OAAC,8IAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;mDA5BZ,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;8CAoC7B,8OAAC;8EAAc;;sDACb,8OAAC;4CAED,aACE,qBAAqB,UACjB,gDACA,qBAAqB,WACnB,2DACA,qBAAqB,YACnB,4CACA;4CAEV,OAAO;4CACP,UAAU,CAAC;gDACT,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC3B,uDAAuD;gDACvD,IAAI,eAAe;oDACjB,iBAAiB;gDACnB;4CACF;4CACA,WAAW,CAAC;gDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oDACpC,EAAE,cAAc;oDAChB,gEAAgE;oDAChE,IAAI,UAAU,IAAI,MAAM,CAAC,WAAW;wDAClC,6DAA6D;wDACzD,MAAM,iBAAiB;4DACrB,gBAAgB,KAAO;4DACvB,iBAAiB,KAAO;4DACxB,aAAa,EAAE,WAAW;4DAC1B,QAAQ,EAAE,MAAM;4DAChB,eAAe,EAAE,aAAa;4DAC9B,SAAS;4DACT,YAAY;4DACZ,kBAAkB;4DAClB,YAAY;4DACZ,WAAW;4DACX,WAAW,KAAK,GAAG;4DACnB,MAAM;4DACN,oBAAoB,IAAM;4DAC1B,sBAAsB,IAAM;4DAC5B,SAAS,KAAO;wDAClB;wDACJ,kBAAkB;oDACpB;gDACF;4CACF;4CACA,UAAU;4CACV,SAAS;gDACP,4DAA4D;gDAC5D,iFAAiF;gDACjF,kCAAkC;gDAClC,mBAAmB;4CACrB;sFApDY;;;;;;sDAwDZ,8OAAC;sFAAc;sDACb,cAAA,8OAAC,gJAAA,CAAA,UAAe;gDACd,kBAAkB;gDAClB,UAAU;gDACV,qBAAqB;gDACrB,gBAAgB,IAAM,sBAAsB;gDAC5C,cAAc,CAAC;oDACb,QAAQ,GAAG,CAAC,mBAAmB;oDAC/B,sCAAsC;oDACtC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;wDAC7B,wDAAwD;wDACxD,iBAAiB,CAAA;4DACf,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,UAC5B,CAAC,UAAU,IAAI,CAAC,CAAA,eACd,aAAa,IAAI,KAAK,QAAQ,IAAI,IAClC,aAAa,IAAI,KAAK,QAAQ,IAAI,IAClC,aAAa,YAAY,KAAK,QAAQ,YAAY;4DAGtD,IAAI,SAAS,MAAM,GAAG,GAAG;gEACvB,uBAAuB;gEACvB,OAAO;uEAAI;uEAAc;iEAAS;4DACpC;4DACA,OAAO;wDACT;oDACF;gDACF;gDACA,aAAa,CAAC,KAAa;oDACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS;oDAC5C,6BAA6B;oDAC7B,IAAI,OAAO,IAAI,IAAI,IAAI;wDACrB,yBAAyB;wDACzB,gBAAgB,CAAA;4DACd,MAAM,YAAY,KAAK,IAAI,CAAC,CAAA,cAAe,YAAY,GAAG,KAAK,IAAI,IAAI;4DACvE,IAAI,WAAW;gEACb,QAAQ,GAAG,CAAC,uBAAuB;gEACnC,OAAO;4DACT;4DACA,uBAAuB;4DACvB,OAAO;mEAAI;gEAAM;oEAAE,KAAK,IAAI,IAAI;oEAAI;gEAAK;6DAAE;wDAC7C;oDACF;gDACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB,CAAC;kFACxB,CAAC;gBACV,EAAE,kBAAkB,0CAA0C,yBAAyB;;;gBAGvF,EAAE,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,gBAAgB;kCACN,CAAC;;0DACrB,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAW,GAAG,kBACvB,sBACA,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,kBAAkB;;;;;;0DAC5B,8OAAC;;0DACC,qBAAqB,UACjB,iBACA,qBAAqB,WACnB,WACA,qBAAqB,YACnB,YACA;;;;;;0DAEV,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAW,GAAG,kBACrB,sBACA,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBAAiB;gBACzB,EAAE,CAAC,kBAAkB,kBAAkB,IAAI;;;;;;;;;;;;oCAI9C,iCACC,8OAAC;wCAUC,SAAS,IAAM,mBAAmB;wCAClC,OAAO;4CAAE,gBAAgB;4CAAa,mBAAmB;wCAAO;kFAVrD,CAAC,qEAAqE,EAC/E,qBAAqB,UACjB,qDACA,qBAAqB,WACnB,oDACA,qBAAqB,YACnB,qDACA,kDACR;kDAIF,cAAA,8OAAC;4CACC,KAAK;4CAUL,SAAS,CAAC,IAAM,EAAE,eAAe;4CACjC,OAAO;gDACL,mBAAmB;gDACnB,WAAW;4CACb;sFAbW,CAAC,gGAAgG,EAC1G,qBAAqB,UACjB,qEACA,qBAAqB,WACnB,mEACA,qBAAqB,YACnB,qEACA,gEACR;;8DAOF,8OAAC;8FAAe,CAAC,4DAA4D,EAC3E,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;;sEACA,8OAAC;sGAAa;;8EACZ,8OAAC;8GAAe,CAAC,iBAAiB,EAChC,qBAAqB,UACjB,kBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,kBACA,eACR;8EACA,cAAA,8OAAC,8IAAA,CAAA,cAAW;wEAAC,WAAW,CAAC,QAAQ,EAC/B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;;;;;;8EAEJ,8OAAC;+GACC,CAAA,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,eAAc;8EAErB,qBAAqB,UAClB,sCACA,qBAAqB,WACnB,qCACA,qBAAqB,YACnB,qCACA;;;;;;;;;;;;sEAGZ,8OAAC;4DACC,SAAS,IAAM,mBAAmB;4DAUlC,cAAW;sGATA,CAAC,mCAAmC,EAC7C,qBAAqB,UACjB,+EACA,qBAAqB,WACnB,2EACA,qBAAqB,YACnB,+EACA,sEACR;sEAGF,cAAA,8OAAC;gEAAI,OAAM;gEAAiD,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0GAAjD;0EAChD,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8DAK3E,8OAAC;8FAAc;;sEACb,8OAAC;sGAAe,CAAC,oBAAoB,EACnC,qBAAqB,UACjB,sEACA,qBAAqB,WACnB,oEACA,qBAAqB,YACnB,sEACA,iEACR;sEACA,cAAA,8OAAC;0GAAa,CAAC,oBAAoB,EACjC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;0EACC,qBAAqB,UAClB,wEACA,qBAAqB,WACnB,yDACA,qBAAqB,YACnB,uEACA;;;;;;;;;;;sEAIZ,8OAAC;sGAAc;sEACZ,uBAAuB,GAAG,CAAC,CAAC,YAAY,sBACvC,8OAAC;oEAEC,SAAS;wEACP,uBAAuB;wEACvB,mBAAmB;oEACrB;oEAUA,OAAO;wEAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;wEAAE,mBAAmB;oEAAO;8GAT5D,CAAC;4BACV,EAAE,qBAAqB,UACnB,kHACA,qBAAqB,WACnB,+GACA,qBAAqB,YACnB,kHACA,0GAA0G;0DACpF,CAAC;8EAGjC,cAAA,8OAAC;kHAAc;;0FACb,8OAAC;0HAAgB,CAAC,4CAA4C,EAC5D,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;0FACA,cAAA,8OAAC;oFAAI,OAAM;oFAAiD,SAAQ;oFAAY,MAAK;8HAAnC;8FAChD,cAAA,8OAAC;wFAAK,UAAS;wFAAU,GAAE;wFAAyL,UAAS;;;;;;;;;;;;;;;;;0FAGjO,8OAAC;0HAAe;0FAAe;;;;;;;;;;;;mEA9B5B;;;;;;;;;;;;;;;;8DAqCb,8OAAC;8FAAe,CAAC,qCAAqC,EACpD,qBAAqB,UACjB,oEACA,qBAAqB,WACnB,iEACA,qBAAqB,YACnB,oEACA,6DACR;8DACA,cAAA,8OAAC;wDACC,SAAS,IAAM,mBAAmB;kGACvB,CAAC;wBACV,EAAE,qBAAqB,UACnB,6FACA,qBAAqB,WACnB,yFACA,qBAAqB,YACnB,6FACA,oFAAoF;kEAE7F,qBAAqB,UAClB,SACA,qBAAqB,WACnB,eACA,qBAAqB,YACnB,aACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DAEb,8OAAC;0FAAc;0DAMb,cAAA,8OAAC;8FAAc;8DAEZ,UAAU,GAAG,CAAC,CAAC,UAAU;wDACxB,MAAM,aAAa,qBAAqB,SAAS,IAAI;wDAErD,sCAAsC;wDACtC,MAAM,gBAAgB;4DACpB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,gBAAgB;4DACpB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,eAAe;4DACnB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,MAAM,iBAAiB;4DACrB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;4DACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;4DACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;4DACxC,OAAO;wDACT;wDAEA,gFAAgF;wDAChF,qBACE,8OAAC;4DAEC,MAAK;4DACL,SAAS,CAAC,IAAM,qBAAqB,GAAG,SAAS,IAAI;4DACrD,UAAU,2BAA2B;4DAUrC,OAAO;gEACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;gEAClC,mBAAmB;4DACrB;sGAZW,CAAC;0BACV,EAAE,aACE,kBACA,4CAA4C,gBAAgB;;0BAEhE,EAAE,aAAa,wBAAwB,kCAAkC;0BACzE,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;;wBAElF,CAAC;;8EAMD,8OAAC;8GAAc;;sFACb,8OAAC,8IAAA,CAAA,UAAO;4EAAC,WAAW,GAAG,eAAe,SAAS,EAAE,aAAa,mBAAmB,IAAI;4EACnF,OAAO;gFAAE,mBAAmB;4EAAO;;;;;;sFAErC,8OAAC;sHAAe;sFAAe,SAAS,IAAI;;;;;;;;;;;;gEAI7C,4BACC,8OAAC;oEAEC,OAAO;wEACL,OAAO;wEACP,iBAAiB;wEACjB,mBAAmB;oEACrB;8GALW,CAAC,sEAAsE,EAAE,kBAAkB;;;;;;gEAUzG,4BACC,8OAAC;oEAEC,OAAO;wEACL,OAAO;wEACP,iBAAiB;wEACjB,mBAAmB;wEACnB,gBAAgB;oEAClB;8GANW,CAAC,oEAAoE,EAAE,kBAAkB;;;;;;;2DAxCnG;;;;;oDAmDX;;;;;;;;;;;0DAKJ,8OAAC;gDAAmC,KAAK;0FAA1B;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,oBAAoB,CAAC;wDACpC,UAAU,2BAA2B;kGAC1B,CAAC;oBACV,EAAE,qBAAqB,UACnB,mDACA,qBAAqB,WACnB,gDACA,qBAAqB,YACnB,mDACA,2CACP;;oBAED,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;kBAClF,CAAC;;0EAED,8OAAC,8IAAA,CAAA,UAAO;gEAAC,WAAW,CAAC,QAAQ,EAC3B,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;0EACF,8OAAC;0GAAe;0EAAuB;;;;;;0EACvC,8OAAC;0GAAgB,CAAC,kCAAkC,EAAE,mBAAmB,eAAe,IAAI;0EAC1F,cAAA,8OAAC;oEAAI,OAAM;oEAAiD,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8GAAjD;8EAChD,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;oDAM1E,kCACC,8OAAC;wDACC,OAAO;4DAAE,mBAAmB;wDAAO;kGADtB;kEAGZ,UAAU,GAAG,CAAC,CAAC,UAAU;4DACxB,MAAM,aAAa,qBAAqB,SAAS,IAAI;4DAErD,sCAAsC;4DACtC,MAAM,oBAAoB;gEACxB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO,aAAa,iCAAiC;gEACpF,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO,aAAa,+BAA+B;gEACnF,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO,aAAa,iCAAiC;gEACtF,OAAO,aAAa,6BAA6B;4DACnD;4DAEA,MAAM,eAAe;gEACnB,IAAI,SAAS,IAAI,KAAK,SAAS,OAAO;gEACtC,IAAI,SAAS,IAAI,KAAK,UAAU,OAAO;gEACvC,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;gEACxC,OAAO;4DACT;4DAEA,qBACE,8OAAC;gEAEC,MAAK;gEACL,SAAS,CAAC;oEACR,qBAAqB,GAAG,SAAS,IAAI;oEACrC,oBAAoB;gEACtB;gEACA,UAAU,2BAA2B;gEAIrC,OAAO;oEACL,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;gEACpC;0GALW,CAAC,2DAA2D,EAAE,oBAAoB;4BAC3F,EAAE,AAAC,2BAA2B,YAAa,kCAAkC,GAAG;0BAClF,CAAC;;kFAKD,8OAAC,8IAAA,CAAA,UAAO;wEAAC,WAAW,GAAG,eAAe,QAAQ,CAAC;;;;;;kFAC/C,8OAAC;kHAAe;kFAAe,SAAS,IAAI;;;;;;oEAC3C,4BACC,8OAAC;wEAAI,OAAM;wEAAyD,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kHAAzD;kFAChD,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;+DAlBpE;;;;;wDAuBX;;;;;;;;;;;;;;;;;;kDAOR,8OAAC;kFAAc;;4CAEZ,CAAC,kBAAkB,CAAC,uCACnB,8OAAC;0FAAc;0DACZ,iDACD,8OAAC;oDACC,MAAK;oDACL,SAAS,cAAc,kBAAkB;oDAazC,OAAO,cAAc,yBAAyB,CAAC,yBAAyB,EAAE,kBAAkB;8FAZjF,CAAC;sBACV,EAAE,cACE,yGACA,qBAAqB,UACnB,0FACA,qBAAqB,WACnB,uFACA,qBAAqB,YACnB,0FACA,kFACT;0CACmB,CAAC;8DAGtB,4BACC,8OAAC;kGAAc;;0EACb,8OAAC;gEAEC,OAAO;oEACL,eAAe;oEACf,mBAAmB;oEACnB,yBAAyB;oEACzB,oBAAoB;gEACtB;0GANU;;;;;;0EAQZ,8OAAC,8IAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;gEAEC,OAAO;oEACL,eAAe;oEACf,mBAAmB;oEACnB,yBAAyB;gEAC3B;0GALU;;;;;;;;;;;6EASd,8OAAC,8IAAA,CAAA,eAAY;wDAAC,WAAW,CAAC,QAAQ,EAChC,qBAAqB,UACjB,oBACA,qBAAqB,WACnB,mBACA,qBAAqB,YACnB,oBACA,iBACR;;;;;;;;;;yEAIN,8OAAC;oDACC,MAAK;oDAEL,OAAM;oDACN,QAAQ;8FAFE;8DAIV,cAAA,8OAAC,8IAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAO9B,8OAAC;gDACC,MAAK;gDAUL,UAAU;gDACV,OACE,YACI,qBAAqB,UACnB,oBACA,qBAAqB,WACnB,iBACA,qBAAqB,YACnB,wBACA,eACN,qBAAqB,UACnB,qBACA,qBAAqB,WACnB,oBACA,qBAAqB,YACnB,yBACA;0FAzBD,CAAC;kBACV,EAAE,qBAAqB,UACnB,yHACA,qBAAqB,WACnB,oHACA,qBAAqB,YACnB,yHACA,6GAA6G;kBACrH,EAAE,YAAY,4CAA4C,IAAI;0DAoB/D,YACC,6BAA6B;gDAC7B,qBAAqB,wBACnB,8OAAC;8FAAe;;wDAAgD;sEAC9C,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAErC,qBAAqB,yBACvB,8OAAC;8FAAe;;wDAAgD;sEACjD,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAElC,qBAAqB,0BACvB,8OAAC;8FAAe;;wDAAgD;sEAC1C,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;yEAG3C,8OAAC;8FAAe;;wDAAgD;sEACnD,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;2DAIpC,eAAe;gDACf,qBAAqB,wBACnB,8OAAC;8FAAe;;wDAAgD;sEACtD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;2DAElB,qBAAqB,yBACvB,8OAAC;8FAAe;;wDAAgD;sEACvD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;2DAEjB,qBAAqB,0BACvB,8OAAC;8FAAe;;wDAAgD;sEACtD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;yEAGpB,8OAAC;8FAAe;;wDAAgD;sEACzD,8OAAC,8IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YA+G9B,oCACC,8OAAC,sIAAA,CAAA,UAAc;gBAAC,SAAS,IAAM,sBAAsB;;;;;;;;;;;;AAI7D;AAEA,QAAQ,WAAW,GAAG;uCAEP"}}, {"offset": {"line": 6724, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6735, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/public/images/favicon.ico.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 58, height: 54, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAEBAgEUJTAqN2uRkkWOxM8/hre8IUpiWwUKDQkAAAAAABIpMy04kL3HPqnj+j2l2+48qeD1Oqnd7yJfdm8DBgcEACqAnJg0uOf8MI2vqhQ0QTceVGteMLbh6i2v1NwPKzMpACyryMMqx+36IGBtWwABAQAKGx4TKLLPyyfK6vMVSVNJACGVpJke1/L9Ia6+tRdUWkklcoBzMLrh7iPF3+QONjovAA44OiwZws3HE+f2/BXm9fUc3fL6Kcjt/h/Q6e8RXGFRAAECAgEMNjcrE6CklhHQ1McSwcW2GZCbjxnE0ssPZmpRi6piXKCTQHwAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 6748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6759, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/public/images/favicon.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 27, height: 32, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAICAYAAAA1BOUGAAAA80lEQVR42gHoABf/AAAAAAAAAAAAAwULByg3e3MHCRUNAAAAAAAAAAAAAAAAAAMFCwUTGTkvJS9alRAVLzUCAwgGAAAAAAAJDR8PMUWkfUdj6t9KZu/8Q1za4ic3hIEGCRYQADJCio92jPj0kaP5/5+u+f+Ro/n/c4fr9Ss4c5IAXXHJ/MTM9P+8w+H/7/L+/7zD4f/Hz/X/WWu8/ABdctLzuMP6/7O98f/R2P7/tL7y/7fC+/9VaMDqAEBZz8xWc/7/aIL+/5Kk/v9pgv7/VG/26ik3gGEAPlfPx01r/v9Sb/7/ZX/+/VBt990uP5VoBggUCKGXbuBimsfYAAAAAElFTkSuQmCC\", blurWidth: 7, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 6772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/LoadingIndicator.tsx"], "sourcesContent": ["// components/chatComponents/LoadingIndicator.tsx\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport logo from \"@/public/images/favicon.png\";\r\nimport { PiSparkle, PiChecks } from \"react-icons/pi\";\r\n\r\ninterface LoadingIndicatorProps {\r\n  message?: string;\r\n  showSteps?: boolean;\r\n  showAvatar?: boolean;\r\n  inline?: boolean;\r\n  timestamp?: string;\r\n  showPulse?: boolean;\r\n}\r\n\r\n// Professional SVG Loading Animation Component\r\nconst SVGLoadingAnimation: React.FC<{ className?: string; variant?: 'default' | 'brain' | 'professional' }> = ({\r\n  className = \"\",\r\n  variant = 'professional'\r\n}) => {\r\n  if (variant === 'professional') {\r\n    return (\r\n      <svg\r\n        width=\"32\"\r\n        height=\"32\"\r\n        viewBox=\"0 0 32 32\"\r\n        className={`${className}`}\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n      >\r\n        <defs>\r\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\r\n            <stop offset=\"0%\" stopColor=\"currentColor\" stopOpacity=\"0.8\" />\r\n            <stop offset=\"50%\" stopColor=\"currentColor\" stopOpacity=\"1\" />\r\n            <stop offset=\"100%\" stopColor=\"currentColor\" stopOpacity=\"0.6\" />\r\n          </linearGradient>\r\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" stopColor=\"currentColor\" stopOpacity=\"0.3\" />\r\n            <stop offset=\"50%\" stopColor=\"currentColor\" stopOpacity=\"0.8\" />\r\n            <stop offset=\"100%\" stopColor=\"currentColor\" stopOpacity=\"0.3\" />\r\n          </linearGradient>\r\n          <filter id=\"glow\">\r\n            <feGaussianBlur stdDeviation=\"1\" result=\"coloredBlur\"/>\r\n            <feMerge>\r\n              <feMergeNode in=\"coloredBlur\"/>\r\n              <feMergeNode in=\"SourceGraphic\"/>\r\n            </feMerge>\r\n          </filter>\r\n        </defs>\r\n\r\n        <style>\r\n          {`\r\n            .pro-outer-ring {\r\n              animation: proRotate 3s linear infinite;\r\n              transform-origin: 16px 16px;\r\n              filter: url(#glow);\r\n            }\r\n\r\n            .pro-inner-ring {\r\n              animation: proRotateReverse 2s linear infinite;\r\n              transform-origin: 16px 16px;\r\n            }\r\n\r\n            .pro-center-pulse {\r\n              animation: proPulse 1.5s ease-in-out infinite;\r\n              transform-origin: 16px 16px;\r\n            }\r\n\r\n            .pro-orbit-dot {\r\n              animation: proOrbit 2.5s ease-in-out infinite;\r\n            }\r\n\r\n            .pro-orbit-dot-1 { animation-delay: 0s; }\r\n            .pro-orbit-dot-2 { animation-delay: 0.4s; }\r\n            .pro-orbit-dot-3 { animation-delay: 0.8s; }\r\n            .pro-orbit-dot-4 { animation-delay: 1.2s; }\r\n\r\n            @keyframes proRotate {\r\n              0% { transform: rotate(0deg); }\r\n              100% { transform: rotate(360deg); }\r\n            }\r\n\r\n            @keyframes proRotateReverse {\r\n              0% { transform: rotate(360deg); }\r\n              100% { transform: rotate(0deg); }\r\n            }\r\n\r\n            @keyframes proPulse {\r\n              0%, 100% {\r\n                transform: scale(1);\r\n                opacity: 0.8;\r\n              }\r\n              50% {\r\n                transform: scale(1.2);\r\n                opacity: 1;\r\n              }\r\n            }\r\n\r\n            @keyframes proOrbit {\r\n              0%, 100% {\r\n                opacity: 0.3;\r\n                transform: scale(0.8);\r\n              }\r\n              50% {\r\n                opacity: 1;\r\n                transform: scale(1.3);\r\n              }\r\n            }\r\n          `}\r\n        </style>\r\n\r\n        {/* Outer rotating ring with gradient */}\r\n        <circle\r\n          className=\"pro-outer-ring\"\r\n          cx=\"16\"\r\n          cy=\"16\"\r\n          r=\"12\"\r\n          fill=\"none\"\r\n          stroke=\"url(#gradient1)\"\r\n          strokeWidth=\"2\"\r\n          strokeLinecap=\"round\"\r\n          strokeDasharray=\"20 10\"\r\n          opacity=\"0.7\"\r\n        />\r\n\r\n        {/* Inner counter-rotating ring */}\r\n        <circle\r\n          className=\"pro-inner-ring\"\r\n          cx=\"16\"\r\n          cy=\"16\"\r\n          r=\"8\"\r\n          fill=\"none\"\r\n          stroke=\"url(#gradient2)\"\r\n          strokeWidth=\"1.5\"\r\n          strokeLinecap=\"round\"\r\n          strokeDasharray=\"15 5\"\r\n          opacity=\"0.5\"\r\n        />\r\n\r\n        {/* Center pulsing core */}\r\n        <circle\r\n          className=\"pro-center-pulse\"\r\n          cx=\"16\"\r\n          cy=\"16\"\r\n          r=\"3\"\r\n          fill=\"url(#gradient1)\"\r\n          opacity=\"0.9\"\r\n        />\r\n\r\n        {/* Orbiting dots */}\r\n        <circle className=\"pro-orbit-dot pro-orbit-dot-1\" cx=\"16\" cy=\"4\" r=\"1.5\" fill=\"currentColor\" />\r\n        <circle className=\"pro-orbit-dot pro-orbit-dot-2\" cx=\"28\" cy=\"16\" r=\"1.5\" fill=\"currentColor\" />\r\n        <circle className=\"pro-orbit-dot pro-orbit-dot-3\" cx=\"16\" cy=\"28\" r=\"1.5\" fill=\"currentColor\" />\r\n        <circle className=\"pro-orbit-dot pro-orbit-dot-4\" cx=\"4\" cy=\"16\" r=\"1.5\" fill=\"currentColor\" />\r\n      </svg>\r\n    );\r\n  }\r\n\r\n  if (variant === 'brain') {\r\n    return (\r\n      <svg\r\n        width=\"24\"\r\n        height=\"24\"\r\n        viewBox=\"0 0 24 24\"\r\n        className={`${className}`}\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n      >\r\n        <style>\r\n          {`\r\n            .brain-wave {\r\n              animation: brainWave 2s ease-in-out infinite;\r\n            }\r\n            .brain-wave-1 { animation-delay: 0s; }\r\n            .brain-wave-2 { animation-delay: 0.2s; }\r\n            .brain-wave-3 { animation-delay: 0.4s; }\r\n            .brain-wave-4 { animation-delay: 0.6s; }\r\n            .brain-wave-5 { animation-delay: 0.8s; }\r\n\r\n            .brain-core {\r\n              animation: brainPulse 1.5s ease-in-out infinite;\r\n            }\r\n\r\n            @keyframes brainWave {\r\n              0%, 100% { opacity: 0.2; transform: scale(0.8); }\r\n              50% { opacity: 1; transform: scale(1.1); }\r\n            }\r\n\r\n            @keyframes brainPulse {\r\n              0%, 100% { opacity: 0.6; }\r\n              50% { opacity: 1; }\r\n            }\r\n          `}\r\n        </style>\r\n\r\n        {/* Brain-like neural network pattern */}\r\n        <circle className=\"brain-core\" cx=\"12\" cy=\"12\" r=\"2\" fill=\"currentColor\" />\r\n        <circle className=\"brain-wave brain-wave-1\" cx=\"6\" cy=\"8\" r=\"1\" fill=\"currentColor\" />\r\n        <circle className=\"brain-wave brain-wave-2\" cx=\"18\" cy=\"8\" r=\"1\" fill=\"currentColor\" />\r\n        <circle className=\"brain-wave brain-wave-3\" cx=\"6\" cy=\"16\" r=\"1\" fill=\"currentColor\" />\r\n        <circle className=\"brain-wave brain-wave-4\" cx=\"18\" cy=\"16\" r=\"1\" fill=\"currentColor\" />\r\n        <circle className=\"brain-wave brain-wave-5\" cx=\"12\" cy=\"6\" r=\"1\" fill=\"currentColor\" />\r\n\r\n        {/* Connecting lines */}\r\n        <line className=\"brain-wave brain-wave-1\" x1=\"12\" y1=\"12\" x2=\"6\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"0.5\" opacity=\"0.4\" />\r\n        <line className=\"brain-wave brain-wave-2\" x1=\"12\" y1=\"12\" x2=\"18\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"0.5\" opacity=\"0.4\" />\r\n        <line className=\"brain-wave brain-wave-3\" x1=\"12\" y1=\"12\" x2=\"6\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"0.5\" opacity=\"0.4\" />\r\n        <line className=\"brain-wave brain-wave-4\" x1=\"12\" y1=\"12\" x2=\"18\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"0.5\" opacity=\"0.4\" />\r\n        <line className=\"brain-wave brain-wave-5\" x1=\"12\" y1=\"12\" x2=\"12\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"0.5\" opacity=\"0.4\" />\r\n      </svg>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <svg\r\n      width=\"24\"\r\n      height=\"24\"\r\n      viewBox=\"0 0 24 24\"\r\n      className={`${className}`}\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <style>\r\n        {`\r\n          .spinner-ring {\r\n            animation: spin 2s linear infinite;\r\n            transform-origin: 12px 12px;\r\n          }\r\n          .spinner-dots {\r\n            animation: pulse 1.5s ease-in-out infinite;\r\n          }\r\n          .spinner-dot-1 { animation-delay: 0s; }\r\n          .spinner-dot-2 { animation-delay: 0.3s; }\r\n          .spinner-dot-3 { animation-delay: 0.6s; }\r\n\r\n          @keyframes spin {\r\n            0% { transform: rotate(0deg); }\r\n            100% { transform: rotate(360deg); }\r\n          }\r\n\r\n          @keyframes pulse {\r\n            0%, 100% { opacity: 0.3; transform: scale(0.8); }\r\n            50% { opacity: 1; transform: scale(1.2); }\r\n          }\r\n        `}\r\n      </style>\r\n\r\n      {/* Outer rotating ring */}\r\n      <circle\r\n        className=\"spinner-ring\"\r\n        cx=\"12\"\r\n        cy=\"12\"\r\n        r=\"10\"\r\n        fill=\"none\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth=\"2\"\r\n        strokeLinecap=\"round\"\r\n        strokeDasharray=\"31.416\"\r\n        strokeDashoffset=\"15.708\"\r\n        opacity=\"0.3\"\r\n      />\r\n\r\n      {/* Inner pulsing dots */}\r\n      <circle className=\"spinner-dots spinner-dot-1\" cx=\"8\" cy=\"12\" r=\"1.5\" fill=\"currentColor\" />\r\n      <circle className=\"spinner-dots spinner-dot-2\" cx=\"12\" cy=\"12\" r=\"1.5\" fill=\"currentColor\" />\r\n      <circle className=\"spinner-dots spinner-dot-3\" cx=\"16\" cy=\"12\" r=\"1.5\" fill=\"currentColor\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nconst LoadingIndicator: React.FC<LoadingIndicatorProps> = ({\r\n  message = \"Thinking\",\r\n  showSteps = false,\r\n  showAvatar = true,\r\n  inline = false,\r\n  timestamp,\r\n  showPulse = false\r\n}) => {\r\n  const formattedTime = timestamp\r\n    ? new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\r\n    : 'processing...';\r\n\r\n  // Inline loading indicator with SVG animation only\r\n  if (inline) {\r\n    return (\r\n      <span className=\"inline-flex items-center justify-center\">\r\n        <SVGLoadingAnimation variant=\"professional\" className=\"text-primaryColor w-8 h-8\" />\r\n      </span>\r\n    );\r\n  }\r\n\r\n  // Full loading indicator with avatar and container\r\n  return (\r\n    <div className=\"flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]\">\r\n      {showAvatar && (\r\n        <Image src={logo} alt=\"AIQuill logo\" className=\"max-sm:size-5 object-cover\" width={32} height={32} />\r\n      )}\r\n      <div className=\"flex flex-col justify-start items-start gap-3 flex-1\">\r\n          <SVGLoadingAnimation variant=\"professional\" className=\"text-secondaryColor w-10 h-10\" />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingIndicator;"], "names": [], "mappings": "AAAA,iDAAiD;;;;;AAEjD;AACA;;;;AAYA,+CAA+C;AAC/C,MAAM,sBAAwG,CAAC,EAC7G,YAAY,EAAE,EACd,UAAU,cAAc,EACzB;IACC,IAAI,YAAY,gBAAgB;QAC9B,qBACE,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,WAAW,GAAG,WAAW;YACzB,OAAM;;8BAEN,8OAAC;;sCACC,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC1D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;oCAAe,aAAY;;;;;;8CACvD,8OAAC;oCAAK,QAAO;oCAAM,WAAU;oCAAe,aAAY;;;;;;8CACxD,8OAAC;oCAAK,QAAO;oCAAO,WAAU;oCAAe,aAAY;;;;;;;;;;;;sCAE3D,8OAAC;4BAAe,IAAG;4BAAY,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC1D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;oCAAe,aAAY;;;;;;8CACvD,8OAAC;oCAAK,QAAO;oCAAM,WAAU;oCAAe,aAAY;;;;;;8CACxD,8OAAC;oCAAK,QAAO;oCAAO,WAAU;oCAAe,aAAY;;;;;;;;;;;;sCAE3D,8OAAC;4BAAO,IAAG;;8CACT,8OAAC;oCAAe,cAAa;oCAAI,QAAO;;;;;;8CACxC,8OAAC;;sDACC,8OAAC;4CAAY,IAAG;;;;;;sDAChB,8OAAC;4CAAY,IAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAKtB,8OAAC;8BACE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAyDF,CAAC;;;;;;8BAIH,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,iBAAgB;oBAChB,SAAQ;;;;;;8BAIV,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,iBAAgB;oBAChB,SAAQ;;;;;;8BAIV,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,SAAQ;;;;;;8BAIV,8OAAC;oBAAO,WAAU;oBAAgC,IAAG;oBAAK,IAAG;oBAAI,GAAE;oBAAM,MAAK;;;;;;8BAC9E,8OAAC;oBAAO,WAAU;oBAAgC,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAM,MAAK;;;;;;8BAC/E,8OAAC;oBAAO,WAAU;oBAAgC,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAM,MAAK;;;;;;8BAC/E,8OAAC;oBAAO,WAAU;oBAAgC,IAAG;oBAAI,IAAG;oBAAK,GAAE;oBAAM,MAAK;;;;;;;;;;;;IAGpF;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,WAAW,GAAG,WAAW;YACzB,OAAM;;8BAEN,8OAAC;8BACE,CAAC;;;;;;;;;;;;;;;;;;;;;;;UAuBF,CAAC;;;;;;8BAIH,8OAAC;oBAAO,WAAU;oBAAa,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,MAAK;;;;;;8BAC1D,8OAAC;oBAAO,WAAU;oBAA0B,IAAG;oBAAI,IAAG;oBAAI,GAAE;oBAAI,MAAK;;;;;;8BACrE,8OAAC;oBAAO,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAI,GAAE;oBAAI,MAAK;;;;;;8BACtE,8OAAC;oBAAO,WAAU;oBAA0B,IAAG;oBAAI,IAAG;oBAAK,GAAE;oBAAI,MAAK;;;;;;8BACtE,8OAAC;oBAAO,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,MAAK;;;;;;8BACvE,8OAAC;oBAAO,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAI,GAAE;oBAAI,MAAK;;;;;;8BAGtE,8OAAC;oBAAK,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,IAAG;oBAAI,QAAO;oBAAe,aAAY;oBAAM,SAAQ;;;;;;8BACxH,8OAAC;oBAAK,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,QAAO;oBAAe,aAAY;oBAAM,SAAQ;;;;;;8BACzH,8OAAC;oBAAK,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,IAAG;oBAAK,QAAO;oBAAe,aAAY;oBAAM,SAAQ;;;;;;8BACzH,8OAAC;oBAAK,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAK,QAAO;oBAAe,aAAY;oBAAM,SAAQ;;;;;;8BAC1H,8OAAC;oBAAK,WAAU;oBAA0B,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,QAAO;oBAAe,aAAY;oBAAM,SAAQ;;;;;;;;;;;;IAG/H;IAEA,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,WAAW,GAAG,WAAW;QACzB,OAAM;;0BAEN,8OAAC;0BACE,CAAC;;;;;;;;;;;;;;;;;;;;;QAqBF,CAAC;;;;;;0BAIH,8OAAC;gBACC,WAAU;gBACV,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,MAAK;gBACL,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,iBAAgB;gBAChB,kBAAiB;gBACjB,SAAQ;;;;;;0BAIV,8OAAC;gBAAO,WAAU;gBAA6B,IAAG;gBAAI,IAAG;gBAAK,GAAE;gBAAM,MAAK;;;;;;0BAC3E,8OAAC;gBAAO,WAAU;gBAA6B,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAM,MAAK;;;;;;0BAC5E,8OAAC;gBAAO,WAAU;gBAA6B,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAM,MAAK;;;;;;;;;;;;AAGlF;AAEA,MAAM,mBAAoD,CAAC,EACzD,UAAU,UAAU,EACpB,YAAY,KAAK,EACjB,aAAa,IAAI,EACjB,SAAS,KAAK,EACd,SAAS,EACT,YAAY,KAAK,EAClB;IACC,MAAM,gBAAgB,YAClB,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;QAAE,MAAM;QAAW,QAAQ;IAAU,KAChF;IAEJ,mDAAmD;IACnD,IAAI,QAAQ;QACV,qBACE,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC;gBAAoB,SAAQ;gBAAe,WAAU;;;;;;;;;;;IAG5D;IAEA,mDAAmD;IACnD,qBACE,8OAAC;QAAI,WAAU;;YACZ,4BACC,8OAAC,6HAAA,CAAA,UAAK;gBAAC,KAAK,0RAAA,CAAA,UAAI;gBAAE,KAAI;gBAAe,WAAU;gBAA6B,OAAO;gBAAI,QAAQ;;;;;;0BAEjG,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAoB,SAAQ;oBAAe,WAAU;;;;;;;;;;;;;;;;;AAIhE;uCAEe"}}, {"offset": {"line": 7418, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/WebpagePreview.tsx"], "sourcesContent": ["import React, { useState, useEffect, memo } from 'react';\r\nimport Image from 'next/image';\r\nimport { PiGlobe, PiArrowSquareOut, PiSpinner, PiLink, PiInfo } from 'react-icons/pi';\r\n\r\ninterface WebpagePreviewProps {\r\n  url: string;\r\n  summary?: string;\r\n  referenceNumber: number;\r\n}\r\n\r\n// Using memo to prevent unnecessary re-renders\r\nconst WebpagePreview: React.FC<WebpagePreviewProps> = memo(({\r\n  url,\r\n  summary,\r\n  referenceNumber\r\n}) => {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);\r\n  const [domainName, setDomainName] = useState<string>('');\r\n\r\n  // Extract domain name for display\r\n  useEffect(() => {\r\n    try {\r\n      const domain = new URL(url).hostname;\r\n      setDomainName(domain.replace('www.', ''));\r\n    } catch (error) {\r\n      setDomainName(url);\r\n    }\r\n  }, [url]);\r\n\r\n  // Get favicon URL\r\n  useEffect(() => {\r\n    try {\r\n      const domain = new URL(url).origin;\r\n      setFaviconUrl(`${domain}/favicon.ico`);\r\n      // Set loading to false after a short delay to avoid flickering\r\n      const timer = setTimeout(() => setIsLoading(false), 300);\r\n      return () => clearTimeout(timer);\r\n    } catch (error) {\r\n      setFaviconUrl(null);\r\n      setIsLoading(false);\r\n    }\r\n  }, [url]);\r\n\r\n  // Function to handle explicit visit button click\r\n  const handleVisitClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    e.preventDefault();\r\n    window.open(url, '_blank', 'noopener,noreferrer');\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"p-3 bg-white dark:bg-gray-800 shadow-xl rounded-lg border border-primaryColor/20 z-50 w-80\"\r\n      // Using onMouseDown instead of onClick for better performance\r\n      onMouseDown={(e) => e.stopPropagation()}\r\n    >\r\n      <div className=\"flex items-center gap-2 mb-2\">\r\n        <div className=\"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-primaryColor/20 to-blue-500/20 rounded-full overflow-hidden\">\r\n          {faviconUrl ? (\r\n            <Image\r\n              src={faviconUrl}\r\n              alt={domainName}\r\n              width={16}\r\n              height={16}\r\n              onError={() => setFaviconUrl(null)}\r\n              className=\"w-4 h-4\"\r\n              unoptimized // Skip image optimization for better performance\r\n            />\r\n          ) : (\r\n            <PiGlobe className=\"text-primaryColor\" />\r\n          )}\r\n        </div>\r\n        <div className=\"flex-1 truncate\">\r\n          <p className=\"text-sm font-medium text-n700 dark:text-n30 truncate\">\r\n            {domainName}\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs font-medium text-white bg-gradient-to-r from-primaryColor to-blue-600 px-2 py-0.5 rounded-full shadow-sm\">\r\n          {referenceNumber}\r\n        </span>\r\n      </div>\r\n\r\n      {/* URL display */}\r\n      <div className=\"flex items-center text-xs text-n500 dark:text-n400 mb-2 bg-gray-50 dark:bg-gray-700/50 p-1.5 rounded\">\r\n        <PiLink className=\"mr-1 text-primaryColor flex-shrink-0\" />\r\n        <span className=\"truncate\">\r\n          {url}\r\n        </span>\r\n      </div>\r\n\r\n      {/* Summary section - more prominent */}\r\n      {summary && (\r\n        <div className=\"bg-gray-50 dark:bg-gray-700/50 p-2 rounded mb-2\">\r\n          <div className=\"flex items-start\">\r\n            <PiInfo className=\"text-primaryColor mr-1 mt-0.5 flex-shrink-0\" />\r\n            <p className=\"text-xs text-n600 dark:text-n300 line-clamp-4\">\r\n              {summary}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Preview section - smaller */}\r\n      <div className=\"relative w-full h-24 mb-2 bg-gray-100 dark:bg-gray-700 rounded overflow-hidden shadow-inner\">\r\n        {isLoading && (\r\n          <div className=\"absolute inset-0 flex items-center justify-center z-10\">\r\n            <div className=\"animate-pulse flex flex-col items-center\">\r\n              <div className=\"w-8 h-8 bg-primaryColor/20 rounded-full mb-1 flex items-center justify-center\">\r\n                <PiSpinner className=\"text-primaryColor animate-spin\" />\r\n              </div>\r\n              <div className=\"h-1.5 w-20 bg-primaryColor/20 rounded\"></div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* We use an img tag instead of iframe for better security and performance */}\r\n        <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-b from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800\">\r\n          <p className=\"text-xs text-n500 dark:text-n400 px-3 text-center\">\r\n            Reference: {domainName}\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-end text-xs\">\r\n        <button\r\n          className=\"flex items-center gap-1 text-white bg-gradient-to-r from-primaryColor to-blue-600 px-2 py-1 rounded shadow-sm hover:shadow-md transition-all duration-200\"\r\n          onClick={handleVisitClick}\r\n        >\r\n          <span>Visit Source</span>\r\n          <PiArrowSquareOut />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\n// Add display name for debugging\r\nWebpagePreview.displayName = 'WebpagePreview';\r\n\r\nexport default WebpagePreview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQA,+CAA+C;AAC/C,MAAM,+BAAgD,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAC1D,GAAG,EACH,OAAO,EACP,eAAe,EAChB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ;YACpC,cAAc,OAAO,OAAO,CAAC,QAAQ;QACvC,EAAE,OAAO,OAAO;YACd,cAAc;QAChB;IACF,GAAG;QAAC;KAAI;IAER,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,SAAS,IAAI,IAAI,KAAK,MAAM;YAClC,cAAc,GAAG,OAAO,YAAY,CAAC;YACrC,+DAA+D;YAC/D,MAAM,QAAQ,WAAW,IAAM,aAAa,QAAQ;YACpD,OAAO,IAAM,aAAa;QAC5B,EAAE,OAAO,OAAO;YACd,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAI;IAER,iDAAiD;IACjD,MAAM,mBAAmB,CAAC;QACxB,EAAE,eAAe;QACjB,EAAE,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,UAAU;IAC7B;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,8DAA8D;QAC9D,aAAa,CAAC,IAAM,EAAE,eAAe;;0BAErC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,2BACC,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAK;4BACL,OAAO;4BACP,QAAQ;4BACR,SAAS,IAAM,cAAc;4BAC7B,WAAU;4BACV,WAAW;;;;;iDAGb,8OAAC,8IAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAGvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;kCAGL,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAKJ,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;;oBACZ,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAoD;gCACnD;;;;;;;;;;;;;;;;;;0BAKlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;;sCAET,8OAAC;sCAAK;;;;;;sCACN,8OAAC,8IAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;AAK3B;AAEA,iCAAiC;AACjC,eAAe,WAAW,GAAG;uCAEd"}}, {"offset": {"line": 7693, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7699, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/PerplexityStyleResponse.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport WebpagePreview from './WebpagePreview';\nimport { Pi<PERSON>aretRight, <PERSON><PERSON><PERSON>bulb, <PERSON>Link, PiX } from 'react-icons/pi';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ClientOnly from '@/components/ui/ClientOnly';\n\ntype Language = 'english' | 'tamil' | 'telugu' | 'kannada';\n\ninterface PerplexityStyleResponseProps {\n  text: string;\n  sentenceAnalysis?: Array<{\n    sentence: string;\n    url: string;\n    summary?: string;\n  }>;\n  relatedQuestions?: string[];\n  onSelectQuestion?: (question: string) => void;\n  selectedLanguage?: string;\n}\n\n// Interface for the preview data\ninterface PreviewData {\n  url: string;\n  summary?: string;\n  referenceNumber: number;\n  position: {\n    x: number;\n    y: number;\n  } | null;\n}\n\nconst PerplexityStyleResponse: React.FC<PerplexityStyleResponseProps> = ({\n  text,\n  sentenceAnalysis,\n  relatedQuestions,\n  onSelectQuestion,\n  selectedLanguage = \"English\"\n}) => {\n  // Debug logging for related questions\n  console.log(\"🔍 PerplexityStyleResponse - Related Questions Debug:\");\n  console.log(\"📊 relatedQuestions:\", relatedQuestions);\n  console.log(\"📊 relatedQuestions length:\", relatedQuestions?.length);\n  console.log(\"📊 relatedQuestions type:\", typeof relatedQuestions);\n  if (relatedQuestions) {\n    relatedQuestions.forEach((q, i) => console.log(`📊 Question ${i + 1}:`, q));\n  }\n  const [activeReference, setActiveReference] = useState<number | null>(null);\n  const [previewData, setPreviewData] = useState<PreviewData | null>(null);\n  const referenceRefs = useRef<Record<number, HTMLDivElement | null>>({});\n  const referenceSpanRefs = useRef<Record<number, HTMLSpanElement | null>>({});\n  const previewTimerRef = useRef<NodeJS.Timeout | null>(null);\n  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);\n  const isHoveringPreviewRef = useRef<boolean>(false);\n\n  // Function to detect language from text content\n  const detectLanguageFromText = (text: string): Language => {\n    // Tamil Unicode range: \\u0B80-\\u0BFF\n    const tamilRegex = /[\\u0B80-\\u0BFF]/;\n    // Telugu Unicode range: \\u0C00-\\u0C7F\n    const teluguRegex = /[\\u0C00-\\u0C7F]/;\n    // Kannada Unicode range: \\u0C80-\\u0CFF\n    const kannadaRegex = /[\\u0C80-\\u0CFF]/;\n\n    if (tamilRegex.test(text)) {\n      return 'tamil';\n    } else if (teluguRegex.test(text)) {\n      return 'telugu';\n    } else if (kannadaRegex.test(text)) {\n      return 'kannada';\n    } else {\n      return 'english';\n    }\n  };\n\n  // Handle reference link navigation based on detected language from response\n  const handleReferenceClick = (url: string, referenceNumber?: number) => {\n    // Only run on client side\n    if (typeof window === 'undefined') return;\n\n    // Find the corresponding sentence analysis item to get source type\n    const sentenceItem = sentenceAnalysis?.find((item, index) => (index + 1) === referenceNumber);\n    const sourceType = (sentenceItem as any)?.source_type;\n\n    // Detect language from the actual response text instead of selectedLanguage prop\n    const detectedLanguage = detectLanguageFromText(text);\n\n    // Debug logging\n    console.log('Reference click - selectedLanguage prop:', selectedLanguage);\n    console.log('Reference click - detected language from text:', detectedLanguage);\n    console.log('Reference click - original URL:', url);\n    console.log('Reference click - source type:', sourceType);\n    console.log('Reference click - sample text for detection:', text.substring(0, 100));\n\n    // Handle file:// URLs (local documents/PDFs/audio files)\n    if (url.startsWith('file://')) {\n      console.log('Local file detected, cannot open directly in browser');\n      // For local files, we could show a message or handle differently\n      alert(`This reference points to a local ${sourceType || 'file'}: ${(sentenceItem as any)?.source_title || 'Unknown'}`);\n      return;\n    }\n\n    // Handle \"Not found\" or invalid URLs\n    if (url === 'Not found' || url === 'N/A') {\n      console.log('Invalid URL detected');\n      alert('This reference does not have a valid URL to open.');\n      return;\n    }\n\n    if (detectedLanguage === 'english') {\n      // For English, open external URL in new tab\n      console.log('Opening external URL in new tab');\n      window.open(url, '_blank', 'noopener,noreferrer');\n    } else {\n      // For non-English languages, open language-specific page in NEW TAB with URL parameters\n      try {\n        const urlObj = new URL(url);\n        const domain = urlObj.hostname;\n\n        // Create query parameters for the language page\n        const params = new URLSearchParams({\n          url: url,\n          domain: domain,\n          referenceNumber: (referenceNumber || 1).toString(),\n          returnUrl: window.location.pathname + window.location.search,\n          sourceType: sourceType || 'unknown'\n        });\n\n        const languagePage = `/${detectedLanguage}?${params.toString()}`;\n\n        // Open language page in NEW TAB instead of same tab\n        console.log('Opening language page in new tab with params:', languagePage);\n        window.open(languagePage, '_blank', 'noopener,noreferrer');\n      } catch (error) {\n        console.error('Error parsing URL for navigation:', error);\n        // Fallback to simple navigation in new tab\n        const languagePage = `/${detectedLanguage}`;\n        window.open(languagePage, '_blank', 'noopener,noreferrer');\n      }\n    }\n  };\n\n  // Handle question selection\n  const handleQuestionSelect = (question: string) => {\n    console.log(\"🔄 PerplexityStyleResponse: handleQuestionSelect called with:\", question);\n    \n    if (onSelectQuestion) {\n      // Remove markdown formatting from the question\n      const cleanQuestion = question.replace(/\\*\\*/g, '');\n      console.log(\"✅ PerplexityStyleResponse: Calling onSelectQuestion with:\", cleanQuestion);\n      \n      // Call onSelectQuestion with the cleaned question\n      onSelectQuestion(cleanQuestion);\n      \n      // Also try to update the input directly as a fallback\n      const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;\n      if (inputElement) {\n        inputElement.value = cleanQuestion;\n        inputElement.dispatchEvent(new Event('input', { bubbles: true }));\n        inputElement.focus();\n      }\n    } else {\n      console.warn(\"❌ PerplexityStyleResponse: onSelectQuestion prop is not provided!\");\n    }\n  };\n\n  // Ref callback that properly handles the TypeScript typing\n  const setReferenceRef = (referenceNumber: number) => (el: HTMLDivElement | null) => {\n    referenceRefs.current[referenceNumber] = el;\n  };\n\n  // Add logging for debugging\n  useEffect(() => {\n    // Log the props for debugging\n    console.log(\"PerplexityStyleResponse props:\", { text, sentenceAnalysis, relatedQuestions, selectedLanguage });\n\n    // Explicitly log related questions\n    if (relatedQuestions && relatedQuestions.length > 0) {\n      console.log(\"PerplexityStyleResponse has related questions:\", relatedQuestions);\n    } else {\n      console.warn(\"PerplexityStyleResponse has no related questions\");\n    }\n\n    // Log language information\n    console.log(\"PerplexityStyleResponse selectedLanguage:\", selectedLanguage);\n    console.log(\"PerplexityStyleResponse detected language from text:\", detectLanguageFromText(text));\n  }, [text, sentenceAnalysis, relatedQuestions, selectedLanguage]);\n\n  // Ref callback for reference number spans\n  const setReferenceSpanRef = (referenceNumber: number) => (el: HTMLSpanElement | null) => {\n    referenceSpanRefs.current[referenceNumber] = el;\n  };\n\n  // Handle mouse enter on reference number with improved debounce\n  const handleReferenceMouseEnter = (referenceNumber: number, url: string, summary?: string) => {\n    // Clear any existing hide timer\n    if (hideTimerRef.current) {\n      clearTimeout(hideTimerRef.current);\n      hideTimerRef.current = null;\n    }\n\n    // Set active reference immediately for visual feedback\n    setActiveReference(referenceNumber);\n\n    // Clear any existing preview timer\n    if (previewTimerRef.current) {\n      clearTimeout(previewTimerRef.current);\n    }\n\n    // Use a small timeout to prevent flickering when moving mouse quickly\n    previewTimerRef.current = setTimeout(() => {\n      // Get position of the reference span\n      const spanElement = referenceSpanRefs.current[referenceNumber];\n      if (spanElement) {\n        const rect = spanElement.getBoundingClientRect();\n        setPreviewData({\n          url,\n          summary,\n          referenceNumber,\n          position: {\n            x: rect.left,\n            y: rect.top\n          }\n        });\n      }\n      previewTimerRef.current = null;\n    }, 100); // Reduced delay for better responsiveness\n  };\n\n  // Handle mouse leave with improved stability\n  const handleReferenceMouseLeave = () => {\n    // Don't hide immediately, wait to see if user is moving to another reference\n    // or to the preview itself\n    hideTimerRef.current = setTimeout(() => {\n      if (!isHoveringPreviewRef.current) {\n        setActiveReference(null);\n        setPreviewData(null);\n      }\n      hideTimerRef.current = null;\n    }, 200);\n  };\n\n  // Removed the auto-scrolling effect when hovering references\n  // This prevents the page from jumping around when hovering references\n\n  // Cleanup effect to clear any timers when component unmounts\n  useEffect(() => {\n    return () => {\n      if (previewTimerRef.current) {\n        clearTimeout(previewTimerRef.current);\n      }\n      if (hideTimerRef.current) {\n        clearTimeout(hideTimerRef.current);\n      }\n    };\n  }, []);\n\n  // Function to process markdown formatting\n  const processMarkdown = (content: string, keyPrefix: string = 'md'): React.ReactNode => {\n    if (!content) return content;\n\n    // Process the content in stages to handle different markdown elements\n\n    // Stage 1: Handle bold text (text between ** **)\n    const processBold = (text: string): React.ReactNode[] => {\n      const boldRegex = /\\*\\*(.*?)\\*\\*/g;\n      const parts = text.split(boldRegex);\n\n      if (parts.length === 1) {\n        return [text]; // No bold formatting found\n      }\n\n      const result: React.ReactNode[] = [];\n\n      // Process the parts - odd indices are the bold text\n      parts.forEach((part, index) => {\n        if (index % 2 === 0) {\n          // Regular text\n          if (part) result.push(part);\n        } else {\n          // Bold text\n          result.push(<strong key={`bold-${index}`}>{part}</strong>);\n        }\n      });\n\n      return result;\n    };\n\n    // Stage 2: Handle italic text (text between * *)\n    const processItalic = (nodes: React.ReactNode[]): React.ReactNode[] => {\n      const result: React.ReactNode[] = [];\n\n      nodes.forEach((node, nodeIndex) => {\n        if (typeof node !== 'string') {\n          // If it's already a React element, keep it as is\n          result.push(node);\n          return;\n        }\n\n        const text = node as string;\n        const italicRegex = /\\*(.*?)\\*/g;\n        const parts = text.split(italicRegex);\n\n        if (parts.length === 1) {\n          result.push(text); // No italic formatting found\n          return;\n        }\n\n        // Process the parts - odd indices are the italic text\n        parts.forEach((part, index) => {\n          if (index % 2 === 0) {\n            // Regular text\n            if (part) result.push(part);\n          } else {\n            // Italic text\n            result.push(<em key={`italic-${nodeIndex}-${index}`}>{part}</em>);\n          }\n        });\n      });\n\n      return result;\n    };\n\n    // Stage 3: Handle headers (lines starting with # or ## or ###)\n    const processHeaders = (text: string, keyPrefix: string = 'header'): React.ReactNode => {\n      if (!text) return text;\n\n      const trimmedText = text.trim();\n\n      // Check if the text starts with header markers\n      if (trimmedText.startsWith('### ')) {\n        return <h3 key={`${keyPrefix}-h3`} className=\"text-lg font-bold mt-5 mb-3 text-gray-800 dark:text-gray-200\">{trimmedText.substring(4)}</h3>;\n      } else if (trimmedText.startsWith('## ')) {\n        return <h2 key={`${keyPrefix}-h2`} className=\"text-xl font-bold mt-6 mb-3 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-2\">{trimmedText.substring(3)}</h2>;\n      } else if (trimmedText.startsWith('# ')) {\n        return <h1 key={`${keyPrefix}-h1`} className=\"text-2xl font-bold mt-6 mb-4 text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2\">{trimmedText.substring(2)}</h1>;\n      }\n\n      // Check if it's a list item\n      if (trimmedText.startsWith('- ')) {\n        return <li key={`${keyPrefix}-li`} className=\"ml-5 mb-2 text-base leading-relaxed\">{trimmedText.substring(2)}</li>;\n      }\n\n      // If it's not a header or list item, process other markdown elements\n      const boldProcessed = processBold(text);\n      const italicProcessed = processItalic(boldProcessed);\n\n      // For regular text, return as a React fragment to prevent unwanted p nesting\n      return <React.Fragment key={`${keyPrefix}-fragment`}>{italicProcessed}</React.Fragment>;\n    };\n\n    // Check if the content is a header or list item first\n    // Handle headers and list items first\n    const trimmedContent = content.trim();\n    if (trimmedContent.startsWith('#') || trimmedContent.startsWith('- ')) {\n      return processHeaders(content, keyPrefix);\n    }\n\n    // Otherwise process other markdown elements\n    const boldProcessed = processBold(content);\n    const italicProcessed = processItalic(boldProcessed);\n\n    return italicProcessed;\n  };\n\n  // If there's no sentence analysis data, just return the text with markdown processing\n  if (!sentenceAnalysis || !Array.isArray(sentenceAnalysis) || sentenceAnalysis.length === 0) {\n    return <div className=\"space-y-4\">{processMarkdown(text, 'main-content')}</div>;\n  }\n\n  // Create a map of sentences to their reference numbers\n  const sentenceToReferenceMap = new Map<string, number>();\n  sentenceAnalysis.forEach((item, index) => {\n    sentenceToReferenceMap.set(item.sentence, index + 1);\n  });\n\n  // Split the text into paragraphs\n  const paragraphs = text.split('\\n').filter(p => p.trim() !== '');\n\n  // Check if we have a list of items\n  const isList = paragraphs.some(p => p.trim().startsWith('- '));\n\n  // Process each paragraph to add reference numbers\n  const processedParagraphs = paragraphs.map((paragraph, paragraphIndex) => {\n    // Check if this paragraph is a header or list item\n    const trimmedParagraph = paragraph.trim();\n    if (trimmedParagraph.startsWith('#') || trimmedParagraph.startsWith('- ')) {\n      // Find all sentences in this paragraph that have references\n      const matchingSentences = sentenceAnalysis.filter(item =>\n        paragraph.includes(item.sentence)\n      );\n\n      if (matchingSentences.length === 0) {\n        // If no matching sentences, process the markdown formatting\n        return processMarkdown(paragraph, `para-${paragraphIndex}`);\n      }\n    }\n\n    // Find all sentences in this paragraph that have references\n    const matchingSentences = sentenceAnalysis.filter(item =>\n      paragraph.includes(item.sentence)\n    );\n\n    if (matchingSentences.length === 0) {\n      // If no matching sentences, return the paragraph with markdown processing\n      const processed = processMarkdown(paragraph, `para-content-${paragraphIndex}`);\n\n      // Check if the processed content is a header element\n      if (React.isValidElement(processed) &&\n          typeof processed.type === 'string' &&\n          ['h1','h2','h3'].includes(processed.type)) {\n        // Return header elements directly without wrapping in <p>\n        return processed;\n      } else {\n        // For non-header content, check if it contains header elements\n        const paragraphText = paragraph.trim();\n        if (paragraphText.startsWith('### ') || paragraphText.startsWith('## ') || paragraphText.startsWith('# ')) {\n          // If the paragraph starts with header markdown, process it as a header\n          return processMarkdown(paragraph, `para-content-${paragraphIndex}`);\n        } else {\n          // Safe to wrap in <p> tag\n          return <p key={`paragraph-${paragraphIndex}`}>{processed}</p>;\n        }\n      }\n    }\n\n    // Sort matching sentences by their position in the paragraph\n    matchingSentences.sort((a, b) => {\n      return paragraph.indexOf(a.sentence) - paragraph.indexOf(b.sentence);\n    });\n\n    // Process the paragraph to add reference numbers\n    let lastIndex = 0;\n    const parts: React.ReactNode[] = [];\n\n    matchingSentences.forEach((item, index) => {\n      const sentenceIndex = paragraph.indexOf(item.sentence, lastIndex);\n\n      if (sentenceIndex === -1) return; // Skip if sentence not found\n\n      // Add text before the sentence with markdown processing\n      if (sentenceIndex > lastIndex) {\n        parts.push(\n          <span key={`before-${paragraphIndex}-${index}`}>\n            {processMarkdown(paragraph.substring(lastIndex, sentenceIndex), `before-md-${paragraphIndex}-${index}`)}\n          </span>\n        );\n      }\n\n      // Get the reference number for this sentence\n      const referenceNumber = sentenceToReferenceMap.get(item.sentence);\n\n      // Add the sentence with the reference number and markdown processing\n      parts.push(\n        <span key={`sentence-${paragraphIndex}-${index}`}>\n          {processMarkdown(item.sentence, `sentence-md-${paragraphIndex}-${index}`)}\n          <span\n            ref={setReferenceSpanRef(referenceNumber || 0)}\n            className=\"reference-number-hover inline-flex items-center justify-center ml-0.5 text-[10px] font-medium text-primaryColor bg-primaryColor/10 rounded-full w-[16px] h-[16px] hover:bg-primaryColor/20 transition-all duration-200\"\n            onMouseEnter={() => handleReferenceMouseEnter(referenceNumber || 0, item.url, item.summary)}\n            onMouseLeave={handleReferenceMouseLeave}\n            // Removed onClick handler to prevent navigation\n            style={{ verticalAlign: 'super' }}\n          >\n            {referenceNumber}\n          </span>\n        </span>\n      );\n\n      lastIndex = sentenceIndex + item.sentence.length;\n    });\n\n    // Add any remaining text after the last matched sentence with markdown processing\n    if (lastIndex < paragraph.length) {\n      parts.push(\n        <span key={`after-${paragraphIndex}`}>\n          {processMarkdown(paragraph.substring(lastIndex), `after-md-${paragraphIndex}`)}\n        </span>\n      );\n    }\n\n    // Check if any part contains header elements or if the paragraph starts with header markdown\n    const hasHeader = parts.some(part =>\n      React.isValidElement(part) &&\n      typeof part.type === 'string' &&\n      ['h1','h2','h3'].includes(part.type)\n    );\n\n    // Also check if the original paragraph text contains header markdown\n    const paragraphText = paragraph.trim();\n    const containsHeaderMarkdown = paragraphText.includes('### ') || paragraphText.includes('## ') || paragraphText.includes('# ');\n\n    // If there are headers or header markdown, use a div container instead of p\n    return (hasHeader || containsHeaderMarkdown) ? (\n      <div key={`paragraph-${paragraphIndex}`} className=\"mb-4 leading-relaxed text-base\">{parts}</div>\n    ) : (\n      <p key={`paragraph-${paragraphIndex}`} className=\"mb-4 leading-relaxed text-base\">{parts}</p>\n    );\n  });\n\n  // Check if we need to wrap list items in a ul element\n  const wrappedProcessedParagraphs = isList ? (\n    <ul className=\"list-disc list-outside pl-5 my-4 space-y-2\">\n      {processedParagraphs.map((paragraph, index) => (\n        <React.Fragment key={`list-item-${index}`}>\n          {paragraph}\n        </React.Fragment>\n      ))}\n    </ul>\n  ) : (\n    processedParagraphs.map((paragraph, index) => (\n      <React.Fragment key={`paragraph-wrapper-${index}`}>\n        {paragraph}\n      </React.Fragment>\n    ))\n  );\n\n  const [showRelatedModal, setShowRelatedModal] = useState(false);\n\n\n  // Helper function to check if there are any valid references (not N/A or Not found)\n  const hasValidReferences = () => {\n    if (!sentenceAnalysis || sentenceAnalysis.length === 0) {\n      return false;\n    }\n\n    return sentenceAnalysis.some(item =>\n      item.url &&\n      item.url !== 'N/A' &&\n      item.url !== 'Not found' &&\n      item.url.trim() !== ''\n    );\n  };\n\n  return (\n    <div className=\"whitespace-pre-wrap relative\">\n      <div className=\"mb-6 leading-relaxed text-base\">\n        {wrappedProcessedParagraphs}\n      </div>\n\n      {/* Related Questions section */}\n      {relatedQuestions && relatedQuestions.length > 0 && (\n        <>\n          <div className=\"mt-6 pt-4 border-t border-primaryColor/10\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-sm font-medium text-n700 dark:text-n30 flex items-center\">\n                <PiLightbulb className=\"h-4 w-4 mr-2 text-primaryColor\" />\n                Related Questions\n              </h3>\n              <button\n                onClick={() => setShowRelatedModal(true)}\n                className=\"text-xs text-primaryColor hover:text-primaryColor/80 transition-colors duration-200 flex items-center\"\n              >\n                View All\n              </button>\n            </div>\n            <div className=\"flex flex-col gap-3\">\n              {relatedQuestions.slice(0, 3).map((question, index) => (\n                <motion.div\n                  key={`related-question-${index}`}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3, delay: index * 0.1 }}\n                  className=\"border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200\"\n                >\n                  <div\n                    className=\"flex justify-between items-center cursor-pointer\"\n                    onClick={() => {\n                      // Always select the question, no toggling\n                      handleQuestionSelect(question);\n                    }}\n                  >\n                    <div className=\"text-sm text-n700 dark:text-n30 flex-1 font-medium\">\n                      {/* Remove markdown formatting from the question */}\n                      {question.replace(/\\*\\*/g, '')}\n                    </div>\n                    <div className=\"text-white p-1.5 rounded-md bg-gradient-to-r from-primaryColor to-blue-600 shadow-sm\">\n                      <PiCaretRight />\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Modal for Related Questions */}\n          <AnimatePresence>\n            {showRelatedModal && (\n              <div className=\"fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4\">\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  transition={{ duration: 0.2 }}\n                  className=\"bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col\"\n                >\n                  <div className=\"p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center\">\n                    <h3 className=\"text-lg font-medium text-n700 dark:text-n30\">Related Questions</h3>\n                    <button\n                      onClick={() => setShowRelatedModal(false)}\n                      className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n                    >\n                      <PiX className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                  <div className=\"p-4 overflow-y-auto flex-1\">\n                    <div className=\"grid gap-3\">\n                      {relatedQuestions.map((question, index) => (\n                        <motion.div\n                          key={`modal-question-${index}`}\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ duration: 0.2, delay: index * 0.05 }}\n                          className=\"border border-primaryColor/10 rounded-lg p-3 hover:bg-primaryColor/5 transition-colors duration-200\"\n                        >\n                          <div\n                            className=\"flex justify-between items-center cursor-pointer\"\n                            onClick={() => {\n                              handleQuestionSelect(question);\n                              setShowRelatedModal(false);\n                            }}\n                          >\n                            <div className=\"text-sm text-n700 dark:text-n30 flex-1\">\n                              {question.replace(/\\*\\*/g, '')}\n                            </div>\n                            <div className=\"bg-gradient-to-r from-primaryColor to-blue-600 text-white p-1.5 rounded-md shadow-sm\">\n                              <PiCaretRight />\n                            </div>\n                          </div>\n                        </motion.div>\n                      ))}\n                    </div>\n                  </div>\n                </motion.div>\n              </div>\n            )}\n          </AnimatePresence>\n        </>\n      )}\n\n      {/* References section at the bottom - more compact design */}\n      {sentenceAnalysis && sentenceAnalysis.length > 0 && hasValidReferences() && (\n        <div className=\"mt-4 pt-3 border-t border-primaryColor/10\">\n          <div className=\"flex items-center mb-2\">\n            <PiLink className=\"h-3.5 w-3.5 mr-1.5 text-primaryColor\" />\n            <h3 className=\"text-xs font-medium text-n700 dark:text-n30\">References</h3>\n            <span className=\"ml-2 text-xs text-n400 dark:text-n200\">\n              {sentenceAnalysis.length} source{sentenceAnalysis.length !== 1 ? 's' : ''}\n            </span>\n          </div>\n\n          <div className=\"flex flex-wrap gap-1.5\">\n                {sentenceAnalysis.map((item, index) => {\n                  const referenceNumber = index + 1;\n                  // Extract domain name for display\n                  let domain = '';\n                  try {\n                    const url = new URL(item.url);\n                    domain = url.hostname.replace('www.', '');\n\n                    // Clean up common domain patterns\n                    if (domain.includes('youtube.com') || domain.includes('youtu.be')) {\n                      domain = 'YouTube';\n                    } else if (domain.includes('economictimes.indiatimes.com')) {\n                      domain = 'Economic Times';\n                    } else if (domain.includes('moneycontrol.com')) {\n                      domain = 'MoneyControl';\n                    } else if (domain.includes('financialexpress.com')) {\n                      domain = 'Financial Express';\n                    } else {\n                      // Capitalize first letter and remove common TLDs for display\n                      domain = domain.split('.')[0];\n                      domain = domain.charAt(0).toUpperCase() + domain.slice(1);\n                    }\n                  } catch (error) {\n                    // If URL parsing fails, try to extract a readable name\n                    domain = item.url.length > 20 ? item.url.substring(0, 20) + '...' : item.url;\n                  }\n\n                  return (\n                    <motion.button\n                      key={`reference-compact-${index}`}\n                      ref={setReferenceRef(referenceNumber)}\n                      initial={{ opacity: 0, scale: 0.95 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ duration: 0.2, delay: index * 0.03 }}\n                      className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-[10px] font-medium transition-all duration-200 ${\n                        activeReference === referenceNumber\n                          ? 'bg-primaryColor text-white'\n                          : 'bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20'\n                      }`}\n                      onMouseEnter={() => handleReferenceMouseEnter(referenceNumber, item.url, item.summary)}\n                      onMouseLeave={handleReferenceMouseLeave}\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleReferenceClick(item.url, referenceNumber);\n                      }}\n                      title={item.url}\n                    >\n                      <span className=\"font-bold text-[9px]\">{referenceNumber}</span>\n                      <span className=\"max-w-[70px] truncate\">{domain}</span>\n                    </motion.button>\n                  );\n                })}\n          </div>\n\n\n          {/* Detailed reference information - shown on hover via the preview */}\n        </div>\n      )}\n\n      {/* Floating preview - positioned absolutely to avoid nesting issues */}\n      <AnimatePresence>\n        {previewData && previewData.position && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 10 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 10 }}\n            transition={{ duration: 0.2 }}\n            className=\"fixed z-50 preview-container shadow-xl\"\n            style={{\n              left: `${previewData.position.x}px`,\n              top: previewData.position.y < 200 ? `${previewData.position.y + 20}px` : `${previewData.position.y - 200}px`\n            }}\n            // Add mouse enter/leave handlers to the preview container to prevent it from disappearing too quickly\n            onMouseEnter={() => {\n              // Track that we're hovering over the preview\n              isHoveringPreviewRef.current = true;\n\n              // Clear any hide timer\n              if (hideTimerRef.current) {\n                clearTimeout(hideTimerRef.current);\n                hideTimerRef.current = null;\n              }\n\n              // Keep the reference active\n              setActiveReference(previewData.referenceNumber);\n            }}\n            onMouseLeave={() => {\n              // Track that we're no longer hovering over the preview\n              isHoveringPreviewRef.current = false;\n\n              // Use a delay before hiding the preview\n              hideTimerRef.current = setTimeout(() => {\n                setActiveReference(null);\n                setPreviewData(null);\n                hideTimerRef.current = null;\n              }, 300);\n            }}\n          >\n            <WebpagePreview\n              url={previewData.url}\n              summary={previewData.summary}\n              referenceNumber={previewData.referenceNumber}\n            />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\nexport default PerplexityStyleResponse;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;;;;;;AA4BA,MAAM,0BAAkE,CAAC,EACvE,IAAI,EACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,SAAS,EAC7B;IACC,sCAAsC;IACtC,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,wBAAwB;IACpC,QAAQ,GAAG,CAAC,+BAA+B,kBAAkB;IAC7D,QAAQ,GAAG,CAAC,6BAA6B,OAAO;IAChD,IAAI,kBAAkB;QACpB,iBAAiB,OAAO,CAAC,CAAC,GAAG,IAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;IAC1E;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyC,CAAC;IACrE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0C,CAAC;IAC1E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACtD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACnD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAW;IAE7C,gDAAgD;IAChD,MAAM,yBAAyB,CAAC;QAC9B,qCAAqC;QACrC,MAAM,aAAa;QACnB,sCAAsC;QACtC,MAAM,cAAc;QACpB,uCAAuC;QACvC,MAAM,eAAe;QAErB,IAAI,WAAW,IAAI,CAAC,OAAO;YACzB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI,CAAC,OAAO;YACjC,OAAO;QACT,OAAO,IAAI,aAAa,IAAI,CAAC,OAAO;YAClC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,4EAA4E;IAC5E,MAAM,uBAAuB,CAAC,KAAa;QACzC,0BAA0B;QAC1B,wCAAmC;;QAEnC,mEAAmE;QACnE,MAAM;QACN,MAAM;QAEN,iFAAiF;QACjF,MAAM;IAuDR;IAEA,4BAA4B;IAC5B,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,iEAAiE;QAE7E,IAAI,kBAAkB;YACpB,+CAA+C;YAC/C,MAAM,gBAAgB,SAAS,OAAO,CAAC,SAAS;YAChD,QAAQ,GAAG,CAAC,6DAA6D;YAEzE,kDAAkD;YAClD,iBAAiB;YAEjB,sDAAsD;YACtD,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,IAAI,cAAc;gBAChB,aAAa,KAAK,GAAG;gBACrB,aAAa,aAAa,CAAC,IAAI,MAAM,SAAS;oBAAE,SAAS;gBAAK;gBAC9D,aAAa,KAAK;YACpB;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,2DAA2D;IAC3D,MAAM,kBAAkB,CAAC,kBAA4B,CAAC;YACpD,cAAc,OAAO,CAAC,gBAAgB,GAAG;QAC3C;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,kCAAkC;YAAE;YAAM;YAAkB;YAAkB;QAAiB;QAE3G,mCAAmC;QACnC,IAAI,oBAAoB,iBAAiB,MAAM,GAAG,GAAG;YACnD,QAAQ,GAAG,CAAC,kDAAkD;QAChE,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;QAEA,2BAA2B;QAC3B,QAAQ,GAAG,CAAC,6CAA6C;QACzD,QAAQ,GAAG,CAAC,wDAAwD,uBAAuB;IAC7F,GAAG;QAAC;QAAM;QAAkB;QAAkB;KAAiB;IAE/D,0CAA0C;IAC1C,MAAM,sBAAsB,CAAC,kBAA4B,CAAC;YACxD,kBAAkB,OAAO,CAAC,gBAAgB,GAAG;QAC/C;IAEA,gEAAgE;IAChE,MAAM,4BAA4B,CAAC,iBAAyB,KAAa;QACvE,gCAAgC;QAChC,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,aAAa,OAAO;YACjC,aAAa,OAAO,GAAG;QACzB;QAEA,uDAAuD;QACvD,mBAAmB;QAEnB,mCAAmC;QACnC,IAAI,gBAAgB,OAAO,EAAE;YAC3B,aAAa,gBAAgB,OAAO;QACtC;QAEA,sEAAsE;QACtE,gBAAgB,OAAO,GAAG,WAAW;YACnC,qCAAqC;YACrC,MAAM,cAAc,kBAAkB,OAAO,CAAC,gBAAgB;YAC9D,IAAI,aAAa;gBACf,MAAM,OAAO,YAAY,qBAAqB;gBAC9C,eAAe;oBACb;oBACA;oBACA;oBACA,UAAU;wBACR,GAAG,KAAK,IAAI;wBACZ,GAAG,KAAK,GAAG;oBACb;gBACF;YACF;YACA,gBAAgB,OAAO,GAAG;QAC5B,GAAG,MAAM,0CAA0C;IACrD;IAEA,6CAA6C;IAC7C,MAAM,4BAA4B;QAChC,6EAA6E;QAC7E,2BAA2B;QAC3B,aAAa,OAAO,GAAG,WAAW;YAChC,IAAI,CAAC,qBAAqB,OAAO,EAAE;gBACjC,mBAAmB;gBACnB,eAAe;YACjB;YACA,aAAa,OAAO,GAAG;QACzB,GAAG;IACL;IAEA,6DAA6D;IAC7D,sEAAsE;IAEtE,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,aAAa,gBAAgB,OAAO;YACtC;YACA,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,aAAa,OAAO;YACnC;QACF;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,MAAM,kBAAkB,CAAC,SAAiB,YAAoB,IAAI;QAChE,IAAI,CAAC,SAAS,OAAO;QAErB,sEAAsE;QAEtE,iDAAiD;QACjD,MAAM,cAAc,CAAC;YACnB,MAAM,YAAY;YAClB,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,OAAO;oBAAC;iBAAK,EAAE,2BAA2B;YAC5C;YAEA,MAAM,SAA4B,EAAE;YAEpC,oDAAoD;YACpD,MAAM,OAAO,CAAC,CAAC,MAAM;gBACnB,IAAI,QAAQ,MAAM,GAAG;oBACnB,eAAe;oBACf,IAAI,MAAM,OAAO,IAAI,CAAC;gBACxB,OAAO;oBACL,YAAY;oBACZ,OAAO,IAAI,eAAC,8OAAC;kCAA8B;uBAAlB,CAAC,KAAK,EAAE,OAAO;;;;;gBAC1C;YACF;YAEA,OAAO;QACT;QAEA,iDAAiD;QACjD,MAAM,gBAAgB,CAAC;YACrB,MAAM,SAA4B,EAAE;YAEpC,MAAM,OAAO,CAAC,CAAC,MAAM;gBACnB,IAAI,OAAO,SAAS,UAAU;oBAC5B,iDAAiD;oBACjD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,MAAM,OAAO;gBACb,MAAM,cAAc;gBACpB,MAAM,QAAQ,KAAK,KAAK,CAAC;gBAEzB,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,OAAO,IAAI,CAAC,OAAO,6BAA6B;oBAChD;gBACF;gBAEA,sDAAsD;gBACtD,MAAM,OAAO,CAAC,CAAC,MAAM;oBACnB,IAAI,QAAQ,MAAM,GAAG;wBACnB,eAAe;wBACf,IAAI,MAAM,OAAO,IAAI,CAAC;oBACxB,OAAO;wBACL,cAAc;wBACd,OAAO,IAAI,eAAC,8OAAC;sCAAyC;2BAAjC,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,OAAO;;;;;oBACrD;gBACF;YACF;YAEA,OAAO;QACT;QAEA,+DAA+D;QAC/D,MAAM,iBAAiB,CAAC,MAAc,YAAoB,QAAQ;YAChE,IAAI,CAAC,MAAM,OAAO;YAElB,MAAM,cAAc,KAAK,IAAI;YAE7B,+CAA+C;YAC/C,IAAI,YAAY,UAAU,CAAC,SAAS;gBAClC,qBAAO,8OAAC;oBAA2B,WAAU;8BAAgE,YAAY,SAAS,CAAC;mBAAnH,GAAG,UAAU,GAAG,CAAC;;;;;YACnC,OAAO,IAAI,YAAY,UAAU,CAAC,QAAQ;gBACxC,qBAAO,8OAAC;oBAA2B,WAAU;8BAAmH,YAAY,SAAS,CAAC;mBAAtK,GAAG,UAAU,GAAG,CAAC;;;;;YACnC,OAAO,IAAI,YAAY,UAAU,CAAC,OAAO;gBACvC,qBAAO,8OAAC;oBAA2B,WAAU;8BAAoH,YAAY,SAAS,CAAC;mBAAvK,GAAG,UAAU,GAAG,CAAC;;;;;YACnC;YAEA,4BAA4B;YAC5B,IAAI,YAAY,UAAU,CAAC,OAAO;gBAChC,qBAAO,8OAAC;oBAA2B,WAAU;8BAAuC,YAAY,SAAS,CAAC;mBAA1F,GAAG,UAAU,GAAG,CAAC;;;;;YACnC;YAEA,qEAAqE;YACrE,MAAM,gBAAgB,YAAY;YAClC,MAAM,kBAAkB,cAAc;YAEtC,6EAA6E;YAC7E,qBAAO,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;0BAAgC;eAA1B,GAAG,UAAU,SAAS,CAAC;;;;;QACrD;QAEA,sDAAsD;QACtD,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,IAAI;QACnC,IAAI,eAAe,UAAU,CAAC,QAAQ,eAAe,UAAU,CAAC,OAAO;YACrE,OAAO,eAAe,SAAS;QACjC;QAEA,4CAA4C;QAC5C,MAAM,gBAAgB,YAAY;QAClC,MAAM,kBAAkB,cAAc;QAEtC,OAAO;IACT;IAEA,sFAAsF;IACtF,IAAI,CAAC,oBAAoB,CAAC,MAAM,OAAO,CAAC,qBAAqB,iBAAiB,MAAM,KAAK,GAAG;QAC1F,qBAAO,8OAAC;YAAI,WAAU;sBAAa,gBAAgB,MAAM;;;;;;IAC3D;IAEA,uDAAuD;IACvD,MAAM,yBAAyB,IAAI;IACnC,iBAAiB,OAAO,CAAC,CAAC,MAAM;QAC9B,uBAAuB,GAAG,CAAC,KAAK,QAAQ,EAAE,QAAQ;IACpD;IAEA,iCAAiC;IACjC,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,OAAO;IAE7D,mCAAmC;IACnC,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,UAAU,CAAC;IAExD,kDAAkD;IAClD,MAAM,sBAAsB,WAAW,GAAG,CAAC,CAAC,WAAW;QACrD,mDAAmD;QACnD,MAAM,mBAAmB,UAAU,IAAI;QACvC,IAAI,iBAAiB,UAAU,CAAC,QAAQ,iBAAiB,UAAU,CAAC,OAAO;YACzE,4DAA4D;YAC5D,MAAM,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,OAChD,UAAU,QAAQ,CAAC,KAAK,QAAQ;YAGlC,IAAI,kBAAkB,MAAM,KAAK,GAAG;gBAClC,4DAA4D;gBAC5D,OAAO,gBAAgB,WAAW,CAAC,KAAK,EAAE,gBAAgB;YAC5D;QACF;QAEA,4DAA4D;QAC5D,MAAM,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,OAChD,UAAU,QAAQ,CAAC,KAAK,QAAQ;QAGlC,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,0EAA0E;YAC1E,MAAM,YAAY,gBAAgB,WAAW,CAAC,aAAa,EAAE,gBAAgB;YAE7E,qDAAqD;YACrD,kBAAI,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,cACrB,OAAO,UAAU,IAAI,KAAK,YAC1B;gBAAC;gBAAK;gBAAK;aAAK,CAAC,QAAQ,CAAC,UAAU,IAAI,GAAG;gBAC7C,0DAA0D;gBAC1D,OAAO;YACT,OAAO;gBACL,+DAA+D;gBAC/D,MAAM,gBAAgB,UAAU,IAAI;gBACpC,IAAI,cAAc,UAAU,CAAC,WAAW,cAAc,UAAU,CAAC,UAAU,cAAc,UAAU,CAAC,OAAO;oBACzG,uEAAuE;oBACvE,OAAO,gBAAgB,WAAW,CAAC,aAAa,EAAE,gBAAgB;gBACpE,OAAO;oBACL,0BAA0B;oBAC1B,qBAAO,8OAAC;kCAAuC;uBAAhC,CAAC,UAAU,EAAE,gBAAgB;;;;;gBAC9C;YACF;QACF;QAEA,6DAA6D;QAC7D,kBAAkB,IAAI,CAAC,CAAC,GAAG;YACzB,OAAO,UAAU,OAAO,CAAC,EAAE,QAAQ,IAAI,UAAU,OAAO,CAAC,EAAE,QAAQ;QACrE;QAEA,iDAAiD;QACjD,IAAI,YAAY;QAChB,MAAM,QAA2B,EAAE;QAEnC,kBAAkB,OAAO,CAAC,CAAC,MAAM;YAC/B,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,QAAQ,EAAE;YAEvD,IAAI,kBAAkB,CAAC,GAAG,QAAQ,6BAA6B;YAE/D,wDAAwD;YACxD,IAAI,gBAAgB,WAAW;gBAC7B,MAAM,IAAI,eACR,8OAAC;8BACE,gBAAgB,UAAU,SAAS,CAAC,WAAW,gBAAgB,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,OAAO;mBAD7F,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,OAAO;;;;;YAIlD;YAEA,6CAA6C;YAC7C,MAAM,kBAAkB,uBAAuB,GAAG,CAAC,KAAK,QAAQ;YAEhE,qEAAqE;YACrE,MAAM,IAAI,eACR,8OAAC;;oBACE,gBAAgB,KAAK,QAAQ,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC,EAAE,OAAO;kCACxE,8OAAC;wBACC,KAAK,oBAAoB,mBAAmB;wBAC5C,WAAU;wBACV,cAAc,IAAM,0BAA0B,mBAAmB,GAAG,KAAK,GAAG,EAAE,KAAK,OAAO;wBAC1F,cAAc;wBACd,gDAAgD;wBAChD,OAAO;4BAAE,eAAe;wBAAQ;kCAE/B;;;;;;;eAVM,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE,OAAO;;;;;YAelD,YAAY,gBAAgB,KAAK,QAAQ,CAAC,MAAM;QAClD;QAEA,kFAAkF;QAClF,IAAI,YAAY,UAAU,MAAM,EAAE;YAChC,MAAM,IAAI,eACR,8OAAC;0BACE,gBAAgB,UAAU,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,gBAAgB;eADpE,CAAC,MAAM,EAAE,gBAAgB;;;;;QAIxC;QAEA,6FAA6F;QAC7F,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,qBAC3B,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,SACrB,OAAO,KAAK,IAAI,KAAK,YACrB;gBAAC;gBAAK;gBAAK;aAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;QAGrC,qEAAqE;QACrE,MAAM,gBAAgB,UAAU,IAAI;QACpC,MAAM,yBAAyB,cAAc,QAAQ,CAAC,WAAW,cAAc,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC;QAEzH,4EAA4E;QAC5E,OAAO,AAAC,aAAa,uCACnB,8OAAC;YAAwC,WAAU;sBAAkC;WAA3E,CAAC,UAAU,EAAE,gBAAgB;;;;iCAEvC,8OAAC;YAAsC,WAAU;sBAAkC;WAA3E,CAAC,UAAU,EAAE,gBAAgB;;;;;IAEzC;IAEA,sDAAsD;IACtD,MAAM,6BAA6B,uBACjC,8OAAC;QAAG,WAAU;kBACX,oBAAoB,GAAG,CAAC,CAAC,WAAW,sBACnC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;0BACZ;eADkB,CAAC,UAAU,EAAE,OAAO;;;;;;;;;eAM7C,oBAAoB,GAAG,CAAC,CAAC,WAAW,sBAClC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sBACZ;WADkB,CAAC,kBAAkB,EAAE,OAAO;;;;;IAMrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAGzD,oFAAoF;IACpF,MAAM,qBAAqB;QACzB,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;YACtD,OAAO;QACT;QAEA,OAAO,iBAAiB,IAAI,CAAC,CAAA,OAC3B,KAAK,GAAG,IACR,KAAK,GAAG,KAAK,SACb,KAAK,GAAG,KAAK,eACb,KAAK,GAAG,CAAC,IAAI,OAAO;IAExB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,oBAAoB,iBAAiB,MAAM,GAAG,mBAC7C;;kCACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,8IAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAmC;;;;;;;kDAG5D,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAC3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,WAAU;kDAEV,cAAA,8OAAC;4CACC,WAAU;4CACV,SAAS;gDACP,0CAA0C;gDAC1C,qBAAqB;4CACvB;;8DAEA,8OAAC;oDAAI,WAAU;8DAEZ,SAAS,OAAO,CAAC,SAAS;;;;;;8DAE7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;uCAlBZ,CAAC,iBAAiB,EAAE,OAAO;;;;;;;;;;;;;;;;kCA2BxC,8OAAC,yLAAA,CAAA,kBAAe;kCACb,kCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8C;;;;;;0DAC5D,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAK;oDACjD,WAAU;8DAEV,cAAA,8OAAC;wDACC,WAAU;wDACV,SAAS;4DACP,qBAAqB;4DACrB,oBAAoB;wDACtB;;0EAEA,8OAAC;gEAAI,WAAU;0EACZ,SAAS,OAAO,CAAC,SAAS;;;;;;0EAE7B,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;mDAjBZ,CAAC,eAAe,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgCjD,oBAAoB,iBAAiB,MAAM,GAAG,KAAK,sCAClD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAK,WAAU;;oCACb,iBAAiB,MAAM;oCAAC;oCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;kCAI3E,8OAAC;wBAAI,WAAU;kCACR,iBAAiB,GAAG,CAAC,CAAC,MAAM;4BAC3B,MAAM,kBAAkB,QAAQ;4BAChC,kCAAkC;4BAClC,IAAI,SAAS;4BACb,IAAI;gCACF,MAAM,MAAM,IAAI,IAAI,KAAK,GAAG;gCAC5B,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ;gCAEtC,kCAAkC;gCAClC,IAAI,OAAO,QAAQ,CAAC,kBAAkB,OAAO,QAAQ,CAAC,aAAa;oCACjE,SAAS;gCACX,OAAO,IAAI,OAAO,QAAQ,CAAC,iCAAiC;oCAC1D,SAAS;gCACX,OAAO,IAAI,OAAO,QAAQ,CAAC,qBAAqB;oCAC9C,SAAS;gCACX,OAAO,IAAI,OAAO,QAAQ,CAAC,yBAAyB;oCAClD,SAAS;gCACX,OAAO;oCACL,6DAA6D;oCAC7D,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC7B,SAAS,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;gCACzD;4BACF,EAAE,OAAO,OAAO;gCACd,uDAAuD;gCACvD,SAAS,KAAK,GAAG,CAAC,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ,KAAK,GAAG;4BAC9E;4BAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,KAAK,gBAAgB;gCACrB,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAK;gCACnC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAK;gCACjD,WAAW,CAAC,8GAA8G,EACxH,oBAAoB,kBAChB,+BACA,iEACJ;gCACF,cAAc,IAAM,0BAA0B,iBAAiB,KAAK,GAAG,EAAE,KAAK,OAAO;gCACrF,cAAc;gCACd,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,qBAAqB,KAAK,GAAG,EAAE;gCACjC;gCACA,OAAO,KAAK,GAAG;;kDAEf,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAyB;;;;;;;+BAnBpC,CAAC,kBAAkB,EAAE,OAAO;;;;;wBAsBvC;;;;;;;;;;;;0BASV,8OAAC,yLAAA,CAAA,kBAAe;0BACb,eAAe,YAAY,QAAQ,kBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;wBACnC,KAAK,YAAY,QAAQ,CAAC,CAAC,GAAG,MAAM,GAAG,YAAY,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,YAAY,QAAQ,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;oBAC9G;oBACA,sGAAsG;oBACtG,cAAc;wBACZ,6CAA6C;wBAC7C,qBAAqB,OAAO,GAAG;wBAE/B,uBAAuB;wBACvB,IAAI,aAAa,OAAO,EAAE;4BACxB,aAAa,aAAa,OAAO;4BACjC,aAAa,OAAO,GAAG;wBACzB;wBAEA,4BAA4B;wBAC5B,mBAAmB,YAAY,eAAe;oBAChD;oBACA,cAAc;wBACZ,uDAAuD;wBACvD,qBAAqB,OAAO,GAAG;wBAE/B,wCAAwC;wBACxC,aAAa,OAAO,GAAG,WAAW;4BAChC,mBAAmB;4BACnB,eAAe;4BACf,aAAa,OAAO,GAAG;wBACzB,GAAG;oBACL;8BAEA,cAAA,8OAAC,+IAAA,CAAA,UAAc;wBACb,KAAK,YAAY,GAAG;wBACpB,SAAS,YAAY,OAAO;wBAC5B,iBAAiB,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;AAO1D;uCAEe"}}, {"offset": {"line": 8630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/components/chatComponents/BotReply.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\nimport Image from \"next/image\";\nimport logo from \"@/public/images/favicon.ico\";\nimport {\n  PiArrowsCounterClockwise,\n  PiCopy,\n  PiShareFat,\n  PiSpeakerHigh,\n  PiThumbsDown,\n  PiThumbsUp,\n  PiCaretUpDown\n} from \"react-icons/pi\";\nimport ReactMarkdown from 'react-markdown';\nimport LoadingIndicator from \"./LoadingIndicator\";\nimport PerplexityStyleResponse from \"./PerplexityStyleResponse\";\nimport ClientOnly from \"@/components/ui/ClientOnly\";\n\n// Define the shape of complex AI responses\nexport interface ComplexAIResponse {\n  summary?: string;\n  isCommandSuggestion?: boolean;\n  commands?: { command: string; label: string }[];\n  code?: string;\n  language?: string;\n  image?: string;\n  ai_response?: string;\n  text?: string;\n  related_questions?: string[];\n  sentence_analysis?: Array<{\n    sentence: string;\n    url: string;\n    summary?: string;\n  }>;\n}\n\n// Note: We're using ComplexAIResponse for type checking instead of a separate ResponseObject interface\n\ntype BotReplyProps = {\n  replyType: string;\n  setScroll: React.Dispatch<React.SetStateAction<boolean>>;\n  isAnimation: boolean;\n  aiResponse: any; // Changed from string | ComplexAIResponse | null to any\n  timestamp?: string;\n  messageId?: string; // No longer used for animation, kept for compatibility\n  selectedLanguage?: string; // Added for language-specific text-to-speech\n  onSelectQuestion?: (question: string) => void; // Added for related questions\n  isSharedView?: boolean; // Added for shared chat view\n};\n\nconst BotReply: React.FC<BotReplyProps> = ({\n  replyType, // Used to determine the type of reply (response, loading, etc.)\n  setScroll,\n  isAnimation,\n  aiResponse,\n  timestamp,\n  messageId,\n  selectedLanguage = \"English\", // Default to English if not provided\n  onSelectQuestion,\n  isSharedView = false // Default to false for normal chat view\n}) => {\n  const [copied, setCopied] = useState(false);\n  const [isSpeaking, setIsSpeaking] = useState(false);\n  const [feedback, setFeedback] = useState<'like' | 'dislike' | null>(null);\n  const speechSynthesisRef = useRef<SpeechSynthesisUtterance | null>(null);\n  const [showIndexes, setShowIndexes] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState<string | null>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Language detection functions\n  const isTamilText = (text: string): boolean => {\n    // Tamil Unicode range: \\u0B80-\\u0BFF\n    const tamilRegex = /[\\u0B80-\\u0BFF]/;\n    return tamilRegex.test(text);\n  };\n\n  const isTeluguText = (text: string): boolean => {\n    // Telugu Unicode range: \\u0C00-\\u0C7F\n    const teluguRegex = /[\\u0C00-\\u0C7F]/;\n    return teluguRegex.test(text);\n  };\n\n  // Language code mapping\n  const getLanguageCode = (language: string): string => {\n    const languageMap: Record<string, string> = {\n      \"English\": \"en-US\",\n      \"Tamil\": \"ta-IN\",\n      \"Telugu\": \"te-IN\"\n    };\n    return languageMap[language] || \"en-US\"; // Default to English if not found\n  };\n\n  // Function to render sentence analysis with URLs\n  const renderSentenceAnalysis = () => {\n    // We're now using the PerplexityStyleResponse component to display references\n    // This function is kept for backward compatibility but doesn't render anything\n    return null;\n  };\n\n  // Immediately set scroll when component mounts\n  useEffect(() => {\n    setScroll(true);\n  }, [setScroll]);\n\n  // Handle click outside to close the dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setShowIndexes(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  // Initialize speech synthesis voices\n  useEffect(() => {\n    // Some browsers need an explicit call to load voices\n    if (window.speechSynthesis) {\n      // Load voices if available\n      const loadVoices = () => {\n        const voices = window.speechSynthesis.getVoices();\n        if (voices.length > 0) {\n          console.log(`Loaded ${voices.length} speech synthesis voices`);\n\n          // Log available voices for debugging\n          voices.forEach(voice => {\n            console.log(`Voice: ${voice.name}, Lang: ${voice.lang}, Default: ${voice.default}`);\n          });\n\n          // Check if we have Tamil or Telugu voices\n          const tamilVoice = voices.find(v => v.lang.includes('ta') || v.name.toLowerCase().includes('tamil'));\n          const teluguVoice = voices.find(v => v.lang.includes('te') || v.name.toLowerCase().includes('telugu'));\n\n          console.log(`Tamil voice available: ${tamilVoice ? 'Yes - ' + tamilVoice.name : 'No'}`);\n          console.log(`Telugu voice available: ${teluguVoice ? 'Yes - ' + teluguVoice.name : 'No'}`);\n        } else {\n          console.warn(\"No speech synthesis voices loaded\");\n        }\n      };\n\n      // Try to load voices immediately\n      loadVoices();\n\n      // Also set up an event listener for when voices change/load\n      window.speechSynthesis.onvoiceschanged = loadVoices;\n\n      // Cleanup\n      return () => {\n        window.speechSynthesis.onvoiceschanged = null;\n      };\n    }\n  }, []);\n\n  // Cleanup effect to stop speech synthesis when component unmounts\n  useEffect(() => {\n    return () => {\n      // Always try to cancel speech synthesis on unmount, regardless of isSpeaking state\n      // This ensures we clean up properly even if state gets out of sync\n      if (window.speechSynthesis) {\n        window.speechSynthesis.cancel();\n\n        // Also clear the reference\n        if (speechSynthesisRef.current) {\n          speechSynthesisRef.current = null;\n        }\n      }\n    };\n  }, []);\n\n  // Added useEffect to ensure logging happens after render\n  useEffect(() => {\n    console.log(\"BotReply received aiResponse:\", aiResponse);\n    if (aiResponse === undefined) {\n      console.warn(\"Warning: BotReply received undefined aiResponse\");\n    } else if (aiResponse === null) {\n      console.warn(\"Warning: BotReply received null aiResponse\");\n    } else {\n      console.log(\"BotReply aiResponse type:\", typeof aiResponse);\n      if (typeof aiResponse === 'string') {\n        console.log(\"BotReply aiResponse string length:\", aiResponse.length);\n        console.log(\"BotReply aiResponse string sample:\", aiResponse.substring(0, 50) + (aiResponse.length > 50 ? '...' : ''));\n\n        // Try to parse if it looks like JSON\n        if (aiResponse.trim().startsWith('{') && aiResponse.trim().endsWith('}')) {\n          try {\n            const parsed = JSON.parse(aiResponse);\n            console.log(\"BotReply successfully parsed aiResponse as JSON:\", parsed);\n          } catch (e) {\n            console.log(\"BotReply aiResponse is not valid JSON\");\n          }\n        }\n      } else if (typeof aiResponse === 'object') {\n        console.log(\"BotReply aiResponse keys:\", Object.keys(aiResponse || {}));\n\n        // Check for common properties\n        if (aiResponse && 'ai_response' in aiResponse) {\n          console.log(\"BotReply found ai_response property:\",\n            typeof aiResponse.ai_response === 'string'\n              ? aiResponse.ai_response.substring(0, 50) + '...'\n              : aiResponse.ai_response);\n        }\n      }\n    }\n  }, [aiResponse]);\n\n  // Add a direct check for aiResponse to provide a fallback\n  const safeAiResponse = aiResponse === undefined || aiResponse === null\n    ? \"Sorry, there was an issue with this response.\"\n    : typeof aiResponse === 'string'\n      ? aiResponse\n      : typeof aiResponse === 'object' && aiResponse.ai_response\n        ? aiResponse.ai_response\n        : JSON.stringify(aiResponse, null, 2);\n\n  // Log the related questions if they exist\n  if (typeof aiResponse === 'object' && aiResponse !== null && Array.isArray(aiResponse.related_questions)) {\n    console.log(\"BotReply: Found related_questions in aiResponse:\", aiResponse.related_questions);\n  }\n\n  const [formattedTime, setFormattedTime] = useState('just now');\n\n  // Format time on client side to prevent hydration errors\n  useEffect(() => {\n    if (timestamp && typeof window !== 'undefined') {\n      const time = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n      setFormattedTime(time);\n    }\n  }, [timestamp]);\n\n  // Process the response to get the content to display\n  const getResponseContent = () => {\n    // Log the raw response for debugging\n    console.log(\"getResponseContent processing aiResponse:\", safeAiResponse);\n\n    // If it's null or undefined (should not happen with safeAiResponse)\n    if (safeAiResponse === null || safeAiResponse === undefined) {\n      console.log(\"safeAiResponse is null/undefined\");\n      return 'Sorry, there was an issue with this response.';\n    }\n\n    // If it's a string (which is the expected format from the API)\n    if (typeof safeAiResponse === 'string') {\n      // Just return the string directly - this is the AI response text\n      console.log(\"safeAiResponse is a string of length:\", safeAiResponse.length);\n\n      // Check if it's a JSON string that needs parsing\n      if (safeAiResponse.trim().startsWith('{') && safeAiResponse.trim().endsWith('}')) {\n        try {\n          const parsed = JSON.parse(safeAiResponse);\n          console.log(\"Successfully parsed JSON string:\", parsed);\n\n          // If the parsed object has an ai_response property, extract it\n          if (parsed.ai_response) {\n            console.log(\"Found ai_response in parsed JSON, using that\");\n            return typeof parsed.ai_response === 'string'\n              ? parsed.ai_response\n              : JSON.stringify(parsed.ai_response);\n          }\n\n          // If it has a text property\n          if (parsed.text) {\n            console.log(\"Found text property in parsed JSON\");\n            return typeof parsed.text === 'string'\n              ? parsed.text\n              : JSON.stringify(parsed.text);\n          }\n\n          // Return the parsed object as a string\n          return JSON.stringify(parsed);\n        } catch (e) {\n          console.log(\"Not valid JSON, keeping as string\");\n        }\n      }\n\n      return safeAiResponse;\n    }\n\n    // If somehow we got an object instead of a string\n    if (typeof safeAiResponse === 'object') {\n      console.log(\"safeAiResponse is an object with keys:\", Object.keys(safeAiResponse || {}));\n\n      // Cast to ResponseObject to access properties safely\n      const responseObj = safeAiResponse as ComplexAIResponse;\n\n      // If it has an ai_response property (maybe the whole API response was passed)\n      if (responseObj && 'ai_response' in responseObj) {\n        console.log(\"Found ai_response property in object\");\n\n        // Check for related_questions\n        if ('related_questions' in responseObj && Array.isArray(responseObj.related_questions)) {\n          console.log(\"Found related_questions in object:\", responseObj.related_questions);\n        }\n\n        const response = responseObj.ai_response;\n        return typeof response === 'string' ? response : JSON.stringify(response);\n      }\n\n      // If it has a summary property\n      if (responseObj && 'summary' in responseObj) {\n        console.log(\"Found summary property in object\");\n        const summary = responseObj.summary;\n        return typeof summary === 'string' ? summary : JSON.stringify(summary);\n      }\n\n      // If it has a text property (from message object)\n      if (responseObj && 'text' in responseObj) {\n        console.log(\"Found text property in object\");\n        const text = responseObj.text;\n        return typeof text === 'string' ? text : JSON.stringify(text);\n      }\n\n      // Check for sentence_analysis (just for logging, we'll handle display separately)\n      if (responseObj && 'sentence_analysis' in responseObj) {\n        console.log(\"Found sentence_analysis in object:\", responseObj.sentence_analysis);\n      }\n\n      // Last resort, stringify the object\n      console.log(\"No recognized properties, stringifying object\");\n      try {\n        return JSON.stringify(safeAiResponse, null, 2);\n      } catch (e) {\n        console.error(\"Failed to stringify safeAiResponse:\", e);\n        return \"Error: Could not process this response.\";\n      }\n    }\n\n    // Fallback for any other type\n    console.log(\"safeAiResponse is an unexpected type:\", typeof safeAiResponse);\n    try {\n      return String(safeAiResponse);\n    } catch (e) {\n      console.error(\"Failed to convert safeAiResponse to string:\", e);\n      return \"Error: Could not convert response to string.\";\n    }\n  };\n\n  // Get the response content and ensure it's not empty\n  const responseContent = getResponseContent();\n  console.log(\"BotReply: responseContent after processing:\",\n    typeof responseContent === 'string'\n      ? `${responseContent.substring(0, 50)}... (length: ${responseContent.length})`\n      : responseContent);\n\n  // Get API environment and Pinecone indexes if available\n  const apiEnvironment = typeof aiResponse === 'object' && aiResponse !== null\n    ? aiResponse.api_environment || 'production'\n    : 'production';\n\n  const pineconeIndexes = typeof aiResponse === 'object' && aiResponse !== null && Array.isArray(aiResponse.pinecone_indexes)\n    ? aiResponse.pinecone_indexes\n    : [];\n\n  // Handle index selection\n  const handleIndexSelect = (index: string) => {\n    setSelectedIndex(index);\n    // You could add functionality here to use the selected index\n    console.log(`Selected index: ${index}`);\n  };\n\n  // Process response for special features if it's an object\n  const getComplexResponse = () => {\n    if (!aiResponse) return null;\n\n    // If it's already an object with our expected structure\n    if (typeof aiResponse === 'object') {\n      // Check for related_questions at the top level\n      if (Array.isArray(aiResponse.related_questions) && aiResponse.related_questions.length > 0) {\n        console.log(\"Found related_questions at top level of object:\", aiResponse.related_questions);\n      }\n\n      // Check for related_questions in the text property\n      if (typeof aiResponse.text === 'object' && aiResponse.text !== null &&\n          Array.isArray(aiResponse.text.related_questions) && aiResponse.text.related_questions.length > 0) {\n        console.log(\"Found related_questions in text property:\", aiResponse.text.related_questions);\n      }\n\n      return aiResponse;\n    }\n\n    // If it's a string that might be a stringified object\n    if (typeof aiResponse === 'string') {\n      try {\n        const parsed = JSON.parse(aiResponse);\n        // Check if it has any of our complex properties\n        if (parsed.code || parsed.commands || parsed.isCommandSuggestion ||\n            (Array.isArray(parsed.sentence_analysis) && parsed.sentence_analysis.length > 0) ||\n            (Array.isArray(parsed.related_questions) && parsed.related_questions.length > 0)) {\n          console.log(\"Found complex response with properties:\", Object.keys(parsed));\n\n          // Log related questions if they exist\n          if (Array.isArray(parsed.related_questions) && parsed.related_questions.length > 0) {\n            console.log(\"Found related_questions in parsed JSON:\", parsed.related_questions);\n          }\n\n          return parsed;\n        }\n      } catch (e) {\n        // Not JSON, just a regular string\n        return null;\n      }\n    }\n\n    return null;\n  };\n\n  const complexResponse = getComplexResponse();\n\n  // Handle text-to-speech\n  const handleTextToSpeech = () => {\n    // Check if speech synthesis is available\n    if (!window.speechSynthesis) {\n      console.error('Speech synthesis not supported in this browser');\n      alert('Text-to-speech is not supported in your browser.');\n      return;\n    }\n\n    // If already speaking, stop it\n    if (isSpeaking) {\n      window.speechSynthesis.cancel();\n      setIsSpeaking(false);\n      return;\n    }\n\n    // Get the text to speak\n    let textToSpeak = responseContent;\n\n    // Check if we have valid text to speak\n    if (!textToSpeak || textToSpeak.trim() === '') {\n      console.error('No valid text to speak');\n      alert('No text available for speech synthesis.');\n      return;\n    }\n\n    // Clean up the text if it contains markdown or code\n    // This is a simple cleanup - you might want to enhance this\n    textToSpeak = textToSpeak.replace(/```[\\s\\S]*?```/g, 'Code block omitted for speech.');\n    textToSpeak = textToSpeak.replace(/`([^`]+)`/g, '$1');\n    textToSpeak = textToSpeak.replace(/\\[([^\\]]+)\\]\\([^)]+\\)/g, '$1');\n    textToSpeak = textToSpeak.replace(/#+\\s+/g, '');\n\n    // Determine the language to use for speech\n    let detectedLanguage = selectedLanguage;\n\n    // Auto-detect language from text if needed\n    const hasTamilChars = isTamilText(textToSpeak);\n    const hasTeluguChars = isTeluguText(textToSpeak);\n\n    if (hasTamilChars) {\n      detectedLanguage = \"Tamil\";\n      console.log(\"Detected Tamil text for speech synthesis\");\n    } else if (hasTeluguChars) {\n      detectedLanguage = \"Telugu\";\n      console.log(\"Detected Telugu text for speech synthesis\");\n    }\n\n    // Get the language code for speech synthesis\n    const languageCode = getLanguageCode(detectedLanguage);\n    console.log(`Using language code for speech synthesis: ${languageCode}`);\n\n    // Create a new SpeechSynthesisUtterance instance\n    const utterance = new SpeechSynthesisUtterance(textToSpeak);\n\n    // Set language\n    utterance.lang = languageCode;\n\n    // Special handling for Tamil and Telugu text\n    if (hasTamilChars || hasTeluguChars) {\n      // Log the text being spoken for debugging\n      console.log(`Speaking ${detectedLanguage} text: ${textToSpeak.substring(0, 100)}${textToSpeak.length > 100 ? '...' : ''}`);\n\n      // Count the number of characters in the text\n      const tamilCharCount = hasTamilChars ?\n        textToSpeak.split('').filter((char: string) => /[\\u0B80-\\u0BFF]/.test(char)).length : 0;\n\n      const teluguCharCount = hasTeluguChars ?\n        textToSpeak.split('').filter((char: string) => /[\\u0C00-\\u0C7F]/.test(char)).length : 0;\n\n      console.log(`${detectedLanguage} character count: ${tamilCharCount || teluguCharCount}`);\n\n      // If the text has a mix of Tamil/Telugu and other characters, we might need special handling\n      if ((tamilCharCount > 0 && tamilCharCount < textToSpeak.length / 2) ||\n          (teluguCharCount > 0 && teluguCharCount < textToSpeak.length / 2)) {\n        console.log(`Mixed text detected with ${detectedLanguage} characters`);\n      }\n    }\n\n    // Set properties\n    utterance.rate = 1.0; // Speech rate (0.1 to 10)\n    utterance.pitch = 1.0; // Speech pitch (0 to 2)\n    utterance.volume = 1.0; // Speech volume (0 to 1)\n\n    // Try to find an appropriate voice for the selected language\n    const voices = window.speechSynthesis.getVoices();\n    console.log(`Available voices for selection: ${voices.length}`);\n\n    // Enhanced voice selection logic\n    let selectedVoice = null;\n\n    // First try: exact language code match\n    selectedVoice = voices.find(v => v.lang === languageCode);\n\n    // Second try: language code prefix match (e.g., 'ta' for Tamil)\n    if (!selectedVoice) {\n      const langPrefix = languageCode.split('-')[0];\n      selectedVoice = voices.find(v => v.lang.startsWith(langPrefix));\n\n      // For Tamil and Telugu, try additional matching patterns\n      if (!selectedVoice) {\n        if (detectedLanguage === \"Tamil\") {\n          // Try to find any voice with 'tamil' in the name or 'ta' in the language code\n          selectedVoice = voices.find(v =>\n            v.name.toLowerCase().includes('tamil') ||\n            v.lang.includes('ta')\n          );\n        } else if (detectedLanguage === \"Telugu\") {\n          // Try to find any voice with 'telugu' in the name or 'te' in the language code\n          selectedVoice = voices.find(v =>\n            v.name.toLowerCase().includes('telugu') ||\n            v.lang.includes('te')\n          );\n        }\n      }\n    }\n\n    // Third try: find any Indian voice for Indian languages\n    if (!selectedVoice && (detectedLanguage === \"Tamil\" || detectedLanguage === \"Telugu\")) {\n      selectedVoice = voices.find(v =>\n        v.lang.includes('in-') || // Indian locale\n        v.name.toLowerCase().includes('india') ||\n        v.name.toLowerCase().includes('indian')\n      );\n    }\n\n    // If we found a voice, use it\n    if (selectedVoice) {\n      console.log(`Selected voice: ${selectedVoice.name} (${selectedVoice.lang}) for ${detectedLanguage}`);\n      utterance.voice = selectedVoice;\n    } else {\n      console.warn(`No matching voice found for ${languageCode} (${detectedLanguage}), using default voice`);\n\n      // As a last resort for Tamil/Telugu, try to use a voice that can handle Unicode properly\n      if (detectedLanguage === \"Tamil\" || detectedLanguage === \"Telugu\") {\n        // Try to find a Google voice as they tend to handle Unicode better\n        const googleVoice = voices.find(v => v.name.includes('Google'));\n        if (googleVoice) {\n          console.log(`Using Google voice as fallback: ${googleVoice.name}`);\n          utterance.voice = googleVoice;\n        }\n      }\n    }\n\n    // Set event handlers\n    utterance.onstart = () => {\n      setIsSpeaking(true);\n    };\n\n    utterance.onend = () => {\n      setIsSpeaking(false);\n      speechSynthesisRef.current = null;\n    };\n\n    utterance.onerror = (event) => {\n      // Extract more specific error information if available\n      const errorMessage = event.error || 'Unknown speech synthesis error';\n      const errorDetails = {\n        message: errorMessage,\n        timeStamp: event.timeStamp,\n        type: event.type,\n        bubbles: event.bubbles,\n        cancelable: event.cancelable\n      };\n\n      console.error('Speech synthesis error:', errorDetails);\n\n      // Reset state\n      setIsSpeaking(false);\n      speechSynthesisRef.current = null;\n\n      // Optionally show a user-friendly error message\n      // You could add a toast notification or other UI feedback here\n    };\n\n    // Store the utterance in the ref for later cancellation\n    speechSynthesisRef.current = utterance;\n\n    // Start speaking with error handling\n    try {\n      // Make sure any previous speech is cancelled\n      window.speechSynthesis.cancel();\n\n      // Special handling for Tamil and Telugu text in browsers that might not support it well\n      if ((detectedLanguage === \"Tamil\" || detectedLanguage === \"Telugu\") &&\n          (hasTamilChars || hasTeluguChars)) {\n\n        // Some browsers might have issues with Tamil/Telugu text\n        // Let's add some additional logging and handling\n        console.log(`Using special handling for ${detectedLanguage} text-to-speech`);\n\n        // Ensure the speech synthesis service is ready\n        window.speechSynthesis.cancel();\n\n        // Force a small delay before speaking to ensure the speech synthesis is ready\n        setTimeout(() => {\n          try {\n            // Start the speech\n            window.speechSynthesis.speak(utterance);\n            console.log(`Started speech synthesis for ${detectedLanguage} text`);\n\n            // Set speaking state\n            setIsSpeaking(true);\n          } catch (innerError) {\n            console.error(`Error in delayed speech synthesis for ${detectedLanguage}:`, innerError);\n            setIsSpeaking(false);\n            speechSynthesisRef.current = null;\n          }\n        }, 100);\n      } else {\n        // Standard handling for other languages\n        // Start the new speech\n        window.speechSynthesis.speak(utterance);\n\n        // Some browsers might not trigger the onstart event properly\n        // Set a fallback timeout to check if speaking actually started\n        setTimeout(() => {\n          if (speechSynthesisRef.current === utterance && !isSpeaking) {\n            console.warn('Speech synthesis may not have started properly');\n            setIsSpeaking(true); // Force update state if onstart didn't fire\n          }\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Exception during speech synthesis initialization:', error);\n      setIsSpeaking(false);\n      speechSynthesisRef.current = null;\n      alert('Failed to start text-to-speech. Please try again.');\n    }\n  };\n\n  // Handle feedback (like/dislike)\n  const handleFeedback = (type: 'like' | 'dislike') => {\n    // Toggle feedback if already selected\n    if (feedback === type) {\n      setFeedback(null);\n    } else {\n      setFeedback(type);\n    }\n\n    // Here you would typically send the feedback to your backend\n    // This is a placeholder for the actual implementation\n    console.log(`User ${feedback === type ? 'removed' : 'gave'} ${type} feedback for message ID: ${messageId}`);\n\n    // Example of how you might send this to an API\n    // if (messageId) {\n    //   fetch('/api/feedback', {\n    //     method: 'POST',\n    //     headers: { 'Content-Type': 'application/json' },\n    //     body: JSON.stringify({\n    //       messageId,\n    //       feedback: feedback === type ? null : type,\n    //       timestamp: new Date().toISOString()\n    //     })\n    //   });\n    // }\n  };\n\n  // Regenerate functionality is currently disabled in the UI\n\n  // Handle sharing\n  const handleShare = () => {\n    // Check if Web Share API is available\n    if (navigator.share) {\n      navigator.share({\n        title: 'AIQuill Response',\n        text: responseContent,\n        // url: window.location.href, // Uncomment if you want to share the URL\n      })\n      .then(() => console.log('Shared successfully'))\n      .catch((error) => console.error('Error sharing:', error));\n    } else {\n      // Fallback for browsers that don't support the Web Share API\n      handleCopy();\n      alert('Link copied to clipboard! The Web Share API is not supported in this browser.');\n    }\n  };\n\n  // Handle copy to clipboard\n  const handleCopy = () => {\n    if (responseContent) {\n      navigator.clipboard.writeText(responseContent);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  // Fallback if no content\n  if ((!responseContent || (typeof responseContent === 'string' && responseContent.trim() === '')) && !isAnimation) {\n    console.warn(\"BotReply: No content to display\", {\n      aiResponseType: typeof aiResponse,\n      safeAiResponseType: typeof safeAiResponse,\n      responseContentType: typeof responseContent,\n      aiResponseValue: aiResponse,\n      safeAiResponseValue: safeAiResponse\n    });\n\n    // Try to display the raw safeAiResponse directly\n    let directDisplay = null;\n\n    if (typeof safeAiResponse === 'string') {\n      directDisplay = safeAiResponse;\n    } else if (typeof safeAiResponse === 'object' && safeAiResponse !== null) {\n      // Cast to ComplexAIResponse to access properties safely\n      const responseObj = safeAiResponse as ComplexAIResponse;\n\n      // Try to extract text from various possible properties\n      if ('ai_response' in responseObj) {\n        directDisplay = responseObj.ai_response;\n      } else if ('text' in responseObj) {\n        directDisplay = responseObj.text;\n      } else if ('summary' in responseObj) {\n        directDisplay = responseObj.summary;\n      } else {\n        // Last resort, stringify the object\n        try {\n          directDisplay = JSON.stringify(safeAiResponse, null, 2);\n        } catch (e) {\n          directDisplay = \"Error: Could not stringify response object.\";\n        }\n      }\n    } else {\n      // For any other type, convert to string\n      directDisplay = String(safeAiResponse);\n    }\n\n    return (\n      <div className=\"flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]\">\n        <Image src={logo} alt=\"AIQuill logo\" className=\"max-sm:size-5 object-cover\" width={32} height={32} />\n        <div className=\"flex flex-col justify-start items-start gap-3 flex-1\">\n          <div className=\"flex justify-between items-center w-full\">\n            <p className=\"text-xs text-n100\">QueryOne, {formattedTime}</p>\n\n            {/* API Environment and Pinecone Indexes Dropdown */}\n            <div className=\"relative\" ref={dropdownRef}>\n              <div\n                className={`text-xs mb-2 flex items-center gap-1 cursor-pointer ${apiEnvironment === 'development' ? 'text-blue-500' : 'text-gray-400'}`}\n                onClick={() => apiEnvironment === 'development' && pineconeIndexes.length > 0 && setShowIndexes(!showIndexes)}\n              >\n                <span>API: {apiEnvironment === 'development' ? 'Development' : 'Production'}</span>\n                {apiEnvironment === 'development' && pineconeIndexes.length > 0 && (\n                  <PiCaretUpDown className=\"inline-block\" />\n                )}\n              </div>\n\n              {/* Pinecone Indexes Dropdown */}\n              {showIndexes && apiEnvironment === 'development' && pineconeIndexes.length > 0 && (\n                <div className=\"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 min-w-[200px]\">\n                  <div className=\"py-1 px-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700\">\n                    Pinecone Indexes\n                  </div>\n                  <div className=\"max-h-[200px] overflow-y-auto\">\n                    {pineconeIndexes.map((index: string, i: number) => (\n                      <div\n                        key={i}\n                        className={`py-2 px-3 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}\n                        onClick={() => handleIndexSelect(index)}\n                      >\n                        {index}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"text-sm bg-primaryColor/5 py-3 px-5 border border-primaryColor/20 rounded-lg w-full sm:max-w-[90%]\">\n            {directDisplay ? (\n              <div className=\"whitespace-pre-wrap\">\n                {typeof aiResponse === 'object' && aiResponse !== null &&\n                (Array.isArray(aiResponse.sentence_analysis) || Array.isArray(aiResponse.related_questions)) ? (\n                  <ClientOnly fallback={<ReactMarkdown>{directDisplay}</ReactMarkdown>}>\n                    <PerplexityStyleResponse\n                      text={directDisplay}\n                      sentenceAnalysis={aiResponse.sentence_analysis || []}\n                      relatedQuestions={aiResponse.related_questions || []}\n                      selectedLanguage={selectedLanguage}\n                    />\n                  </ClientOnly>\n                ) : typeof aiResponse === 'object' && aiResponse !== null &&\n                   typeof aiResponse.text === 'object' && aiResponse.text !== null &&\n                   Array.isArray(aiResponse.text.related_questions) ? (\n                  <ClientOnly fallback={<ReactMarkdown>{typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : directDisplay}</ReactMarkdown>}>\n                    <PerplexityStyleResponse\n                      text={typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : directDisplay}\n                      sentenceAnalysis={aiResponse.text.sentence_analysis || []}\n                      relatedQuestions={aiResponse.text.related_questions || []}\n                      selectedLanguage={selectedLanguage}\n                    />\n                  </ClientOnly>\n                ) : (\n                  <ReactMarkdown>{directDisplay}</ReactMarkdown>\n                )}\n              </div>\n            ) : (\n              <>\n                <p>I'm sorry, there was an issue processing this response.</p>\n                {process.env.NODE_ENV === 'development' && (\n                  <div className=\"mt-2 text-xs text-gray-500\">\n                    <p>Debug info:</p>\n                    <p>aiResponse type: {typeof aiResponse}</p>\n                    <p>aiResponse value: {aiResponse ? (typeof aiResponse === 'string' ? aiResponse.substring(0, 100) + '...' : JSON.stringify(aiResponse)) : 'null'}</p>\n                  </div>\n                )}\n                {renderSentenceAnalysis()}\n              </>\n            )}\n          </div>\n          {!isSharedView && (\n            <div className=\"flex justify-end items-center gap-2\">\n            {/* <button\n                className={`p-1 hover:bg-gray-100 rounded-full ${isSpeaking ? 'bg-blue-100 text-blue-600' : ''}`}\n                title={isSpeaking ? \"Stop text to speech\" : \"Text to speech\"}\n                onClick={handleTextToSpeech}\n              >\n                <PiSpeakerHigh />\n              </button> */}\n              <button\n                className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'like' ? 'bg-green-100 text-green-600' : ''}`}\n                title=\"Like\"\n                onClick={() => handleFeedback('like')}\n              >\n                <PiThumbsUp />\n              </button>\n              <button\n                className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'dislike' ? 'bg-red-100 text-red-600' : ''}`}\n                title=\"Dislike\"\n                onClick={() => handleFeedback('dislike')}\n              >\n                <PiThumbsDown />\n              </button>\n              <button\n                className={`p-1 hover:bg-gray-100 rounded-full ${copied ? 'bg-green-100 text-green-600' : ''}`}\n                onClick={handleCopy}\n                title={copied ? \"Copied!\" : \"Copy to clipboard\"}\n              >\n                <PiCopy />\n              </button>\n              {/* <button\n                className=\"p-1 hover:bg-gray-100 rounded-full\"\n                title=\"Regenerate response\"\n                onClick={handleRegenerate}\n              >\n                <PiArrowsCounterClockwise />\n              </button> */}\n              <button\n                className=\"p-1 hover:bg-gray-100 rounded-full\"\n                title=\"Share response\"\n                onClick={handleShare}\n              >\n                <PiShareFat />\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  const renderCodeBlock = () => {\n    const code = complexResponse?.code;\n    const language = complexResponse?.language || 'plaintext';\n\n    if (code) {\n      return (\n        <div className=\"mt-3 p-3 bg-gray-100 rounded-md overflow-x-auto\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <span className=\"text-xs text-gray-500\">{language}</span>\n            <button\n              className=\"text-xs bg-gray-200 px-2 py-1 rounded hover:bg-gray-300\"\n              onClick={() => {\n                navigator.clipboard.writeText(code);\n                setCopied(true);\n                setTimeout(() => setCopied(false), 2000);\n              }}\n            >\n              {copied ? 'Copied!' : 'Copy'}\n            </button>\n          </div>\n          <pre className=\"text-sm\">\n            <code className={`language-${language}`}>\n              {code}\n            </code>\n          </pre>\n        </div>\n      );\n    }\n    return null;\n  };\n\n  const renderCommands = () => {\n    if (\n      complexResponse &&\n      complexResponse.isCommandSuggestion &&\n      complexResponse.commands?.length\n    ) {\n      return (\n        <div className=\"mt-3\">\n          <p className=\"font-medium mb-2\">Suggested commands:</p>\n          <div className=\"flex flex-wrap gap-2\">\n            {complexResponse.commands.map((cmd: { command: string; label: string }, index: number) => (\n              <button\n                key={index}\n                className=\"px-3 py-1 bg-primaryColor/10 text-primaryColor rounded-full text-xs hover:bg-primaryColor/20\"\n              >\n                {cmd.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      );\n    }\n    return null;\n  };\n\n\n\n  // Removed logDebugInfo function since we moved its logic directly into the useEffect\n\n  // Call the debug function\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development') {\n      console.log(\"BotReply Debug Info:\", {\n        aiResponseType: typeof aiResponse,\n        aiResponseSample: typeof aiResponse === 'string'\n          ? aiResponse.substring(0, 50) + (aiResponse.length > 50 ? '...' : '')\n          : JSON.stringify(aiResponse),\n        processedResponseSample: typeof responseContent === 'string'\n          ? responseContent.substring(0, 50) + (responseContent.length > 50 ? '...' : '')\n          : JSON.stringify(responseContent),\n        hasSentenceAnalysis: typeof aiResponse === 'object' && aiResponse !== null &&\n          Array.isArray(aiResponse.sentence_analysis) && aiResponse.sentence_analysis.length > 0\n      });\n\n      // Log sentence analysis data if available\n      if (typeof aiResponse === 'object' && aiResponse !== null &&\n          Array.isArray(aiResponse.sentence_analysis) && aiResponse.sentence_analysis.length > 0) {\n        console.log(\"Sentence analysis data found:\", aiResponse.sentence_analysis);\n      }\n\n      // Log related questions data if available\n      if (typeof aiResponse === 'object' && aiResponse !== null &&\n          Array.isArray(aiResponse.related_questions) && aiResponse.related_questions.length > 0) {\n        console.log(\"Related questions data found:\", aiResponse.related_questions);\n      }\n    }\n\n    // Set scroll based on reply type\n    if (replyType === 'loading' || replyType === 'response') {\n      setScroll(true);\n    }\n  }, [aiResponse, responseContent, replyType, setScroll]);\n\n  return (\n    <div className=\"flex justify-start items-start gap-1 sm:gap-3 w-full max-w-[90%]\">\n      <Image src={logo} alt=\"AIQuill logo\" className=\"max-sm:size-5 object-cover\" width={32} height={32} />\n      <div className=\"flex flex-col justify-start items-start gap-3 flex-1\">\n        {/* Debug info is now logged to console only */}\n        <div className=\"flex justify-between items-center w-full\">\n          <p className=\"text-xs text-n100\">QueryOne, {formattedTime}</p>\n\n          {/* API Environment and Pinecone Indexes Dropdown */}\n          <div className=\"relative\" ref={dropdownRef}>\n            {/* <div\n              className={`text-xs mb-2 flex items-center gap-1 cursor-pointer ${apiEnvironment === 'development' ? 'text-blue-500' : 'text-gray-400'}`}\n              onClick={() => apiEnvironment === 'development' && pineconeIndexes.length > 0 && setShowIndexes(!showIndexes)}\n            >\n              <span>API: {apiEnvironment === 'development' ? 'Development' : 'Production'}</span>\n              {apiEnvironment === 'development' && pineconeIndexes.length > 0 && (\n                <PiCaretUpDown className=\"inline-block\" />\n              )}\n            </div> */}\n\n            {/* Pinecone Indexes Dropdown */}\n            {showIndexes && apiEnvironment === 'development' && pineconeIndexes.length > 0 && (\n              <div className=\"absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 z-10 min-w-[200px]\">\n                <div className=\"py-1 px-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700\">\n                  Pinecone Indexes\n                </div>\n                <div className=\"max-h-[200px] overflow-y-auto\">\n                  {pineconeIndexes.map((index: string, i: number) => (\n                    <div\n                      key={i}\n                      className={`py-2 px-3 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}\n                      onClick={() => handleIndexSelect(index)}\n                    >\n                      {index}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div className=\"text-sm bg-primaryColor/5 py-3 px-5 border border-primaryColor/20 rounded-lg w-full sm:max-w-[90%]\">\n          {isAnimation || aiResponse === \"__LOADING__\" ? (\n            <LoadingIndicator\n              message=\"Generating answers for you\"\n              showSteps={true}\n              showAvatar={false}\n            />\n          ) : (\n            <div className=\"whitespace-pre-wrap\">\n              {responseContent && responseContent.trim() !== '' ? (\n                // Display the response with Perplexity-style references if sentence analysis or related questions are available\n                typeof aiResponse === 'object' && aiResponse !== null &&\n                (Array.isArray(aiResponse.sentence_analysis) || Array.isArray(aiResponse.related_questions)) ? (\n                  <ClientOnly fallback={<ReactMarkdown>{responseContent}</ReactMarkdown>}>\n                    <PerplexityStyleResponse\n                      text={responseContent}\n                      sentenceAnalysis={aiResponse.sentence_analysis || []}\n                      relatedQuestions={(() => {\n                        console.log(\"🔍 BotReply - Passing related questions:\", aiResponse.related_questions);\n                        return aiResponse.related_questions || [];\n                      })()}\n                      onSelectQuestion={(question) => {\n                        console.log(\"BotReply: Selected question:\", question);\n                        if (onSelectQuestion) {\n                          onSelectQuestion(question);\n                          // Also try to update the input directly\n                          const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;\n                          if (inputElement) {\n                            inputElement.value = question;\n                            inputElement.dispatchEvent(new Event('input', { bubbles: true }));\n                            inputElement.focus();\n                          }\n                        }\n                      }}\n                      selectedLanguage={selectedLanguage}\n                    />\n                  </ClientOnly>\n                ) : (\n                  // Check if we have a text object with related_questions\n                  typeof aiResponse === 'object' && aiResponse !== null &&\n                  typeof aiResponse.text === 'object' && aiResponse.text !== null &&\n                  Array.isArray(aiResponse.text.related_questions) ? (\n                    <ClientOnly fallback={<ReactMarkdown>{typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : responseContent}</ReactMarkdown>}>\n                      <PerplexityStyleResponse\n                        text={typeof aiResponse.text.ai_response === 'string' ? aiResponse.text.ai_response : responseContent}\n                        sentenceAnalysis={aiResponse.text.sentence_analysis || []}\n                        relatedQuestions={aiResponse.text.related_questions || []}\n                        onSelectQuestion={onSelectQuestion}\n                        selectedLanguage={selectedLanguage}\n                      />\n                    </ClientOnly>\n                  ) : (\n                    // Otherwise just show the markdown\n                    <ReactMarkdown>{responseContent}</ReactMarkdown>\n                  )\n                )\n              ) : typeof safeAiResponse === 'string' && safeAiResponse.trim() !== '' ? (\n                <ReactMarkdown>{safeAiResponse}</ReactMarkdown>\n              ) : (\n                <p>No response content available. Please try again.</p>\n              )}\n\n              {/* Render code blocks and commands */}\n              <>\n                {renderCodeBlock()}\n                {renderCommands()}\n              </>\n            </div>\n          )}\n        </div>\n        {!isSharedView && (\n          <div className=\"flex justify-end items-center gap-2\">\n            {/* <button\n              className={`p-1 hover:bg-gray-100 rounded-full ${isSpeaking ? 'bg-blue-100 text-blue-600' : ''}`}\n              title={isSpeaking ? \"Stop text to speech\" : \"Text to speech\"}\n              onClick={handleTextToSpeech}\n            >\n              <PiSpeakerHigh />\n            </button> */}\n            <button\n              className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'like' ? 'bg-green-100 text-green-600' : ''}`}\n              title=\"Like\"\n              onClick={() => handleFeedback('like')}\n            >\n              <PiThumbsUp />\n            </button>\n            <button\n              className={`p-1 hover:bg-gray-100 rounded-full ${feedback === 'dislike' ? 'bg-red-100 text-red-600' : ''}`}\n              title=\"Dislike\"\n              onClick={() => handleFeedback('dislike')}\n            >\n              <PiThumbsDown />\n            </button>\n            <button\n              className={`p-1 hover:bg-gray-100 rounded-full ${copied ? 'bg-green-100 text-green-600' : ''}`}\n              onClick={handleCopy}\n              title={copied ? \"Copied!\" : \"Copy to clipboard\"}\n            >\n              <PiCopy />\n            </button>\n            {/* <button\n              className=\"p-1 hover:bg-gray-100 rounded-full\"\n              title=\"Regenerate response\"\n              onClick={handleRegenerate}\n            >\n              <PiArrowsCounterClockwise />\n            </button> */}\n            <button\n              className=\"p-1 hover:bg-gray-100 rounded-full\"\n              title=\"Share response\"\n              onClick={handleShare}\n            >\n              <PiShareFat />\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default BotReply;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAWA;AACA;AACA;AAHA;AATA;;;;;;;;;;AA8CA,MAAM,WAAoC,CAAC,EACzC,SAAS,EACT,SAAS,EACT,WAAW,EACX,UAAU,EACV,SAAS,EACT,SAAS,EACT,mBAAmB,SAAS,EAC5B,gBAAgB,EAChB,eAAe,MAAM,wCAAwC;AAAzC,EACrB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmC;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,+BAA+B;IAC/B,MAAM,cAAc,CAAC;QACnB,qCAAqC;QACrC,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,MAAM,eAAe,CAAC;QACpB,sCAAsC;QACtC,MAAM,cAAc;QACpB,OAAO,YAAY,IAAI,CAAC;IAC1B;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAsC;YAC1C,WAAW;YACX,SAAS;YACT,UAAU;QACZ;QACA,OAAO,WAAW,CAAC,SAAS,IAAI,SAAS,kCAAkC;IAC7E;IAEA,iDAAiD;IACjD,MAAM,yBAAyB;QAC7B,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT;IAEA,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;IACZ,GAAG;QAAC;KAAU;IAEd,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,eAAe;YACjB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,IAAI,OAAO,eAAe,EAAE;YAC1B,2BAA2B;YAC3B,MAAM,aAAa;gBACjB,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;gBAC/C,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,CAAC,wBAAwB,CAAC;oBAE7D,qCAAqC;oBACrC,OAAO,OAAO,CAAC,CAAA;wBACb,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;oBACpF;oBAEA,0CAA0C;oBAC1C,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAC3F,MAAM,cAAc,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;oBAE5F,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa,WAAW,WAAW,IAAI,GAAG,MAAM;oBACtF,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,cAAc,WAAW,YAAY,IAAI,GAAG,MAAM;gBAC3F,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,iCAAiC;YACjC;YAEA,4DAA4D;YAC5D,OAAO,eAAe,CAAC,eAAe,GAAG;YAEzC,UAAU;YACV,OAAO;gBACL,OAAO,eAAe,CAAC,eAAe,GAAG;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,mFAAmF;YACnF,mEAAmE;YACnE,IAAI,OAAO,eAAe,EAAE;gBAC1B,OAAO,eAAe,CAAC,MAAM;gBAE7B,2BAA2B;gBAC3B,IAAI,mBAAmB,OAAO,EAAE;oBAC9B,mBAAmB,OAAO,GAAG;gBAC/B;YACF;QACF;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,iCAAiC;QAC7C,IAAI,eAAe,WAAW;YAC5B,QAAQ,IAAI,CAAC;QACf,OAAO,IAAI,eAAe,MAAM;YAC9B,QAAQ,IAAI,CAAC;QACf,OAAO;YACL,QAAQ,GAAG,CAAC,6BAA6B,OAAO;YAChD,IAAI,OAAO,eAAe,UAAU;gBAClC,QAAQ,GAAG,CAAC,sCAAsC,WAAW,MAAM;gBACnE,QAAQ,GAAG,CAAC,sCAAsC,WAAW,SAAS,CAAC,GAAG,MAAM,CAAC,WAAW,MAAM,GAAG,KAAK,QAAQ,EAAE;gBAEpH,qCAAqC;gBACrC,IAAI,WAAW,IAAI,GAAG,UAAU,CAAC,QAAQ,WAAW,IAAI,GAAG,QAAQ,CAAC,MAAM;oBACxE,IAAI;wBACF,MAAM,SAAS,KAAK,KAAK,CAAC;wBAC1B,QAAQ,GAAG,CAAC,oDAAoD;oBAClE,EAAE,OAAO,GAAG;wBACV,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF,OAAO,IAAI,OAAO,eAAe,UAAU;gBACzC,QAAQ,GAAG,CAAC,6BAA6B,OAAO,IAAI,CAAC,cAAc,CAAC;gBAEpE,8BAA8B;gBAC9B,IAAI,cAAc,iBAAiB,YAAY;oBAC7C,QAAQ,GAAG,CAAC,wCACV,OAAO,WAAW,WAAW,KAAK,WAC9B,WAAW,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QAC1C,WAAW,WAAW;gBAC9B;YACF;QACF;IACF,GAAG;QAAC;KAAW;IAEf,0DAA0D;IAC1D,MAAM,iBAAiB,eAAe,aAAa,eAAe,OAC9D,kDACA,OAAO,eAAe,WACpB,aACA,OAAO,eAAe,YAAY,WAAW,WAAW,GACtD,WAAW,WAAW,GACtB,KAAK,SAAS,CAAC,YAAY,MAAM;IAEzC,0CAA0C;IAC1C,IAAI,OAAO,eAAe,YAAY,eAAe,QAAQ,MAAM,OAAO,CAAC,WAAW,iBAAiB,GAAG;QACxG,QAAQ,GAAG,CAAC,oDAAoD,WAAW,iBAAiB;IAC9F;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAgD;;QAGhD;IACF,GAAG;QAAC;KAAU;IAEd,qDAAqD;IACrD,MAAM,qBAAqB;QACzB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,oEAAoE;QACpE,IAAI,mBAAmB,QAAQ,mBAAmB,WAAW;YAC3D,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,+DAA+D;QAC/D,IAAI,OAAO,mBAAmB,UAAU;YACtC,iEAAiE;YACjE,QAAQ,GAAG,CAAC,yCAAyC,eAAe,MAAM;YAE1E,iDAAiD;YACjD,IAAI,eAAe,IAAI,GAAG,UAAU,CAAC,QAAQ,eAAe,IAAI,GAAG,QAAQ,CAAC,MAAM;gBAChF,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,QAAQ,GAAG,CAAC,oCAAoC;oBAEhD,+DAA+D;oBAC/D,IAAI,OAAO,WAAW,EAAE;wBACtB,QAAQ,GAAG,CAAC;wBACZ,OAAO,OAAO,OAAO,WAAW,KAAK,WACjC,OAAO,WAAW,GAClB,KAAK,SAAS,CAAC,OAAO,WAAW;oBACvC;oBAEA,4BAA4B;oBAC5B,IAAI,OAAO,IAAI,EAAE;wBACf,QAAQ,GAAG,CAAC;wBACZ,OAAO,OAAO,OAAO,IAAI,KAAK,WAC1B,OAAO,IAAI,GACX,KAAK,SAAS,CAAC,OAAO,IAAI;oBAChC;oBAEA,uCAAuC;oBACvC,OAAO,KAAK,SAAS,CAAC;gBACxB,EAAE,OAAO,GAAG;oBACV,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,OAAO;QACT;QAEA,kDAAkD;QAClD,IAAI,OAAO,mBAAmB,UAAU;YACtC,QAAQ,GAAG,CAAC,0CAA0C,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAErF,qDAAqD;YACrD,MAAM,cAAc;YAEpB,8EAA8E;YAC9E,IAAI,eAAe,iBAAiB,aAAa;gBAC/C,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,IAAI,uBAAuB,eAAe,MAAM,OAAO,CAAC,YAAY,iBAAiB,GAAG;oBACtF,QAAQ,GAAG,CAAC,sCAAsC,YAAY,iBAAiB;gBACjF;gBAEA,MAAM,WAAW,YAAY,WAAW;gBACxC,OAAO,OAAO,aAAa,WAAW,WAAW,KAAK,SAAS,CAAC;YAClE;YAEA,+BAA+B;YAC/B,IAAI,eAAe,aAAa,aAAa;gBAC3C,QAAQ,GAAG,CAAC;gBACZ,MAAM,UAAU,YAAY,OAAO;gBACnC,OAAO,OAAO,YAAY,WAAW,UAAU,KAAK,SAAS,CAAC;YAChE;YAEA,kDAAkD;YAClD,IAAI,eAAe,UAAU,aAAa;gBACxC,QAAQ,GAAG,CAAC;gBACZ,MAAM,OAAO,YAAY,IAAI;gBAC7B,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC;YAC1D;YAEA,kFAAkF;YAClF,IAAI,eAAe,uBAAuB,aAAa;gBACrD,QAAQ,GAAG,CAAC,sCAAsC,YAAY,iBAAiB;YACjF;YAEA,oCAAoC;YACpC,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,OAAO,KAAK,SAAS,CAAC,gBAAgB,MAAM;YAC9C,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,OAAO;YACT;QACF;QAEA,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,yCAAyC,OAAO;QAC5D,IAAI;YACF,OAAO,OAAO;QAChB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO;QACT;IACF;IAEA,qDAAqD;IACrD,MAAM,kBAAkB;IACxB,QAAQ,GAAG,CAAC,+CACV,OAAO,oBAAoB,WACvB,GAAG,gBAAgB,SAAS,CAAC,GAAG,IAAI,aAAa,EAAE,gBAAgB,MAAM,CAAC,CAAC,CAAC,GAC5E;IAEN,wDAAwD;IACxD,MAAM,iBAAiB,OAAO,eAAe,YAAY,eAAe,OACpE,WAAW,eAAe,IAAI,eAC9B;IAEJ,MAAM,kBAAkB,OAAO,eAAe,YAAY,eAAe,QAAQ,MAAM,OAAO,CAAC,WAAW,gBAAgB,IACtH,WAAW,gBAAgB,GAC3B,EAAE;IAEN,yBAAyB;IACzB,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,6DAA6D;QAC7D,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO;IACxC;IAEA,0DAA0D;IAC1D,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,OAAO;QAExB,wDAAwD;QACxD,IAAI,OAAO,eAAe,UAAU;YAClC,+CAA+C;YAC/C,IAAI,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,WAAW,iBAAiB,CAAC,MAAM,GAAG,GAAG;gBAC1F,QAAQ,GAAG,CAAC,mDAAmD,WAAW,iBAAiB;YAC7F;YAEA,mDAAmD;YACnD,IAAI,OAAO,WAAW,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAC3D,MAAM,OAAO,CAAC,WAAW,IAAI,CAAC,iBAAiB,KAAK,WAAW,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,GAAG;gBACpG,QAAQ,GAAG,CAAC,6CAA6C,WAAW,IAAI,CAAC,iBAAiB;YAC5F;YAEA,OAAO;QACT;QAEA,sDAAsD;QACtD,IAAI,OAAO,eAAe,UAAU;YAClC,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,gDAAgD;gBAChD,IAAI,OAAO,IAAI,IAAI,OAAO,QAAQ,IAAI,OAAO,mBAAmB,IAC3D,MAAM,OAAO,CAAC,OAAO,iBAAiB,KAAK,OAAO,iBAAiB,CAAC,MAAM,GAAG,KAC7E,MAAM,OAAO,CAAC,OAAO,iBAAiB,KAAK,OAAO,iBAAiB,CAAC,MAAM,GAAG,GAAI;oBACpF,QAAQ,GAAG,CAAC,2CAA2C,OAAO,IAAI,CAAC;oBAEnE,sCAAsC;oBACtC,IAAI,MAAM,OAAO,CAAC,OAAO,iBAAiB,KAAK,OAAO,iBAAiB,CAAC,MAAM,GAAG,GAAG;wBAClF,QAAQ,GAAG,CAAC,2CAA2C,OAAO,iBAAiB;oBACjF;oBAEA,OAAO;gBACT;YACF,EAAE,OAAO,GAAG;gBACV,kCAAkC;gBAClC,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IAExB,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,yCAAyC;QACzC,IAAI,CAAC,OAAO,eAAe,EAAE;YAC3B,QAAQ,KAAK,CAAC;YACd,MAAM;YACN;QACF;QAEA,+BAA+B;QAC/B,IAAI,YAAY;YACd,OAAO,eAAe,CAAC,MAAM;YAC7B,cAAc;YACd;QACF;QAEA,wBAAwB;QACxB,IAAI,cAAc;QAElB,uCAAuC;QACvC,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,QAAQ,KAAK,CAAC;YACd,MAAM;YACN;QACF;QAEA,oDAAoD;QACpD,4DAA4D;QAC5D,cAAc,YAAY,OAAO,CAAC,mBAAmB;QACrD,cAAc,YAAY,OAAO,CAAC,cAAc;QAChD,cAAc,YAAY,OAAO,CAAC,0BAA0B;QAC5D,cAAc,YAAY,OAAO,CAAC,UAAU;QAE5C,2CAA2C;QAC3C,IAAI,mBAAmB;QAEvB,2CAA2C;QAC3C,MAAM,gBAAgB,YAAY;QAClC,MAAM,iBAAiB,aAAa;QAEpC,IAAI,eAAe;YACjB,mBAAmB;YACnB,QAAQ,GAAG,CAAC;QACd,OAAO,IAAI,gBAAgB;YACzB,mBAAmB;YACnB,QAAQ,GAAG,CAAC;QACd;QAEA,6CAA6C;QAC7C,MAAM,eAAe,gBAAgB;QACrC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,cAAc;QAEvE,iDAAiD;QACjD,MAAM,YAAY,IAAI,yBAAyB;QAE/C,eAAe;QACf,UAAU,IAAI,GAAG;QAEjB,6CAA6C;QAC7C,IAAI,iBAAiB,gBAAgB;YACnC,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,iBAAiB,OAAO,EAAE,YAAY,SAAS,CAAC,GAAG,OAAO,YAAY,MAAM,GAAG,MAAM,QAAQ,IAAI;YAEzH,6CAA6C;YAC7C,MAAM,iBAAiB,gBACrB,YAAY,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,OAAiB,kBAAkB,IAAI,CAAC,OAAO,MAAM,GAAG;YAExF,MAAM,kBAAkB,iBACtB,YAAY,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,OAAiB,kBAAkB,IAAI,CAAC,OAAO,MAAM,GAAG;YAExF,QAAQ,GAAG,CAAC,GAAG,iBAAiB,kBAAkB,EAAE,kBAAkB,iBAAiB;YAEvF,6FAA6F;YAC7F,IAAI,AAAC,iBAAiB,KAAK,iBAAiB,YAAY,MAAM,GAAG,KAC5D,kBAAkB,KAAK,kBAAkB,YAAY,MAAM,GAAG,GAAI;gBACrE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,iBAAiB,WAAW,CAAC;YACvE;QACF;QAEA,iBAAiB;QACjB,UAAU,IAAI,GAAG,KAAK,0BAA0B;QAChD,UAAU,KAAK,GAAG,KAAK,wBAAwB;QAC/C,UAAU,MAAM,GAAG,KAAK,yBAAyB;QAEjD,6DAA6D;QAC7D,MAAM,SAAS,OAAO,eAAe,CAAC,SAAS;QAC/C,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,OAAO,MAAM,EAAE;QAE9D,iCAAiC;QACjC,IAAI,gBAAgB;QAEpB,uCAAuC;QACvC,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAE5C,gEAAgE;QAChE,IAAI,CAAC,eAAe;YAClB,MAAM,aAAa,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE;YAC7C,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,UAAU,CAAC;YAEnD,yDAAyD;YACzD,IAAI,CAAC,eAAe;gBAClB,IAAI,qBAAqB,SAAS;oBAChC,8EAA8E;oBAC9E,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAC1B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC9B,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAEpB,OAAO,IAAI,qBAAqB,UAAU;oBACxC,+EAA+E;oBAC/E,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAC1B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAC9B,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAEpB;YACF;QACF;QAEA,wDAAwD;QACxD,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,WAAW,qBAAqB,QAAQ,GAAG;YACrF,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAC1B,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,gBAAgB;gBAC1C,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC9B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAElC;QAEA,8BAA8B;QAC9B,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,MAAM,EAAE,kBAAkB;YACnG,UAAU,KAAK,GAAG;QACpB,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,aAAa,EAAE,EAAE,iBAAiB,sBAAsB,CAAC;YAErG,yFAAyF;YACzF,IAAI,qBAAqB,WAAW,qBAAqB,UAAU;gBACjE,mEAAmE;gBACnE,MAAM,cAAc,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;gBACrD,IAAI,aAAa;oBACf,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,YAAY,IAAI,EAAE;oBACjE,UAAU,KAAK,GAAG;gBACpB;YACF;QACF;QAEA,qBAAqB;QACrB,UAAU,OAAO,GAAG;YAClB,cAAc;QAChB;QAEA,UAAU,KAAK,GAAG;YAChB,cAAc;YACd,mBAAmB,OAAO,GAAG;QAC/B;QAEA,UAAU,OAAO,GAAG,CAAC;YACnB,uDAAuD;YACvD,MAAM,eAAe,MAAM,KAAK,IAAI;YACpC,MAAM,eAAe;gBACnB,SAAS;gBACT,WAAW,MAAM,SAAS;gBAC1B,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,YAAY,MAAM,UAAU;YAC9B;YAEA,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,cAAc;YACd,cAAc;YACd,mBAAmB,OAAO,GAAG;QAE7B,gDAAgD;QAChD,+DAA+D;QACjE;QAEA,wDAAwD;QACxD,mBAAmB,OAAO,GAAG;QAE7B,qCAAqC;QACrC,IAAI;YACF,6CAA6C;YAC7C,OAAO,eAAe,CAAC,MAAM;YAE7B,wFAAwF;YACxF,IAAI,CAAC,qBAAqB,WAAW,qBAAqB,QAAQ,KAC9D,CAAC,iBAAiB,cAAc,GAAG;gBAErC,yDAAyD;gBACzD,iDAAiD;gBACjD,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,iBAAiB,eAAe,CAAC;gBAE3E,+CAA+C;gBAC/C,OAAO,eAAe,CAAC,MAAM;gBAE7B,8EAA8E;gBAC9E,WAAW;oBACT,IAAI;wBACF,mBAAmB;wBACnB,OAAO,eAAe,CAAC,KAAK,CAAC;wBAC7B,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,iBAAiB,KAAK,CAAC;wBAEnE,qBAAqB;wBACrB,cAAc;oBAChB,EAAE,OAAO,YAAY;wBACnB,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,iBAAiB,CAAC,CAAC,EAAE;wBAC5E,cAAc;wBACd,mBAAmB,OAAO,GAAG;oBAC/B;gBACF,GAAG;YACL,OAAO;gBACL,wCAAwC;gBACxC,uBAAuB;gBACvB,OAAO,eAAe,CAAC,KAAK,CAAC;gBAE7B,6DAA6D;gBAC7D,+DAA+D;gBAC/D,WAAW;oBACT,IAAI,mBAAmB,OAAO,KAAK,aAAa,CAAC,YAAY;wBAC3D,QAAQ,IAAI,CAAC;wBACb,cAAc,OAAO,4CAA4C;oBACnE;gBACF,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qDAAqD;YACnE,cAAc;YACd,mBAAmB,OAAO,GAAG;YAC7B,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,MAAM,iBAAiB,CAAC;QACtB,sCAAsC;QACtC,IAAI,aAAa,MAAM;YACrB,YAAY;QACd,OAAO;YACL,YAAY;QACd;QAEA,6DAA6D;QAC7D,sDAAsD;QACtD,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,aAAa,OAAO,YAAY,OAAO,CAAC,EAAE,KAAK,0BAA0B,EAAE,WAAW;IAE1G,+CAA+C;IAC/C,mBAAmB;IACnB,6BAA6B;IAC7B,sBAAsB;IACtB,uDAAuD;IACvD,6BAA6B;IAC7B,mBAAmB;IACnB,mDAAmD;IACnD,4CAA4C;IAC5C,SAAS;IACT,QAAQ;IACR,IAAI;IACN;IAEA,2DAA2D;IAE3D,iBAAiB;IACjB,MAAM,cAAc;QAClB,sCAAsC;QACtC,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO;gBACP,MAAM;YAER,GACC,IAAI,CAAC,IAAM,QAAQ,GAAG,CAAC,wBACvB,KAAK,CAAC,CAAC,QAAU,QAAQ,KAAK,CAAC,kBAAkB;QACpD,OAAO;YACL,6DAA6D;YAC7D;YACA,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,IAAI,iBAAiB;YACnB,UAAU,SAAS,CAAC,SAAS,CAAC;YAC9B,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,yBAAyB;IACzB,IAAI,CAAC,CAAC,mBAAoB,OAAO,oBAAoB,YAAY,gBAAgB,IAAI,OAAO,EAAG,KAAK,CAAC,aAAa;QAChH,QAAQ,IAAI,CAAC,mCAAmC;YAC9C,gBAAgB,OAAO;YACvB,oBAAoB,OAAO;YAC3B,qBAAqB,OAAO;YAC5B,iBAAiB;YACjB,qBAAqB;QACvB;QAEA,iDAAiD;QACjD,IAAI,gBAAgB;QAEpB,IAAI,OAAO,mBAAmB,UAAU;YACtC,gBAAgB;QAClB,OAAO,IAAI,OAAO,mBAAmB,YAAY,mBAAmB,MAAM;YACxE,wDAAwD;YACxD,MAAM,cAAc;YAEpB,uDAAuD;YACvD,IAAI,iBAAiB,aAAa;gBAChC,gBAAgB,YAAY,WAAW;YACzC,OAAO,IAAI,UAAU,aAAa;gBAChC,gBAAgB,YAAY,IAAI;YAClC,OAAO,IAAI,aAAa,aAAa;gBACnC,gBAAgB,YAAY,OAAO;YACrC,OAAO;gBACL,oCAAoC;gBACpC,IAAI;oBACF,gBAAgB,KAAK,SAAS,CAAC,gBAAgB,MAAM;gBACvD,EAAE,OAAO,GAAG;oBACV,gBAAgB;gBAClB;YACF;QACF,OAAO;YACL,wCAAwC;YACxC,gBAAgB,OAAO;QACzB;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBAAC,KAAK,0RAAA,CAAA,UAAI;oBAAE,KAAI;oBAAe,WAAU;oBAA6B,OAAO;oBAAI,QAAQ;;;;;;8BAC/F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAoB;wCAAW;;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;oCAAW,KAAK;;sDAC7B,8OAAC;4CACC,WAAW,CAAC,oDAAoD,EAAE,mBAAmB,gBAAgB,kBAAkB,iBAAiB;4CACxI,SAAS,IAAM,mBAAmB,iBAAiB,gBAAgB,MAAM,GAAG,KAAK,eAAe,CAAC;;8DAEjG,8OAAC;;wDAAK;wDAAM,mBAAmB,gBAAgB,gBAAgB;;;;;;;gDAC9D,mBAAmB,iBAAiB,gBAAgB,MAAM,GAAG,mBAC5D,8OAAC,8IAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;wCAK5B,eAAe,mBAAmB,iBAAiB,gBAAgB,MAAM,GAAG,mBAC3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA+G;;;;;;8DAG9H,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,OAAe,kBACnC,8OAAC;4DAEC,WAAW,CAAC,0EAA0E,EAAE,kBAAkB,QAAQ,oEAAoE,oCAAoC;4DAC1N,SAAS,IAAM,kBAAkB;sEAEhC;2DAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnB,8OAAC;4BAAI,WAAU;sCACZ,8BACC,8OAAC;gCAAI,WAAU;0CACZ,OAAO,eAAe,YAAY,eAAe,QAClD,CAAC,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,MAAM,OAAO,CAAC,WAAW,iBAAiB,CAAC,kBACzF,8OAAC,+HAAA,CAAA,UAAU;oCAAC,wBAAU,8OAAC,wLAAA,CAAA,UAAa;kDAAE;;;;;;8CACpC,cAAA,8OAAC,wJAAA,CAAA,UAAuB;wCACtB,MAAM;wCACN,kBAAkB,WAAW,iBAAiB,IAAI,EAAE;wCACpD,kBAAkB,WAAW,iBAAiB,IAAI,EAAE;wCACpD,kBAAkB;;;;;;;;;;2CAGpB,OAAO,eAAe,YAAY,eAAe,QAClD,OAAO,WAAW,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAC3D,MAAM,OAAO,CAAC,WAAW,IAAI,CAAC,iBAAiB,kBAChD,8OAAC,+HAAA,CAAA,UAAU;oCAAC,wBAAU,8OAAC,wLAAA,CAAA,UAAa;kDAAE,OAAO,WAAW,IAAI,CAAC,WAAW,KAAK,WAAW,WAAW,IAAI,CAAC,WAAW,GAAG;;;;;;8CACpH,cAAA,8OAAC,wJAAA,CAAA,UAAuB;wCACtB,MAAM,OAAO,WAAW,IAAI,CAAC,WAAW,KAAK,WAAW,WAAW,IAAI,CAAC,WAAW,GAAG;wCACtF,kBAAkB,WAAW,IAAI,CAAC,iBAAiB,IAAI,EAAE;wCACzD,kBAAkB,WAAW,IAAI,CAAC,iBAAiB,IAAI,EAAE;wCACzD,kBAAkB;;;;;;;;;;yDAItB,8OAAC,wLAAA,CAAA,UAAa;8CAAE;;;;;;;;;;qDAIpB;;kDACE,8OAAC;kDAAE;;;;;;oCACF,oDAAyB,+BACxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DACH,8OAAC;;oDAAE;oDAAkB,OAAO;;;;;;;0DAC5B,8OAAC;;oDAAE;oDAAmB,aAAc,OAAO,eAAe,WAAW,WAAW,SAAS,CAAC,GAAG,OAAO,QAAQ,KAAK,SAAS,CAAC,cAAe;;;;;;;;;;;;;oCAG7I;;;;;;;;wBAIN,CAAC,8BACA,8OAAC;4BAAI,WAAU;;8CAQb,8OAAC;oCACC,WAAW,CAAC,mCAAmC,EAAE,aAAa,SAAS,gCAAgC,IAAI;oCAC3G,OAAM;oCACN,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,8IAAA,CAAA,aAAU;;;;;;;;;;8CAEb,8OAAC;oCACC,WAAW,CAAC,mCAAmC,EAAE,aAAa,YAAY,4BAA4B,IAAI;oCAC1G,OAAM;oCACN,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;8CAEf,8OAAC;oCACC,WAAW,CAAC,mCAAmC,EAAE,SAAS,gCAAgC,IAAI;oCAC9F,SAAS;oCACT,OAAO,SAAS,YAAY;8CAE5B,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;8CAST,8OAAC;oCACC,WAAU;oCACV,OAAM;oCACN,SAAS;8CAET,cAAA,8OAAC,8IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOzB;IAEA,MAAM,kBAAkB;QACtB,MAAM,OAAO,iBAAiB;QAC9B,MAAM,WAAW,iBAAiB,YAAY;QAE9C,IAAI,MAAM;YACR,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC;gCACC,WAAU;gCACV,SAAS;oCACP,UAAU,SAAS,CAAC,SAAS,CAAC;oCAC9B,UAAU;oCACV,WAAW,IAAM,UAAU,QAAQ;gCACrC;0CAEC,SAAS,YAAY;;;;;;;;;;;;kCAG1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAW,CAAC,SAAS,EAAE,UAAU;sCACpC;;;;;;;;;;;;;;;;;QAKX;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IACE,mBACA,gBAAgB,mBAAmB,IACnC,gBAAgB,QAAQ,EAAE,QAC1B;YACA,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAyC,sBACtE,8OAAC;gCAEC,WAAU;0CAET,IAAI,KAAK;+BAHL;;;;;;;;;;;;;;;;QASjB;QACA,OAAO;IACT;IAIA,qFAAqF;IAErF,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,wBAAwB;gBAClC,gBAAgB,OAAO;gBACvB,kBAAkB,OAAO,eAAe,WACpC,WAAW,SAAS,CAAC,GAAG,MAAM,CAAC,WAAW,MAAM,GAAG,KAAK,QAAQ,EAAE,IAClE,KAAK,SAAS,CAAC;gBACnB,yBAAyB,OAAO,oBAAoB,WAChD,gBAAgB,SAAS,CAAC,GAAG,MAAM,CAAC,gBAAgB,MAAM,GAAG,KAAK,QAAQ,EAAE,IAC5E,KAAK,SAAS,CAAC;gBACnB,qBAAqB,OAAO,eAAe,YAAY,eAAe,QACpE,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,WAAW,iBAAiB,CAAC,MAAM,GAAG;YACzF;YAEA,0CAA0C;YAC1C,IAAI,OAAO,eAAe,YAAY,eAAe,QACjD,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,WAAW,iBAAiB,CAAC,MAAM,GAAG,GAAG;gBAC1F,QAAQ,GAAG,CAAC,iCAAiC,WAAW,iBAAiB;YAC3E;YAEA,0CAA0C;YAC1C,IAAI,OAAO,eAAe,YAAY,eAAe,QACjD,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,WAAW,iBAAiB,CAAC,MAAM,GAAG,GAAG;gBAC1F,QAAQ,GAAG,CAAC,iCAAiC,WAAW,iBAAiB;YAC3E;QACF;QAEA,iCAAiC;QACjC,IAAI,cAAc,aAAa,cAAc,YAAY;YACvD,UAAU;QACZ;IACF,GAAG;QAAC;QAAY;QAAiB;QAAW;KAAU;IAEtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAK;gBAAC,KAAK,0RAAA,CAAA,UAAI;gBAAE,KAAI;gBAAe,WAAU;gBAA6B,OAAO;gBAAI,QAAQ;;;;;;0BAC/F,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAoB;oCAAW;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;gCAAW,KAAK;0CAY5B,eAAe,mBAAmB,iBAAiB,gBAAgB,MAAM,GAAG,mBAC3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA+G;;;;;;sDAG9H,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,OAAe,kBACnC,8OAAC;oDAEC,WAAW,CAAC,0EAA0E,EAAE,kBAAkB,QAAQ,oEAAoE,oCAAoC;oDAC1N,SAAS,IAAM,kBAAkB;8DAEhC;mDAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAanB,8OAAC;wBAAI,WAAU;kCACZ,eAAe,eAAe,8BAC7B,8OAAC,iJAAA,CAAA,UAAgB;4BACf,SAAQ;4BACR,WAAW;4BACX,YAAY;;;;;iDAGd,8OAAC;4BAAI,WAAU;;gCACZ,mBAAmB,gBAAgB,IAAI,OAAO,KAC7C,gHAAgH;gCAChH,OAAO,eAAe,YAAY,eAAe,QACjD,CAAC,MAAM,OAAO,CAAC,WAAW,iBAAiB,KAAK,MAAM,OAAO,CAAC,WAAW,iBAAiB,CAAC,kBACzF,8OAAC,+HAAA,CAAA,UAAU;oCAAC,wBAAU,8OAAC,wLAAA,CAAA,UAAa;kDAAE;;;;;;8CACpC,cAAA,8OAAC,wJAAA,CAAA,UAAuB;wCACtB,MAAM;wCACN,kBAAkB,WAAW,iBAAiB,IAAI,EAAE;wCACpD,kBAAkB,CAAC;4CACjB,QAAQ,GAAG,CAAC,4CAA4C,WAAW,iBAAiB;4CACpF,OAAO,WAAW,iBAAiB,IAAI,EAAE;wCAC3C,CAAC;wCACD,kBAAkB,CAAC;4CACjB,QAAQ,GAAG,CAAC,gCAAgC;4CAC5C,IAAI,kBAAkB;gDACpB,iBAAiB;gDACjB,wCAAwC;gDACxC,MAAM,eAAe,SAAS,aAAa,CAAC;gDAC5C,IAAI,cAAc;oDAChB,aAAa,KAAK,GAAG;oDACrB,aAAa,aAAa,CAAC,IAAI,MAAM,SAAS;wDAAE,SAAS;oDAAK;oDAC9D,aAAa,KAAK;gDACpB;4CACF;wCACF;wCACA,kBAAkB;;;;;;;;;;2CAItB,wDAAwD;gCACxD,OAAO,eAAe,YAAY,eAAe,QACjD,OAAO,WAAW,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAC3D,MAAM,OAAO,CAAC,WAAW,IAAI,CAAC,iBAAiB,kBAC7C,8OAAC,+HAAA,CAAA,UAAU;oCAAC,wBAAU,8OAAC,wLAAA,CAAA,UAAa;kDAAE,OAAO,WAAW,IAAI,CAAC,WAAW,KAAK,WAAW,WAAW,IAAI,CAAC,WAAW,GAAG;;;;;;8CACpH,cAAA,8OAAC,wJAAA,CAAA,UAAuB;wCACtB,MAAM,OAAO,WAAW,IAAI,CAAC,WAAW,KAAK,WAAW,WAAW,IAAI,CAAC,WAAW,GAAG;wCACtF,kBAAkB,WAAW,IAAI,CAAC,iBAAiB,IAAI,EAAE;wCACzD,kBAAkB,WAAW,IAAI,CAAC,iBAAiB,IAAI,EAAE;wCACzD,kBAAkB;wCAClB,kBAAkB;;;;;;;;;;2CAItB,mCAAmC;8CACnC,8OAAC,wLAAA,CAAA,UAAa;8CAAE;;;;;2CAGlB,OAAO,mBAAmB,YAAY,eAAe,IAAI,OAAO,mBAClE,8OAAC,wLAAA,CAAA,UAAa;8CAAE;;;;;yDAEhB,8OAAC;8CAAE;;;;;;8CAIL;;wCACG;wCACA;;;;;;;;;;;;;;oBAKR,CAAC,8BACA,8OAAC;wBAAI,WAAU;;0CAQb,8OAAC;gCACC,WAAW,CAAC,mCAAmC,EAAE,aAAa,SAAS,gCAAgC,IAAI;gCAC3G,OAAM;gCACN,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC,8IAAA,CAAA,aAAU;;;;;;;;;;0CAEb,8OAAC;gCACC,WAAW,CAAC,mCAAmC,EAAE,aAAa,YAAY,4BAA4B,IAAI;gCAC1G,OAAM;gCACN,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;0CAEf,8OAAC;gCACC,WAAW,CAAC,mCAAmC,EAAE,SAAS,gCAAgC,IAAI;gCAC9F,SAAS;gCACT,OAAO,SAAS,YAAY;0CAE5B,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;0CAST,8OAAC;gCACC,WAAU;gCACV,OAAM;gCACN,SAAS;0CAET,cAAA,8OAAC,8IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;uCAEe"}}, {"offset": {"line": 9893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/app/%28with-layout%29/chat/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport UserMessage from \"@/components/chatComponents/UserMessage\";\r\nimport ChatBox from \"@/components/chatComponents/ChatBox\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport BotReply from \"@/components/chatComponents/BotReply\";\r\nimport { Chat, useChatHandler } from \"@/stores/chatList\";\r\n\r\nfunction CustomChat() {\r\n  const [scroll, setScroll] = useState(false);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const { chatList, userQuery, updateChatList } = useChatHandler();\r\n  const path = usePathname();\r\n  const [currentChat, setCurrentChat] = useState<Chat>();\r\n  const [selectedLanguage] = useState(\"English\");\r\n\r\n  const chatId = path ? path.split(\"/chat/\")[1] : null;\r\n\r\n  // Load chat list when component mounts\r\n  useEffect(() => {\r\n    updateChatList();\r\n  }, [updateChatList]);\r\n\r\n  useEffect(() => {\r\n    const currentChatList = chatList.find(({ id }: { id: string }) => {\r\n      return id === chatId;\r\n    });\r\n    setCurrentChat(currentChatList);\r\n  }, [chatList, path, chatId]);\r\n\r\n  useEffect(() => {\r\n    if (chatContainerRef.current) {\r\n      chatContainerRef.current.scrollTop =\r\n        chatContainerRef.current.scrollHeight;\r\n    }\r\n  }, [userQuery, scroll]);\r\n\r\n  return (\r\n    <div className=\" flex flex-col gap-4 h-full flex-1 overflow-auto w-full z-20 \">\r\n      <div className=\"overflow-auto w-full flex-1\" ref={chatContainerRef}>\r\n        <div className={`pb-6  flex-grow  w-full max-w-[1070px] mx-auto `}>\r\n          <div className=\"flex gap-3 px-6 relative z-20  w-full flex-col \">\r\n            {currentChat &&\r\n              currentChat.messages.map((item, idx) => {\r\n                return (\r\n                  <div className=\"flex flex-col gap-3\" key={idx}>\r\n                    {item.isUser && typeof item.text === \"string\" && (\r\n                      <UserMessage\r\n                        message={item.text}\r\n                        timestamp={item.timestamp}\r\n                        uploadedFiles={item.uploadedFiles}\r\n                        uploadedURLs={item.uploadedURLs}\r\n                        selectedLanguage={selectedLanguage}\r\n                      />\r\n                    )}\r\n\r\n                    {!item.isUser && (\r\n                      <BotReply\r\n                        replyType=\"response\"\r\n                        setScroll={setScroll}\r\n                        isAnimation={userQuery === item.text}\r\n                        aiResponse={item.text}\r\n                        timestamp={item.timestamp}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ChatBox />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CustomChat;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;IAC7D,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC7C,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEpC,MAAM,SAAS,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,GAAG;IAEhD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,EAAkB;YAC3D,OAAO,OAAO;QAChB;QACA,eAAe;IACjB,GAAG;QAAC;QAAU;QAAM;KAAO;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,OAAO,EAAE;YAC5B,iBAAiB,OAAO,CAAC,SAAS,GAChC,iBAAiB,OAAO,CAAC,YAAY;QACzC;IACF,GAAG;QAAC;QAAW;KAAO;IAEtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;gBAA8B,KAAK;0BAChD,cAAA,8OAAC;oBAAI,WAAW,CAAC,+CAA+C,CAAC;8BAC/D,cAAA,8OAAC;wBAAI,WAAU;kCACZ,eACC,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM;4BAC9B,qBACE,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI,KAAK,0BACnC,8OAAC,4IAAA,CAAA,UAAW;wCACV,SAAS,KAAK,IAAI;wCAClB,WAAW,KAAK,SAAS;wCACzB,eAAe,KAAK,aAAa;wCACjC,cAAc,KAAK,YAAY;wCAC/B,kBAAkB;;;;;;oCAIrB,CAAC,KAAK,MAAM,kBACX,8OAAC,yIAAA,CAAA,UAAQ;wCACP,WAAU;wCACV,WAAW;wCACX,aAAa,cAAc,KAAK,IAAI;wCACpC,YAAY,KAAK,IAAI;wCACrB,WAAW,KAAK,SAAS;;;;;;;+BAjBW;;;;;wBAsB9C;;;;;;;;;;;;;;;;0BAKR,8OAAC,wIAAA,CAAA,UAAO;;;;;;;;;;;AAGd;uCAEe"}}, {"offset": {"line": 10020, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}