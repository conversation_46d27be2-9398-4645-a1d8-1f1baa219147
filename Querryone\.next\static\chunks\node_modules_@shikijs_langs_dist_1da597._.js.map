{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/regexp.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"RegExp\\\",\\\"fileTypes\\\":[\\\"re\\\"],\\\"name\\\":\\\"regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}],\\\"repository\\\":{\\\"codetags\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.codetag.notation.python\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\\\\\\\b)\\\"},\\\"fregexp-base-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#fregexp-quantifier\\\"},{\\\"include\\\":\\\"#fstring-formatting-braces\\\"},{\\\"match\\\":\\\"\\\\\\\\{.*?\\\\\\\\}\\\"},{\\\"include\\\":\\\"#regexp-base-common\\\"}]},\\\"fregexp-quantifier\\\":{\\\"match\\\":\\\"\\\\\\\\{\\\\\\\\{(\\\\\\\\d+|\\\\\\\\d+,(\\\\\\\\d+)?|,\\\\\\\\d+)\\\\\\\\}\\\\\\\\}\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},\\\"fstring-formatting-braces\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.brace.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"match\\\":\\\"({)(\\\\\\\\s*?)(})\\\"},{\\\"match\\\":\\\"({{|}})\\\",\\\"name\\\":\\\"constant.character.escape.python\\\"}]},\\\"regexp-backreference\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.backreference.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\?P=\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?)(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.backreference.named.regexp\\\"},\\\"regexp-backreference-number\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.backreference.regexp\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\[1-9]\\\\\\\\d?)\\\",\\\"name\\\":\\\"meta.backreference.regexp\\\"},\\\"regexp-base-common\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"support.other.match.any.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\^\\\",\\\"name\\\":\\\"support.other.match.begin.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\",\\\"name\\\":\\\"support.other.match.end.regexp\\\"},{\\\"match\\\":\\\"[+*?]\\\\\\\\??\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.disjunction.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-sequence\\\"}]},\\\"regexp-base-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-quantifier\\\"},{\\\"include\\\":\\\"#regexp-base-common\\\"}]},\\\"regexp-character-set\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\^?\\\\\\\\](?!.*?\\\\\\\\])\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?(\\\\\\\\])?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.begin.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.set.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.end.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.character.set.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-charecter-set-escapes\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.set.regexp\\\"}]}]},\\\"regexp-charecter-set-escapes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[abfnrtv\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-special\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-7]{1,3})\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-character\\\"},{\\\"include\\\":\\\"#regexp-escape-unicode\\\"},{\\\"include\\\":\\\"#regexp-escape-catchall\\\"}]},\\\"regexp-comments\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.comment.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comment.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"comment.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"regexp-conditional\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?\\\\\\\\((\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?|\\\\\\\\d+)\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.conditional.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.conditional.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-escape-catchall\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},\\\"regexp-escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(x[0-9A-Fa-f]{2}|0[0-7]{1,2}|[0-7]{3})\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},\\\"regexp-escape-sequence\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-escape-special\\\"},{\\\"include\\\":\\\"#regexp-escape-character\\\"},{\\\"include\\\":\\\"#regexp-escape-unicode\\\"},{\\\"include\\\":\\\"#regexp-backreference-number\\\"},{\\\"include\\\":\\\"#regexp-escape-catchall\\\"}]},\\\"regexp-escape-special\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([AbBdDsSwWZ])\\\",\\\"name\\\":\\\"support.other.escape.special.regexp\\\"},\\\"regexp-escape-unicode\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\\\",\\\"name\\\":\\\"constant.character.unicode.regexp\\\"},\\\"regexp-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-base-expression\\\"},{\\\"include\\\":\\\"#regexp-character-set\\\"},{\\\"include\\\":\\\"#regexp-comments\\\"},{\\\"include\\\":\\\"#regexp-flags\\\"},{\\\"include\\\":\\\"#regexp-named-group\\\"},{\\\"include\\\":\\\"#regexp-backreference\\\"},{\\\"include\\\":\\\"#regexp-lookahead\\\"},{\\\"include\\\":\\\"#regexp-lookahead-negative\\\"},{\\\"include\\\":\\\"#regexp-lookbehind\\\"},{\\\"include\\\":\\\"#regexp-lookbehind-negative\\\"},{\\\"include\\\":\\\"#regexp-conditional\\\"},{\\\"include\\\":\\\"#regexp-parentheses-non-capturing\\\"},{\\\"include\\\":\\\"#regexp-parentheses\\\"}]},\\\"regexp-flags\\\":{\\\"match\\\":\\\"\\\\\\\\(\\\\\\\\?[aiLmsux]+\\\\\\\\)\\\",\\\"name\\\":\\\"storage.modifier.flag.regexp\\\"},\\\"regexp-lookahead\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-lookahead-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-lookbehind\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-lookbehind-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-named-group\\\":{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?P<\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.group.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.named.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-parentheses-non-capturing\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-expression\\\"}]},\\\"regexp-quantifier\\\":{\\\"match\\\":\\\"\\\\\\\\{(\\\\\\\\d+|\\\\\\\\d+,(\\\\\\\\d+)?|,\\\\\\\\d+)\\\\\\\\}\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"}},\\\"scopeName\\\":\\\"source.regexp.python\\\",\\\"aliases\\\":[\\\"regex\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/c.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"C\\\",\\\"name\\\":\\\"c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional\\\"},{\\\"include\\\":\\\"#predefined_macros\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#switch_statement\\\"},{\\\"include\\\":\\\"#anon_pattern_1\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#anon_pattern_2\\\"},{\\\"include\\\":\\\"#anon_pattern_3\\\"},{\\\"include\\\":\\\"#anon_pattern_4\\\"},{\\\"include\\\":\\\"#anon_pattern_5\\\"},{\\\"include\\\":\\\"#anon_pattern_6\\\"},{\\\"include\\\":\\\"#anon_pattern_7\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#anon_pattern_range_1\\\"},{\\\"include\\\":\\\"#anon_pattern_range_2\\\"},{\\\"include\\\":\\\"#anon_pattern_range_3\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"},{\\\"include\\\":\\\"#anon_pattern_range_4\\\"},{\\\"include\\\":\\\"#anon_pattern_range_5\\\"},{\\\"include\\\":\\\"#anon_pattern_range_6\\\"},{\\\"include\\\":\\\"#anon_pattern_8\\\"},{\\\"include\\\":\\\"#anon_pattern_9\\\"},{\\\"include\\\":\\\"#anon_pattern_10\\\"},{\\\"include\\\":\\\"#anon_pattern_11\\\"},{\\\"include\\\":\\\"#anon_pattern_12\\\"},{\\\"include\\\":\\\"#anon_pattern_13\\\"},{\\\"include\\\":\\\"#block\\\"},{\\\"include\\\":\\\"#parens\\\"},{\\\"include\\\":\\\"#anon_pattern_range_7\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#anon_pattern_range_8\\\"},{\\\"include\\\":\\\"#anon_pattern_range_9\\\"},{\\\"include\\\":\\\"#anon_pattern_14\\\"},{\\\"include\\\":\\\"#anon_pattern_15\\\"}],\\\"repository\\\":{\\\"access-method\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*(?:(\\\\\\\\.)|(->))((?:(?:[a-zA-Z_][a-zA-Z_0-9]*)\\\\\\\\s*(?:(?:\\\\\\\\.)|(?:->)))*)\\\\\\\\s*([a-zA-Z_][a-zA-Z_0-9]*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},{\\\"match\\\":\\\"[a-zA-Z_][a-zA-Z_0-9]*\\\",\\\"name\\\":\\\"variable.object.c\\\"},{\\\"match\\\":\\\".+\\\",\\\"name\\\":\\\"everything.else.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.c\\\"}},\\\"name\\\":\\\"meta.function-call.member.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"anon_pattern_1\\\":{\\\"match\\\":\\\"\\\\\\\\b(break|continue|do|else|for|goto|if|_Pragma|return|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.c\\\"},\\\"anon_pattern_10\\\":{\\\"match\\\":\\\"\\\\\\\\b(int8_t|int16_t|int32_t|int64_t|uint8_t|uint16_t|uint32_t|uint64_t|int_least8_t|int_least16_t|int_least32_t|int_least64_t|uint_least8_t|uint_least16_t|uint_least32_t|uint_least64_t|int_fast8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|intptr_t|uintptr_t|intmax_t|intmax_t|uintmax_t|uintmax_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.stdint.c\\\"},\\\"anon_pattern_11\\\":{\\\"match\\\":\\\"\\\\\\\\b(noErr|kNilOptions|kInvalidID|kVariableLengthArray)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.mac-classic.c\\\"},\\\"anon_pattern_12\\\":{\\\"match\\\":\\\"\\\\\\\\b(AbsoluteTime|Boolean|Byte|ByteCount|ByteOffset|BytePtr|CompTimeValue|ConstLogicalAddress|ConstStrFileNameParam|ConstStringPtr|Duration|Fixed|FixedPtr|Float32|Float32Point|Float64|Float80|Float96|FourCharCode|Fract|FractPtr|Handle|ItemCount|LogicalAddress|OptionBits|OSErr|OSStatus|OSType|OSTypePtr|PhysicalAddress|ProcessSerialNumber|ProcessSerialNumberPtr|ProcHandle|Ptr|ResType|ResTypePtr|ShortFixed|ShortFixedPtr|SignedByte|SInt16|SInt32|SInt64|SInt8|Size|StrFileName|StringHandle|StringPtr|TimeBase|TimeRecord|TimeScale|TimeValue|TimeValue64|UInt16|UInt32|UInt64|UInt8|UniChar|UniCharCount|UniCharCountPtr|UniCharPtr|UnicodeScalarValue|UniversalProcHandle|UniversalProcPtr|UnsignedFixed|UnsignedFixedPtr|UnsignedWide|UTF16Char|UTF32Char|UTF8Char)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.mac-classic.c\\\"},\\\"anon_pattern_13\\\":{\\\"match\\\":\\\"\\\\\\\\b([A-Za-z0-9_]+_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.posix-reserved.c\\\"},\\\"anon_pattern_14\\\":{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.terminator.statement.c\\\"},\\\"anon_pattern_15\\\":{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.c\\\"},\\\"anon_pattern_2\\\":{\\\"match\\\":\\\"typedef\\\",\\\"name\\\":\\\"keyword.other.typedef.c\\\"},\\\"anon_pattern_3\\\":{\\\"match\\\":\\\"\\\\\\\\b(const|extern|register|restrict|static|volatile|inline)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.c\\\"},\\\"anon_pattern_4\\\":{\\\"match\\\":\\\"\\\\\\\\bk[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.variable.mac-classic.c\\\"},\\\"anon_pattern_5\\\":{\\\"match\\\":\\\"\\\\\\\\bg[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.global.mac-classic.c\\\"},\\\"anon_pattern_6\\\":{\\\"match\\\":\\\"\\\\\\\\bs[A-Z]\\\\\\\\w*\\\\\\\\b\\\",\\\"name\\\":\\\"variable.other.readwrite.static.mac-classic.c\\\"},\\\"anon_pattern_7\\\":{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.c\\\"},\\\"anon_pattern_8\\\":{\\\"match\\\":\\\"\\\\\\\\b(u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sys-types.c\\\"},\\\"anon_pattern_9\\\":{\\\"match\\\":\\\"\\\\\\\\b(pthread_attr_t|pthread_cond_t|pthread_condattr_t|pthread_mutex_t|pthread_mutexattr_t|pthread_once_t|pthread_rwlock_t|pthread_rwlockattr_t|pthread_t|pthread_key_t)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.pthread.c\\\"},\\\"anon_pattern_range_1\\\":{\\\"begin\\\":\\\"((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))((#)\\\\\\\\s*define\\\\\\\\b)\\\\\\\\s+((?<!\\\\\\\\w)[a-zA-Z_]\\\\\\\\w*(?!\\\\\\\\w))(?:(\\\\\\\\()([^()\\\\\\\\\\\\\\\\]+)(\\\\\\\\)))?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.directive.define.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.preprocessor.c\\\"}},\\\"match\\\":\\\"(?<=[(,])\\\\\\\\s*((?<!\\\\\\\\w)[a-zA-Z_]\\\\\\\\w*(?!\\\\\\\\w))\\\\\\\\s*\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.c\\\"},{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"ellipses.c punctuation.vararg-ellipses.variable.parameter.preprocessor.c\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.c\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.macro.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"anon_pattern_range_2\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(error|warning))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.diagnostic.$3.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.diagnostic.c\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"[^'\\\\\\\"]\\\",\\\"end\\\":\\\"(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"string.unquoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]},\\\"anon_pattern_range_3\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(include(?:_next)?|import))\\\\\\\\b\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.$3.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.include.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.include.c\\\"},{\\\"begin\\\":\\\"<\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\">\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.other.lt-gt.include.c\\\"}]},\\\"anon_pattern_range_4\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*line)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.line.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_5\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*undef))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.undef.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_6\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*(?:((#)\\\\\\\\s*pragma))\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=(?://|/\\\\\\\\*))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.pragma.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#strings\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w\\\\\\\\-$]*\\\",\\\"name\\\":\\\"entity.other.attribute-name.pragma.preprocessor.c\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},\\\"anon_pattern_range_7\\\":{\\\"begin\\\":\\\"(?<!\\\\\\\\w)(?!\\\\\\\\s*(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|pthread_rwlock_t|atomic_uintptr_t|atomic_ptrdiff_t|atomic_uintmax_t|atomic_intmax_t|atomic_char32_t|atomic_intptr_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|uint_least8_t|int_least32_t|int_least16_t|pthread_key_t|uint_fast32_t|uint_fast64_t|uint_fast16_t|atomic_size_t|atomic_ushort|atomic_ullong|int_least64_t|atomic_ulong|int_least8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|memory_order|atomic_schar|atomic_uchar|atomic_short|atomic_llong|thread_local|atomic_bool|atomic_uint|atomic_long|int_fast8_t|suseconds_t|atomic_char|atomic_int|useconds_t|_Imaginary|uintmax_t|uintmax_t|in_addr_t|in_port_t|_Noreturn|blksize_t|pthread_t|uintptr_t|volatile|u_quad_t|blkcnt_t|intmax_t|intptr_t|_Complex|uint16_t|uint32_t|uint64_t|_Alignof|_Alignas|continue|unsigned|restrict|intmax_t|register|int64_t|qaddr_t|segsz_t|_Atomic|alignas|default|caddr_t|nlink_t|typedef|u_short|fixpt_t|clock_t|swblk_t|ssize_t|alignof|daddr_t|int16_t|int32_t|uint8_t|struct|mode_t|size_t|time_t|ushort|u_long|u_char|int8_t|double|signed|static|extern|inline|return|switch|xor_eq|and_eq|bitand|not_eq|sizeof|quad_t|uid_t|bitor|union|off_t|key_t|ino_t|compl|u_int|short|const|false|while|float|pid_t|break|_Bool|or_eq|div_t|dev_t|gid_t|id_t|long|case|goto|else|bool|auto|id_t|enum|uint|true|NULL|void|char|for|not|int|and|xor|do|or|if)\\\\\\\\s*\\\\\\\\()(?=[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?!\\\\\\\\G)(?<=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.function.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},\\\"anon_pattern_range_8\\\":{\\\"begin\\\":\\\"([a-zA-Z_][a-zA-Z_0-9]*|(?<=[\\\\\\\\]\\\\\\\\)]))?(\\\\\\\\[)(?!\\\\\\\\])\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.object.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.begin.bracket.square.c\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.end.bracket.square.c\\\"}},\\\"name\\\":\\\"meta.bracket.square.access.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"anon_pattern_range_9\\\":{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\s*\\\\\\\\]\\\",\\\"name\\\":\\\"storage.modifier.array.bracket.square.c\\\"},\\\"backslash_escapes\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3][0-7]{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.c\\\"},\\\"block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"name\\\":\\\"meta.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"block_comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"},{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"block_innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-enabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-conditional-block\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#c_function_call\\\"},{\\\"begin\\\":\\\"(?:(?:(?=\\\\\\\\s)(?<!else|new|return)(?<=\\\\\\\\w)\\\\\\\\s+(and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)))((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.initialization.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.initialization.c\\\"}},\\\"name\\\":\\\"meta.initialization.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"include\\\":\\\"#parens-block\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"c_conditional_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"c_function_call\\\":{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.function-call.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"case_statement\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)case(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.case.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.colon.case.c\\\"}},\\\"name\\\":\\\"meta.conditional.case.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:^)(?>\\\\\\\\s*)(\\\\\\\\/\\\\\\\\/[!\\\\\\\\/]+)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.documentation.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.documentation.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.documentation.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.documentation.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*[!*]+(?=\\\\\\\\s))(.+)([!*]*\\\\\\\\*\\\\\\\\/)\\\",\\\"name\\\":\\\"comment.block.documentation.c\\\"},{\\\"begin\\\":\\\"((?>\\\\\\\\s*)\\\\\\\\/\\\\\\\\*[!*]+(?:(?:\\\\\\\\n|$)|(?=\\\\\\\\s)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.documentation.c\\\"}},\\\"end\\\":\\\"([!*]*\\\\\\\\*\\\\\\\\/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.documentation.c\\\"}},\\\"name\\\":\\\"comment.block.documentation.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:callergraph|callgraph|else|endif|f\\\\\\\\$|f\\\\\\\\[|f\\\\\\\\]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|\\\\\\\\$|\\\\\\\\#|<|>|%|\\\\\\\"|\\\\\\\\.|=|::|\\\\\\\\||\\\\\\\\-\\\\\\\\-|\\\\\\\\-\\\\\\\\-\\\\\\\\-)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.italic.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|em|e))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.bold.doxygen.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]b)\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"markup.inline.raw.string.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:c|p))\\\\\\\\s+(\\\\\\\\S+)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:a|anchor|b|c|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|e|em|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"in|out\\\",\\\"name\\\":\\\"keyword.other.parameter.direction.$0.c\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.c\\\"}},\\\"match\\\":\\\"((?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@]param)(?:\\\\\\\\s*\\\\\\\\[((?:,?\\\\\\\\s*(?:in|out)\\\\\\\\s*)+)\\\\\\\\])?\\\\\\\\s+(\\\\\\\\b\\\\\\\\w+\\\\\\\\b)\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:arg|attention|author|authors|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remark|remarks|result|return|returns|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?<=[\\\\\\\\s*!\\\\\\\\/])[\\\\\\\\\\\\\\\\@](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\\\\\b(?:\\\\\\\\{[^}]*\\\\\\\\})?\\\",\\\"name\\\":\\\"storage.type.class.doxygen.c\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\b[A-Z]+:|@[a-z_]+:)\\\",\\\"name\\\":\\\"storage.type.class.gtkdoc\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.block.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\/\\\\\\\\* =(\\\\\\\\s*.*?)\\\\\\\\s*= \\\\\\\\*\\\\\\\\/$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.block.banner.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\*\\\\\\\\/)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.c\\\"}},\\\"name\\\":\\\"comment.block.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.toc-list.banner.line.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\/\\\\\\\\/ =(\\\\\\\\s*.*?)\\\\\\\\s*=$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.banner.c\\\"},{\\\"begin\\\":\\\"((?:^[ \\\\\\\\t]+)?)(?=\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.c\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]}]},{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#line_comment\\\"}]},{\\\"include\\\":\\\"#block_comment\\\"},{\\\"include\\\":\\\"#line_comment\\\"}]},\\\"default_statement\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)default(?!\\\\\\\\w))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.control.default.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.colon.case.default.c\\\"}},\\\"name\\\":\\\"meta.conditional.case.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"disabled\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*if(n?def)?\\\\\\\\b.*$\\\",\\\"end\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*endif\\\\\\\\b\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},\\\"evaluation_context\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"function-call-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"function-innards\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parameters.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parameters.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.function.definition.parameters.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#probably_a_parameter\\\"},{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-innards\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},\\\"inline_comment\\\":{\\\"patterns\\\":[{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?:[^\\\\\\\\*]|(?:\\\\\\\\*)++[^\\\\\\\\/])*+((?:\\\\\\\\*)++\\\\\\\\/))\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"3\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(\\\\\\\\/\\\\\\\\*)((?:[^\\\\\\\\*]|(?:\\\\\\\\*)++[^\\\\\\\\/])*+((?:\\\\\\\\*)++\\\\\\\\/))\\\"}]},\\\"line_comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\s*+(\\\\\\\\/\\\\\\\\/)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.c\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\n)(?<!\\\\\\\\\\\\\\\\\\\\\\\\n)\\\",\\\"endCaptures\\\":{},\\\"name\\\":\\\"comment.line.double-slash.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"line_continuation_character\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.escape.line-continuation.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\n\\\"}]},\\\"member_access\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.member.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*(\\\\\\\\b(?!(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|atomic_uintptr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintmax_t|pthread_mutex_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_attr_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_cond_t|pthread_once_t|uint_fast64_t|uint_fast16_t|atomic_size_t|uint_least8_t|int_least64_t|int_least32_t|int_least16_t|pthread_key_t|atomic_ullong|atomic_ushort|uint_fast32_t|atomic_schar|atomic_short|uint_fast8_t|int_fast64_t|int_fast32_t|int_fast16_t|atomic_ulong|atomic_llong|int_least8_t|atomic_uchar|memory_order|suseconds_t|int_fast8_t|atomic_bool|atomic_char|atomic_uint|atomic_long|atomic_int|useconds_t|_Imaginary|blksize_t|pthread_t|in_addr_t|uintptr_t|in_port_t|uintmax_t|uintmax_t|blkcnt_t|uint16_t|unsigned|_Complex|uint32_t|intptr_t|intmax_t|intmax_t|uint64_t|u_quad_t|int64_t|int32_t|ssize_t|caddr_t|clock_t|uint8_t|u_short|swblk_t|segsz_t|int16_t|fixpt_t|daddr_t|nlink_t|qaddr_t|size_t|time_t|mode_t|signed|quad_t|ushort|u_long|u_char|double|int8_t|ino_t|uid_t|pid_t|_Bool|float|dev_t|div_t|short|gid_t|off_t|u_int|key_t|id_t|uint|long|void|char|bool|id_t|int)\\\\\\\\b)[a-zA-Z_]\\\\\\\\w*\\\\\\\\b(?!\\\\\\\\())\\\"},\\\"method_access\\\":{\\\"begin\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))((?:[a-zA-Z_]\\\\\\\\w*\\\\\\\\s*(?:(?:(?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|(?:(?:->\\\\\\\\*|->)))\\\\\\\\s*)*)\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.other.object.access.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.dot-access.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.pointer-access.c\\\"}},\\\"match\\\":\\\"((?:[a-zA-Z_]\\\\\\\\w*|(?<=\\\\\\\\]|\\\\\\\\)))\\\\\\\\s*)(?:((?:\\\\\\\\.\\\\\\\\*|\\\\\\\\.))|((?:->\\\\\\\\*|->)))\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.member.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.function.member.c\\\"}},\\\"contentName\\\":\\\"meta.function-call.member.c\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.function.member.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"}]},\\\"numbers\\\":{\\\"captures\\\":{\\\"0\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?=.)\\\",\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.c\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.c\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.c\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9a-fA-F])\\\\\\\\.|\\\\\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?<!')([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?([lLfF](?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"constant.numeric.decimal.point.c\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"8\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.c\\\"},\\\"9\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.c\\\"},\\\"10\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.c\\\"},\\\"11\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"12\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.floating-point.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?:(?<=[0-9])\\\\\\\\.|\\\\\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)?((?<!')([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?([lLfF](?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.binary.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.binary.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[bB])([01](?:[01]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.octal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.octal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0)((?:[0-7]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))+)((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.unit.hexadecimal.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.hexadecimal.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.hexadecimal.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.hexadecimal.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.hexadecimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G0[xX])([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?<!')([pP])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"constant.numeric.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.unit.exponent.decimal.c\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.plus.exponent.decimal.c\\\"},\\\"7\\\":{\\\"name\\\":\\\"keyword.operator.minus.exponent.decimal.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.exponent.decimal.c\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])\\\",\\\"name\\\":\\\"punctuation.separator.constant.numeric\\\"}]},\\\"9\\\":{\\\"name\\\":\\\"keyword.other.unit.suffix.integer.c\\\"}},\\\"match\\\":\\\"(\\\\\\\\G(?=[0-9.])(?!0[xXbB]))([0-9](?:[0-9]|((?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)((?<!')([eE])(\\\\\\\\+?)(\\\\\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])'(?=[0-9a-fA-F])))*)))?((?:(?:(?:(?:(?:[uU]|[uU]ll?)|[uU]LL?)|ll?[uU]?)|LL?[uU]?)|[fF])(?!\\\\\\\\w))?$\\\"},{\\\"match\\\":\\\"(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])+\\\",\\\"name\\\":\\\"invalid.illegal.constant.numeric\\\"}]}]}},\\\"match\\\":\\\"(?<!\\\\\\\\w)\\\\\\\\.?\\\\\\\\d(?:(?:[0-9a-zA-Z_\\\\\\\\.]|')|(?<=[eEpP])[+-])*\\\"},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\w$])(sizeof)(?![\\\\\\\\w$])\\\",\\\"name\\\":\\\"keyword.operator.sizeof.c\\\"},{\\\"match\\\":\\\"--\\\",\\\"name\\\":\\\"keyword.operator.decrement.c\\\"},{\\\"match\\\":\\\"\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.increment.c\\\"},{\\\"match\\\":\\\"%=|\\\\\\\\+=|-=|\\\\\\\\*=|(?<!\\\\\\\\()/=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.c\\\"},{\\\"match\\\":\\\"&=|\\\\\\\\^=|<<=|>>=|\\\\\\\\|=\\\",\\\"name\\\":\\\"keyword.operator.assignment.compound.bitwise.c\\\"},{\\\"match\\\":\\\"<<|>>\\\",\\\"name\\\":\\\"keyword.operator.bitwise.shift.c\\\"},{\\\"match\\\":\\\"!=|<=|>=|==|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.c\\\"},{\\\"match\\\":\\\"&&|!|\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.logical.c\\\"},{\\\"match\\\":\\\"&|\\\\\\\\||\\\\\\\\^|~\\\",\\\"name\\\":\\\"keyword.operator.c\\\"},{\\\"match\\\":\\\"=\\\",\\\"name\\\":\\\"keyword.operator.assignment.c\\\"},{\\\"match\\\":\\\"%|\\\\\\\\*|/|-|\\\\\\\\+\\\",\\\"name\\\":\\\"keyword.operator.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call-innards\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"parens\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.parens.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"parens-block\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"name\\\":\\\"meta.parens.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"},{\\\"match\\\":\\\"(?-mix:(?<!:):(?!:))\\\",\\\"name\\\":\\\"punctuation.range-based.c\\\"}]},\\\"pragma-mark\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.preprocessor.pragma.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.directive.pragma.pragma-mark.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"entity.name.tag.pragma-mark.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(((#)\\\\\\\\s*pragma\\\\\\\\s+mark)\\\\\\\\s+(.*))\\\",\\\"name\\\":\\\"meta.section.c\\\"},\\\"predefined_macros\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.other.preprocessor.macro.predefined.$1.c\\\"}},\\\"match\\\":\\\"\\\\\\\\b(__cplusplus|__DATE__|__FILE__|__LINE__|__STDC__|__STDC_HOSTED__|__STDC_NO_COMPLEX__|__STDC_VERSION__|__STDCPP_THREADS__|__TIME__|NDEBUG|__OBJC__|__ASSEMBLER__|__ATOM__|__AVX__|__AVX2__|_CHAR_UNSIGNED|__CLR_VER|_CONTROL_FLOW_GUARD|__COUNTER__|__cplusplus_cli|__cplusplus_winrt|_CPPRTTI|_CPPUNWIND|_DEBUG|_DLL|__FUNCDNAME__|__FUNCSIG__|__FUNCTION__|_INTEGRAL_MAX_BITS|__INTELLISENSE__|_ISO_VOLATILE|_KERNEL_MODE|_M_AMD64|_M_ARM|_M_ARM_ARMV7VE|_M_ARM_FP|_M_ARM64|_M_CEE|_M_CEE_PURE|_M_CEE_SAFE|_M_FP_EXCEPT|_M_FP_FAST|_M_FP_PRECISE|_M_FP_STRICT|_M_IX86|_M_IX86_FP|_M_X64|_MANAGED|_MSC_BUILD|_MSC_EXTENSIONS|_MSC_FULL_VER|_MSC_VER|_MSVC_LANG|__MSVC_RUNTIME_CHECKS|_MT|_NATIVE_WCHAR_T_DEFINED|_OPENMP|_PREFAST|__TIMESTAMP__|_VC_NO_DEFAULTLIB|_WCHAR_T_DEFINED|_WIN32|_WIN64|_WINRT_DLL|_ATL_VER|_MFC_VER|__GFORTRAN__|__GNUC__|__GNUC_MINOR__|__GNUC_PATCHLEVEL__|__GNUG__|__STRICT_ANSI__|__BASE_FILE__|__INCLUDE_LEVEL__|__ELF__|__VERSION__|__OPTIMIZE__|__OPTIMIZE_SIZE__|__NO_INLINE__|__GNUC_STDC_INLINE__|__CHAR_UNSIGNED__|__WCHAR_UNSIGNED__|__REGISTER_PREFIX__|__REGISTER_PREFIX__|__SIZE_TYPE__|__PTRDIFF_TYPE__|__WCHAR_TYPE__|__WINT_TYPE__|__INTMAX_TYPE__|__UINTMAX_TYPE__|__SIG_ATOMIC_TYPE__|__INT8_TYPE__|__INT16_TYPE__|__INT32_TYPE__|__INT64_TYPE__|__UINT8_TYPE__|__UINT16_TYPE__|__UINT32_TYPE__|__UINT64_TYPE__|__INT_LEAST8_TYPE__|__INT_LEAST16_TYPE__|__INT_LEAST32_TYPE__|__INT_LEAST64_TYPE__|__UINT_LEAST8_TYPE__|__UINT_LEAST16_TYPE__|__UINT_LEAST32_TYPE__|__UINT_LEAST64_TYPE__|__INT_FAST8_TYPE__|__INT_FAST16_TYPE__|__INT_FAST32_TYPE__|__INT_FAST64_TYPE__|__UINT_FAST8_TYPE__|__UINT_FAST16_TYPE__|__UINT_FAST32_TYPE__|__UINT_FAST64_TYPE__|__INTPTR_TYPE__|__UINTPTR_TYPE__|__CHAR_BIT__|__SCHAR_MAX__|__WCHAR_MAX__|__SHRT_MAX__|__INT_MAX__|__LONG_MAX__|__LONG_LONG_MAX__|__WINT_MAX__|__SIZE_MAX__|__PTRDIFF_MAX__|__INTMAX_MAX__|__UINTMAX_MAX__|__SIG_ATOMIC_MAX__|__INT8_MAX__|__INT16_MAX__|__INT32_MAX__|__INT64_MAX__|__UINT8_MAX__|__UINT16_MAX__|__UINT32_MAX__|__UINT64_MAX__|__INT_LEAST8_MAX__|__INT_LEAST16_MAX__|__INT_LEAST32_MAX__|__INT_LEAST64_MAX__|__UINT_LEAST8_MAX__|__UINT_LEAST16_MAX__|__UINT_LEAST32_MAX__|__UINT_LEAST64_MAX__|__INT_FAST8_MAX__|__INT_FAST16_MAX__|__INT_FAST32_MAX__|__INT_FAST64_MAX__|__UINT_FAST8_MAX__|__UINT_FAST16_MAX__|__UINT_FAST32_MAX__|__UINT_FAST64_MAX__|__INTPTR_MAX__|__UINTPTR_MAX__|__WCHAR_MIN__|__WINT_MIN__|__SIG_ATOMIC_MIN__|__SCHAR_WIDTH__|__SHRT_WIDTH__|__INT_WIDTH__|__LONG_WIDTH__|__LONG_LONG_WIDTH__|__PTRDIFF_WIDTH__|__SIG_ATOMIC_WIDTH__|__SIZE_WIDTH__|__WCHAR_WIDTH__|__WINT_WIDTH__|__INT_LEAST8_WIDTH__|__INT_LEAST16_WIDTH__|__INT_LEAST32_WIDTH__|__INT_LEAST64_WIDTH__|__INT_FAST8_WIDTH__|__INT_FAST16_WIDTH__|__INT_FAST32_WIDTH__|__INT_FAST64_WIDTH__|__INTPTR_WIDTH__|__INTMAX_WIDTH__|__SIZEOF_INT__|__SIZEOF_LONG__|__SIZEOF_LONG_LONG__|__SIZEOF_SHORT__|__SIZEOF_POINTER__|__SIZEOF_FLOAT__|__SIZEOF_DOUBLE__|__SIZEOF_LONG_DOUBLE__|__SIZEOF_SIZE_T__|__SIZEOF_WCHAR_T__|__SIZEOF_WINT_T__|__SIZEOF_PTRDIFF_T__|__BYTE_ORDER__|__ORDER_LITTLE_ENDIAN__|__ORDER_BIG_ENDIAN__|__ORDER_PDP_ENDIAN__|__FLOAT_WORD_ORDER__|__DEPRECATED|__EXCEPTIONS|__GXX_RTTI|__USING_SJLJ_EXCEPTIONS__|__GXX_EXPERIMENTAL_CXX0X__|__GXX_WEAK__|__NEXT_RUNTIME__|__LP64__|_LP64|__SSP__|__SSP_ALL__|__SSP_STRONG__|__SSP_EXPLICIT__|__SANITIZE_ADDRESS__|__SANITIZE_THREAD__|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16|__HAVE_SPECULATION_SAFE_VALUE|__GCC_HAVE_DWARF2_CFI_ASM|__FP_FAST_FMA|__FP_FAST_FMAF|__FP_FAST_FMAL|__FP_FAST_FMAF16|__FP_FAST_FMAF32|__FP_FAST_FMAF64|__FP_FAST_FMAF128|__FP_FAST_FMAF32X|__FP_FAST_FMAF64X|__FP_FAST_FMAF128X|__GCC_IEC_559|__GCC_IEC_559_COMPLEX|__NO_MATH_ERRNO__|__has_builtin|__has_feature|__has_extension|__has_cpp_attribute|__has_c_attribute|__has_attribute|__has_declspec_attribute|__is_identifier|__has_include|__has_include_next|__has_warning|__BASE_FILE__|__FILE_NAME__|__clang__|__clang_major__|__clang_minor__|__clang_patchlevel__|__clang_version__|__fp16|_Float16)\\\\\\\\b\\\"},{\\\"match\\\":\\\"\\\\\\\\b__([A-Z_]+)__\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.other.preprocessor.macro.predefined.probably.$1.c\\\"}]},\\\"preprocessor-rule-conditional\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if(?:n?def)?\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"invalid.illegal.stray-$1.c\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(else|elif|endif)\\\\\\\\b\\\"}]},\\\"preprocessor-rule-conditional-line\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?:\\\\\\\\bdefined\\\\\\\\b\\\\\\\\s*$)|(?:\\\\\\\\bdefined\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\s*(?:(?!defined\\\\\\\\b)[a-zA-Z_$][\\\\\\\\w$]*\\\\\\\\b)\\\\\\\\s*\\\\\\\\)*\\\\\\\\s*(?:\\\\\\\\n|//|/\\\\\\\\*|\\\\\\\\?|\\\\\\\\:|&&|\\\\\\\\|\\\\\\\\||\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n)))\\\",\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},{\\\"match\\\":\\\"\\\\\\\\bdefined\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.macro-name.c\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#numbers\\\"},{\\\"begin\\\":\\\"\\\\\\\\?\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"end\\\":\\\":\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.ternary.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#operators\\\"},{\\\"match\\\":\\\"\\\\\\\\b(NULL|true|false|TRUE|FALSE)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.c\\\"},{\\\"match\\\":\\\"[a-zA-Z_$][\\\\\\\\w$]*\\\",\\\"name\\\":\\\"entity.name.function.preprocessor.c\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"\\\\\\\\)|(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]}]},\\\"preprocessor-rule-define-line-blocks\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-define-line-contents\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"begin\\\":\\\"{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.c\\\"}},\\\"end\\\":\\\"}|(?=\\\\\\\\s*#\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.c\\\"}},\\\"name\\\":\\\"meta.block.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-blocks\\\"}]},{\\\"match\\\":\\\"\\\\\\\\(\\\",\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"},{\\\"match\\\":\\\"\\\\\\\\)\\\",\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas|asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|long|short|signed|struct|typedef|union|unsigned|void)\\\\\\\\s*\\\\\\\\()(?=(?:[A-Za-z_][A-Za-z0-9_]*+|::)++\\\\\\\\s*\\\\\\\\(|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\]))\\\\\\\\s*\\\\\\\\()\\\",\\\"end\\\":\\\"(?<=\\\\\\\\))(?!\\\\\\\\w)|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.function.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"$self\\\"}]},\\\"preprocessor-rule-define-line-functions\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#storage_types\\\"},{\\\"include\\\":\\\"#vararg_ellipses\\\"},{\\\"include\\\":\\\"#method_access\\\"},{\\\"include\\\":\\\"#member_access\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"begin\\\":\\\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\\\\\s*\\\\\\\\()((?:[A-Za-z_][A-Za-z0-9_]*+|::)++|(?:(?<=operator)(?:[-*&<>=+!]+|\\\\\\\\(\\\\\\\\)|\\\\\\\\[\\\\\\\\])))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\s*\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-define-line-functions\\\"}]},{\\\"include\\\":\\\"#preprocessor-rule-define-line-contents\\\"}]},\\\"preprocessor-rule-disabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-elif-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-enabled-else-block\\\"},{\\\"include\\\":\\\"#preprocessor-rule-disabled-elif\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]}]},\\\"preprocessor-rule-disabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0+\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:elif|else|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]}]},\\\"preprocessor-rule-enabled\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.preprocessor.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]}]},\\\"preprocessor-rule-enabled-block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*if\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.else-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.if-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]}]}]},\\\"preprocessor-rule-enabled-elif\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"preprocessor-rule-enabled-elif-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*elif\\\\\\\\b)(?=\\\\\\\\s*\\\\\\\\(*\\\\\\\\b0*1\\\\\\\\b\\\\\\\\)*\\\\\\\\s*(?:$|//|/\\\\\\\\*))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G(?=.)(?!//|/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))\\\",\\\"end\\\":\\\"(?=//)|(?=/\\\\\\\\*(?!.*\\\\\\\\\\\\\\\\\\\\\\\\s*\\\\\\\\n))|(?<!\\\\\\\\\\\\\\\\)(?=\\\\\\\\n)\\\",\\\"name\\\":\\\"meta.preprocessor.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#preprocessor-rule-conditional-line\\\"}]},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"\\\\\\\\n\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(else)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.in-block.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*(elif)\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"contentName\\\":\\\"comment.block.preprocessor.elif-branch.c\\\",\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*(?:else|elif|endif)\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#disabled\\\"},{\\\"include\\\":\\\"#pragma-mark\\\"}]},{\\\"include\\\":\\\"#block_innards\\\"}]}]},\\\"preprocessor-rule-enabled-else\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]},\\\"preprocessor-rule-enabled-else-block\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((#)\\\\\\\\s*else\\\\\\\\b)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.preprocessor.c\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.control.directive.conditional.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.directive.c\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*((#)\\\\\\\\s*endif\\\\\\\\b))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#block_innards\\\"}]},\\\"probably_a_parameter\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.probably.c\\\"}},\\\"match\\\":\\\"(?<=(?:[a-zA-Z_0-9] |[&*>\\\\\\\\]\\\\\\\\)]))\\\\\\\\s*([a-zA-Z_]\\\\\\\\w*)\\\\\\\\s*(?=(?:\\\\\\\\[\\\\\\\\]\\\\\\\\s*)?(?:,|\\\\\\\\)))\\\"},\\\"static_assert\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)static_assert|_Static_assert(?!\\\\\\\\w))((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"keyword.other.static_assert.c\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"10\\\":{\\\"name\\\":\\\"punctuation.section.arguments.begin.bracket.round.static_assert.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.arguments.end.bracket.round.static_assert.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(,)\\\\\\\\s*(?=(?:L|u8|u|U\\\\\\\\s*\\\\\\\\\\\\\\\")?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.delimiter.comma.c\\\"}},\\\"end\\\":\\\"(?=\\\\\\\\))\\\",\\\"name\\\":\\\"meta.static_assert.message.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_context\\\"}]},{\\\"include\\\":\\\"#evaluation_context\\\"}]},\\\"storage_types\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:unsigned|signed|double|_Bool|short|float|long|void|char|bool|int)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.primitive.c\\\"},{\\\"match\\\":\\\"(?-mix:(?<!\\\\\\\\w)(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|pthread_rwlockattr_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_uint_fast16_t|atomic_int_least64_t|atomic_int_least32_t|atomic_int_least16_t|atomic_uint_least8_t|atomic_uint_fast8_t|atomic_int_least8_t|atomic_int_fast16_t|pthread_mutexattr_t|atomic_int_fast32_t|atomic_int_fast64_t|atomic_int_fast8_t|pthread_condattr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintptr_t|atomic_uintmax_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|int_least32_t|pthread_key_t|int_least16_t|int_least64_t|uint_least8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|atomic_ushort|atomic_ullong|atomic_size_t|int_fast16_t|int_fast64_t|uint_fast8_t|atomic_short|atomic_uchar|atomic_schar|int_least8_t|memory_order|atomic_llong|atomic_ulong|int_fast32_t|atomic_long|atomic_uint|atomic_char|int_fast8_t|suseconds_t|atomic_bool|atomic_int|_Imaginary|useconds_t|in_port_t|uintmax_t|uintmax_t|pthread_t|blksize_t|in_addr_t|uintptr_t|blkcnt_t|uint16_t|uint32_t|uint64_t|u_quad_t|_Complex|intptr_t|intmax_t|intmax_t|segsz_t|u_short|nlink_t|uint8_t|int64_t|int32_t|int16_t|fixpt_t|daddr_t|caddr_t|qaddr_t|ssize_t|clock_t|swblk_t|u_long|mode_t|int8_t|time_t|ushort|u_char|quad_t|size_t|pid_t|gid_t|uid_t|dev_t|div_t|off_t|u_int|key_t|ino_t|uint|id_t|id_t)(?!\\\\\\\\w))\\\",\\\"name\\\":\\\"storage.type.built-in.c\\\"},{\\\"match\\\":\\\"(?-mix:\\\\\\\\b(enum|struct|union)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.type.$1.c\\\"},{\\\"begin\\\":\\\"(\\\\\\\\b(?:__asm__|asm)\\\\\\\\b)\\\\\\\\s*((?:volatile)?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.asm.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.modifier.c\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"name\\\":\\\"meta.asm.c\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"(?:^)((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))(?:\\\\\\\\n|$)\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"begin\\\":\\\"(((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.assembly.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.assembly.c\\\"}},\\\"patterns\\\":[{\\\"begin\\\":\\\"(R?)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.encoding.c\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.assembly.c\\\"}},\\\"contentName\\\":\\\"meta.embedded.assembly.c\\\",\\\"end\\\":\\\"(\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.assembly.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.asm\\\"},{\\\"include\\\":\\\"source.x86\\\"},{\\\"include\\\":\\\"source.x86_64\\\"},{\\\"include\\\":\\\"source.arm\\\"},{\\\"include\\\":\\\"#backslash_escapes\\\"},{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.assembly.inner.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.assembly.inner.c\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"variable.other.asm.label.c\\\"},\\\"6\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"7\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"8\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"9\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]}},\\\"match\\\":\\\"\\\\\\\\[((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))([a-zA-Z_]\\\\\\\\w*)((?:(?:(?>\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+?|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z)))\\\\\\\\]\\\"},{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.delimiter.colon.assembly.c\\\"},{\\\"include\\\":\\\"#comments\\\"}]}]}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[abefnprtv'\\\\\\\"?]|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8})\\\",\\\"name\\\":\\\"constant.character.escape.c\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"invalid.illegal.unknown-escape.c\\\"}]},\\\"string_placeholder\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"%(\\\\\\\\d+\\\\\\\\$)?[#0\\\\\\\\- +']*[,;:_]?((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?(\\\\\\\\.((-?\\\\\\\\d+)|\\\\\\\\*(-?\\\\\\\\d+\\\\\\\\$)?)?)?(hh|h|ll|l|j|t|z|q|L|vh|vl|v|hv|hl)?[diouxXDOUeEfFgGaACcSspn%]\\\",\\\"name\\\":\\\"constant.other.placeholder.c\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.placeholder.c\\\"}},\\\"match\\\":\\\"(%)(?!\\\\\\\"\\\\\\\\s*(PRI|SCN))\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.double.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_placeholder\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.c\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.c\\\"}},\\\"name\\\":\\\"string.quoted.single.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#line_continuation_character\\\"}]}]},\\\"switch_conditional_parentheses\\\":{\\\"begin\\\":\\\"((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"2\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"4\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"5\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.bracket.round.conditional.switch.c\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.bracket.round.conditional.switch.c\\\"}},\\\"name\\\":\\\"meta.conditional.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#evaluation_context\\\"},{\\\"include\\\":\\\"#c_conditional_context\\\"}]},\\\"switch_statement\\\":{\\\"begin\\\":\\\"(((?>(?:(?:(?>(?<!\\\\\\\\s)\\\\\\\\s+)|(\\\\\\\\/\\\\\\\\*)((?>(?:[^\\\\\\\\*]|(?>\\\\\\\\*+)[^\\\\\\\\/])*)((?>\\\\\\\\*+)\\\\\\\\/)))+|(?:(?:(?:(?:\\\\\\\\b|(?<=\\\\\\\\W))|(?=\\\\\\\\W))|\\\\\\\\A)|\\\\\\\\Z))))((?<!\\\\\\\\w)switch(?!\\\\\\\\w)))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.head.switch.c\\\"},\\\"2\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#inline_comment\\\"}]},\\\"3\\\":{\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.begin.c\\\"},\\\"4\\\":{\\\"name\\\":\\\"comment.block.c\\\"},\\\"5\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\*\\\\\\\\/\\\",\\\"name\\\":\\\"comment.block.c punctuation.definition.comment.end.c\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"comment.block.c\\\"}]},\\\"6\\\":{\\\"name\\\":\\\"keyword.control.switch.c\\\"}},\\\"end\\\":\\\"(?:(?<=\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)|(?=[;>\\\\\\\\[\\\\\\\\]=]))\\\",\\\"name\\\":\\\"meta.block.switch.c\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\G ?\\\",\\\"end\\\":\\\"((?:\\\\\\\\{|<%|\\\\\\\\?\\\\\\\\?<|(?=;)))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.begin.bracket.curly.switch.c\\\"}},\\\"name\\\":\\\"meta.head.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#switch_conditional_parentheses\\\"},{\\\"include\\\":\\\"$self\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\{|<%|\\\\\\\\?\\\\\\\\?<)\\\",\\\"end\\\":\\\"(\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.block.end.bracket.curly.switch.c\\\"}},\\\"name\\\":\\\"meta.body.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#default_statement\\\"},{\\\"include\\\":\\\"#case_statement\\\"},{\\\"include\\\":\\\"$self\\\"},{\\\"include\\\":\\\"#block_innards\\\"}]},{\\\"begin\\\":\\\"(?<=\\\\\\\\}|%>|\\\\\\\\?\\\\\\\\?>)[\\\\\\\\s\\\\\\\\n]*\\\",\\\"end\\\":\\\"[\\\\\\\\s\\\\\\\\n]*(?=;)\\\",\\\"name\\\":\\\"meta.tail.switch.c\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"$self\\\"}]}]},\\\"vararg_ellipses\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\.\\\\\\\\.\\\\\\\\.(?!\\\\\\\\.)\\\",\\\"name\\\":\\\"punctuation.vararg-ellipses.c\\\"}},\\\"scopeName\\\":\\\"source.c\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/glsl.mjs"], "sourcesContent": ["import c from './c.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"GLSL\\\",\\\"fileTypes\\\":[\\\"vs\\\",\\\"fs\\\",\\\"gs\\\",\\\"vsh\\\",\\\"fsh\\\",\\\"gsh\\\",\\\"vshader\\\",\\\"fshader\\\",\\\"gshader\\\",\\\"vert\\\",\\\"frag\\\",\\\"geom\\\",\\\"f.glsl\\\",\\\"v.glsl\\\",\\\"g.glsl\\\"],\\\"foldingStartMarker\\\":\\\"/\\\\\\\\*\\\\\\\\*|\\\\\\\\{\\\\\\\\s*$\\\",\\\"foldingStopMarker\\\":\\\"\\\\\\\\*\\\\\\\\*/|^\\\\\\\\s*\\\\\\\\}\\\",\\\"name\\\":\\\"glsl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(void|bool|int|uint|float|vec2|vec3|vec4|bvec2|bvec3|bvec4|ivec2|ivec2|ivec3|uvec2|uvec2|uvec3|mat2|mat3|mat4|mat2x2|mat2x3|mat2x4|mat3x2|mat3x3|mat3x4|mat4x2|mat4x3|mat4x4|sampler[1|2|3]D|samplerCube|sampler2DRect|sampler[1|2]DShadow|sampler2DRectShadow|sampler[1|2]DArray|sampler[1|2]DArrayShadow|samplerBuffer|sampler2DMS|sampler2DMSArray|struct|isampler[1|2|3]D|isamplerCube|isampler2DRect|isampler[1|2]DArray|isamplerBuffer|isampler2DMS|isampler2DMSArray|usampler[1|2|3]D|usamplerCube|usampler2DRect|usampler[1|2]DArray|usamplerBuffer|usampler2DMS|usampler2DMSArray)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(attribute|centroid|const|flat|in|inout|invariant|noperspective|out|smooth|uniform|varying)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(gl_BackColor|gl_BackLightModelProduct|gl_BackLightProduct|gl_BackMaterial|gl_BackSecondaryColor|gl_ClipDistance|gl_ClipPlane|gl_ClipVertex|gl_Color|gl_DepthRange|gl_DepthRangeParameters|gl_EyePlaneQ|gl_EyePlaneR|gl_EyePlaneS|gl_EyePlaneT|gl_Fog|gl_FogCoord|gl_FogFragCoord|gl_FogParameters|gl_FragColor|gl_FragCoord|gl_FragDat|gl_FragDept|gl_FrontColor|gl_FrontFacing|gl_FrontLightModelProduct|gl_FrontLightProduct|gl_FrontMaterial|gl_FrontSecondaryColor|gl_InstanceID|gl_Layer|gl_LightModel|gl_LightModelParameters|gl_LightModelProducts|gl_LightProducts|gl_LightSource|gl_LightSourceParameters|gl_MaterialParameters|gl_ModelViewMatrix|gl_ModelViewMatrixInverse|gl_ModelViewMatrixInverseTranspose|gl_ModelViewMatrixTranspose|gl_ModelViewProjectionMatrix|gl_ModelViewProjectionMatrixInverse|gl_ModelViewProjectionMatrixInverseTranspose|gl_ModelViewProjectionMatrixTranspose|gl_MultiTexCoord[0-7]|gl_Normal|gl_NormalMatrix|gl_NormalScale|gl_ObjectPlaneQ|gl_ObjectPlaneR|gl_ObjectPlaneS|gl_ObjectPlaneT|gl_Point|gl_PointCoord|gl_PointParameters|gl_PointSize|gl_Position|gl_PrimitiveIDIn|gl_ProjectionMatrix|gl_ProjectionMatrixInverse|gl_ProjectionMatrixInverseTranspose|gl_ProjectionMatrixTranspose|gl_SecondaryColor|gl_TexCoord|gl_TextureEnvColor|gl_TextureMatrix|gl_TextureMatrixInverse|gl_TextureMatrixInverseTranspose|gl_TextureMatrixTranspose|gl_Vertex|gl_VertexIDh)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(gl_MaxClipPlanes|gl_MaxCombinedTextureImageUnits|gl_MaxDrawBuffers|gl_MaxFragmentUniformComponents|gl_MaxLights|gl_MaxTextureCoords|gl_MaxTextureImageUnits|gl_MaxTextureUnits|gl_MaxVaryingFloats|gl_MaxVertexAttribs|gl_MaxVertexTextureImageUnits|gl_MaxVertexUniformComponents)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(abs|acos|all|any|asin|atan|ceil|clamp|cos|cross|degrees|dFdx|dFdy|distance|dot|equal|exp|exp2|faceforward|floor|fract|ftransform|fwidth|greaterThan|greaterThanEqual|inversesqrt|length|lessThan|lessThanEqual|log|log2|matrixCompMult|max|min|mix|mod|noise[1-4]|normalize|not|notEqual|outerProduct|pow|radians|reflect|refract|shadow1D|shadow1DLod|shadow1DProj|shadow1DProjLod|shadow2D|shadow2DLod|shadow2DProj|shadow2DProjLod|sign|sin|smoothstep|sqrt|step|tan|texture1D|texture1DLod|texture1DProj|texture1DProjLod|texture2D|texture2DLod|texture2DProj|texture2DProjLod|texture3D|texture3DLod|texture3DProj|texture3DProjLod|textureCube|textureCubeLod|transpose)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.glsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(asm|double|enum|extern|goto|inline|long|short|sizeof|static|typedef|union|unsigned|volatile)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.glsl\\\"},{\\\"include\\\":\\\"source.c\\\"}],\\\"scopeName\\\":\\\"source.glsl\\\",\\\"embeddedLangs\\\":[\\\"c\\\"]}\"))\n\nexport default [\n...c,\nlang\n]\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;OACZ,mJAAA,CAAA,UAAC;IACJ;CACC", "ignoreList": [0]}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/sql.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"SQL\\\",\\\"name\\\":\\\"sql\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"((?<!@)@)\\\\\\\\b(\\\\\\\\w+)\\\\\\\\b\\\",\\\"name\\\":\\\"text.variable\\\"},{\\\"match\\\":\\\"(\\\\\\\\[)[^\\\\\\\\]]*(\\\\\\\\])\\\",\\\"name\\\":\\\"text.bracketed\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(create(?:\\\\\\\\s+or\\\\\\\\s+replace)?)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)(['\\\\\\\"`]?)(\\\\\\\\w+)\\\\\\\\4\\\",\\\"name\\\":\\\"meta.create.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(drop)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.other.cascade.sql\\\"}},\\\"match\\\":\\\"(?i:\\\\\\\\s*(drop)\\\\\\\\s+(table)\\\\\\\\s+(\\\\\\\\w+)(\\\\\\\\s+cascade)?\\\\\\\\b)\\\",\\\"name\\\":\\\"meta.drop.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.create.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.table.sql\\\"}},\\\"match\\\":\\\"(?i:^\\\\\\\\s*(alter)\\\\\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\\\\\s+)\\\",\\\"name\\\":\\\"meta.alter.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"5\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"6\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"7\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"8\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"9\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"10\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"11\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"12\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"13\\\":{\\\"name\\\":\\\"storage.type.sql\\\"},\\\"14\\\":{\\\"name\\\":\\\"constant.numeric.sql\\\"},\\\"15\\\":{\\\"name\\\":\\\"storage.type.sql\\\"}},\\\"match\\\":\\\"(?xi)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# normal stuff, capture 1\\\\n\\\\t\\\\t\\\\t\\\\t \\\\\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\\\\\b\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# numeric suffix, capture 2 + 3i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(bit\\\\\\\\svarying|character\\\\\\\\s(?:varying)?|tinyint|var\\\\\\\\schar|float|interval)\\\\\\\\((\\\\\\\\d+)\\\\\\\\)\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# optional numeric suffix, capture 4 + 5i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(char|number|varchar\\\\\\\\d?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, capture 6 + 7i + 8i\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(numeric|decimal)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+),(\\\\\\\\d+)\\\\\\\\))?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 9, 10i, 11\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(times?)\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\swith(?:out)?\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t# special case, captures 12, 13, 14i, 15\\\\n\\\\t\\\\t\\\\t\\\\t|\\\\\\\\b(timestamp)(?:(s|tz))?\\\\\\\\b(?:\\\\\\\\((\\\\\\\\d+)\\\\\\\\))?(\\\\\\\\s(with|without)\\\\\\\\stime\\\\\\\\szone\\\\\\\\b)?\\\\n\\\\n\\\\t\\\\t\\\\t\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b((?:primary|foreign)\\\\\\\\s+key|references|on\\\\\\\\sdelete(\\\\\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\\\\\b)\\\",\\\"name\\\":\\\"storage.modifier.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\b\\\\\\\\d+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(select(\\\\\\\\s+(all|distinct))?|insert\\\\\\\\s+(ignore\\\\\\\\s+)?into|update|delete|from|set|where|group\\\\\\\\s+by|or|like|and|union(\\\\\\\\s+all)?|having|order\\\\\\\\s+by|limit|cross\\\\\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\\\\\s+outer)?)\\\\\\\\s+join|natural(\\\\\\\\s+(inner|(left|right|full)(\\\\\\\\s+outer)?))?\\\\\\\\s+join)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(on|off|((is\\\\\\\\s+)?not\\\\\\\\s+)?null)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DDL.create.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bvalues\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.DML.II.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(begin(\\\\\\\\s+work)?|start\\\\\\\\s+transaction|commit(\\\\\\\\s+work)?|rollback(\\\\\\\\s+work)?)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.LUW.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\b(grant(\\\\\\\\swith\\\\\\\\sgrant\\\\\\\\soption)?|revoke)\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.authorization.sql\\\"},{\\\"match\\\":\\\"(?i:\\\\\\\\bin\\\\\\\\b)\\\",\\\"name\\\":\\\"keyword.other.data-integrity.sql\\\"},{\\\"match\\\":\\\"(?i:^\\\\\\\\s*(comment\\\\\\\\s+on\\\\\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\\\\\s+.*?\\\\\\\\s+(is)\\\\\\\\s+)\\\",\\\"name\\\":\\\"keyword.other.object-comments.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\bAS\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.alias.sql\\\"},{\\\"match\\\":\\\"(?i)\\\\\\\\b(DESC|ASC)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.order.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\*\\\",\\\"name\\\":\\\"keyword.operator.star.sql\\\"},{\\\"match\\\":\\\"[!<>]?=|<>|<|>\\\",\\\"name\\\":\\\"keyword.operator.comparison.sql\\\"},{\\\"match\\\":\\\"-|\\\\\\\\+|/\\\",\\\"name\\\":\\\"keyword.operator.math.sql\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.concatenator.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.aggregate.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdev|stdevp|var|varp)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.analytic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.bitmanipulation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(bit_count|get_bit|left_shift|right_shift|set_bit)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.conversion.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.collation.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(collationproperty|tertiary_weights)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cryptographic.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.cursor.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(cursor_status)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datetime.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.datatype.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.expression.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(coalesce|nullif)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.globalvar.sql\\\"}},\\\"match\\\":\\\"(?<!@)@@(?i)\\\\\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.json.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.logical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(choose|iif|greatest|least)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.mathematical.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.metadata.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.ranking.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(rank|dense_rank|ntile|row_number)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.rowset.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.security.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.string.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.system.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.textimage.sql\\\"}},\\\"match\\\":\\\"(?i)\\\\\\\\b(patindex|textptr|textvalid)\\\\\\\\b\\\\\\\\s*\\\\\\\\(\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.other.database-name.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"constant.other.table-name.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\w+?)\\\\\\\\.(\\\\\\\\w+)\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#regexps\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\\\\\\\\\s+or\\\\\\\\\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime|datetime2|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|session|sessions|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablock|tablockx|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|to|top|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|value|values|varbinary|varchar|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|window|windows|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.sql\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.scope.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.scope.end.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.block.sql\\\"}],\\\"repository\\\":{\\\"comment-block\\\":{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.block\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=--)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"--\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.double-dash.sql\\\"}]},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.sql\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[]},{\\\"include\\\":\\\"#comment-block\\\"}]},\\\"regexps\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"/(?=\\\\\\\\S.*/)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"/\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\/\\\",\\\"name\\\":\\\"constant.character.escape.slash.sql\\\"}]},{\\\"begin\\\":\\\"%r\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.regexp.modr.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]},\\\"string_escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.sql\\\"},\\\"string_interpolation\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(#\\\\\\\\{)([^\\\\\\\\}]*)(\\\\\\\\})\\\",\\\"name\\\":\\\"string.interpolated.sql\\\"},\\\"strings\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(N)?(')[^']*(')\\\",\\\"name\\\":\\\"string.quoted.single.sql\\\"},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.single.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(`)[^`\\\\\\\\\\\\\\\\]*(`)\\\",\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\"},{\\\"begin\\\":\\\"`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"`\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.other.backtick.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"match\\\":\\\"(\\\\\\\")[^\\\\\\\"#]*(\\\\\\\")\\\",\\\"name\\\":\\\"string.quoted.double.sql\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.quoted.double.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]},{\\\"begin\\\":\\\"%\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.sql\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.sql\\\"}},\\\"name\\\":\\\"string.other.quoted.brackets.sql\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_interpolation\\\"}]}]}},\\\"scopeName\\\":\\\"source.sql\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/python.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Python\\\",\\\"name\\\":\\\"python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#statement\\\"},{\\\"include\\\":\\\"#expression\\\"}],\\\"repository\\\":{\\\"annotated-parameter\\\":{\\\"begin\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(:)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.python\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"},{\\\"match\\\":\\\"=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.python\\\"}]},\\\"assignment-operator\\\":{\\\"match\\\":\\\"<<=|>>=|//=|\\\\\\\\*\\\\\\\\*=|\\\\\\\\+=|-=|/=|@=|\\\\\\\\*=|%=|~=|\\\\\\\\^=|&=|\\\\\\\\|=|=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.python\\\"},\\\"backticks\\\":{\\\"begin\\\":\\\"\\\\\\\\`\\\",\\\"end\\\":\\\"(?:\\\\\\\\`|(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\n))\\\",\\\"name\\\":\\\"invalid.deprecated.backtick.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"builtin-callables\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"include\\\":\\\"#builtin-exceptions\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#builtin-types\\\"}]},\\\"builtin-exceptions\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b((Arithmetic|Assertion|Attribute|Buffer|BlockingIO|BrokenPipe|ChildProcess|(Connection(Aborted|Refused|Reset)?)|EOF|Environment|FileExists|FileNotFound|FloatingPoint|IO|Import|Indentation|Index|Interrupted|IsADirectory|NotADirectory|Permission|ProcessLookup|Timeout|Key|Lookup|Memory|Name|NotImplemented|OS|Overflow|Reference|Runtime|Recursion|Syntax|System|Tab|Type|UnboundLocal|Unicode(Encode|Decode|Translate)?|Value|Windows|ZeroDivision|ModuleNotFound)Error|((Pending)?Deprecation|Runtime|Syntax|User|Future|Import|Unicode|Bytes|Resource)?Warning|SystemExit|Stop(Async)?Iteration|KeyboardInterrupt|GeneratorExit|(Base)?Exception)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.exception.python\\\"},\\\"builtin-functions\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(__import__|abs|aiter|all|any|anext|ascii|bin|breakpoint|callable|chr|compile|copyright|credits|delattr|dir|divmod|enumerate|eval|exec|exit|filter|format|getattr|globals|hasattr|hash|help|hex|id|input|isinstance|issubclass|iter|len|license|locals|map|max|memoryview|min|next|oct|open|ord|pow|print|quit|range|reload|repr|reversed|round|setattr|sorted|sum|vars|zip)\\\\\\\\b\\\",\\\"name\\\":\\\"support.function.builtin.python\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(file|reduce|intern|raw_input|unicode|cmp|basestring|execfile|long|xrange)\\\\\\\\b\\\",\\\"name\\\":\\\"variable.legacy.builtin.python\\\"}]},\\\"builtin-possible-callables\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin-callables\\\"},{\\\"include\\\":\\\"#magic-names\\\"}]},\\\"builtin-types\\\":{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(bool|bytearray|bytes|classmethod|complex|dict|float|frozenset|int|list|object|property|set|slice|staticmethod|str|tuple|type|super)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.python\\\"},\\\"call-wrapper-inheritance\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?=([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(\\\\\\\\())\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"}},\\\"name\\\":\\\"meta.function-call.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#inheritance-name\\\"},{\\\"include\\\":\\\"#function-arguments\\\"}]},\\\"class-declaration\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\s*(class)\\\\\\\\s+(?=[[:alpha:]_]\\\\\\\\w*\\\\\\\\s*(:|\\\\\\\\())\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.class.python\\\"}},\\\"end\\\":\\\"(:)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.class.begin.python\\\"}},\\\"name\\\":\\\"meta.class.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#class-name\\\"},{\\\"include\\\":\\\"#class-inheritance\\\"}]}]},\\\"class-inheritance\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.inheritance.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.inheritance.end.python\\\"}},\\\"name\\\":\\\"meta.class.inheritance.python\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(\\\\\\\\*\\\\\\\\*|\\\\\\\\*)\\\",\\\"name\\\":\\\"keyword.operator.unpacking.arguments.python\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.inheritance.python\\\"},{\\\"match\\\":\\\"=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.python\\\"},{\\\"match\\\":\\\"\\\\\\\\bmetaclass\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.metaclass.python\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"include\\\":\\\"#class-kwarg\\\"},{\\\"include\\\":\\\"#call-wrapper-inheritance\\\"},{\\\"include\\\":\\\"#expression-base\\\"},{\\\"include\\\":\\\"#member-access-class\\\"},{\\\"include\\\":\\\"#inheritance-identifier\\\"}]},\\\"class-kwarg\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.inherited-class.python variable.parameter.class.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(=)(?!=)\\\"},\\\"class-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"include\\\":\\\"#builtin-possible-callables\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.type.class.python\\\"}]},\\\"codetags\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.codetag.notation.python\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\\\\\\\b)\\\"},\\\"comments\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:\\\\\\\\#\\\\\\\\s*(type:)\\\\\\\\s*+(?!$|\\\\\\\\#))\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.typehint.comment.python\\\"},\\\"1\\\":{\\\"name\\\":\\\"comment.typehint.directive.notation.python\\\"}},\\\"contentName\\\":\\\"meta.typehint.comment.python\\\",\\\"end\\\":\\\"(?:$|(?=\\\\\\\\#))\\\",\\\"name\\\":\\\"comment.line.number-sign.python\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\Gignore(?=\\\\\\\\s*(?:$|\\\\\\\\#))\\\",\\\"name\\\":\\\"comment.typehint.ignore.notation.python\\\"},{\\\"match\\\":\\\"(?<!\\\\\\\\.)\\\\\\\\b(bool|bytes|float|int|object|str|List|Dict|Iterable|Sequence|Set|FrozenSet|Callable|Union|Tuple|Any|None)\\\\\\\\b\\\",\\\"name\\\":\\\"comment.typehint.type.notation.python\\\"},{\\\"match\\\":\\\"([\\\\\\\\[\\\\\\\\]\\\\\\\\(\\\\\\\\),\\\\\\\\.\\\\\\\\=\\\\\\\\*]|(->))\\\",\\\"name\\\":\\\"comment.typehint.punctuation.notation.python\\\"},{\\\"match\\\":\\\"([[:alpha:]_]\\\\\\\\w*)\\\",\\\"name\\\":\\\"comment.typehint.variable.notation.python\\\"}]},{\\\"include\\\":\\\"#comments-base\\\"}]},\\\"comments-base\\\":{\\\"begin\\\":\\\"(\\\\\\\\#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.python\\\"}},\\\"end\\\":\\\"($)\\\",\\\"name\\\":\\\"comment.line.number-sign.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"comments-string-double-three\\\":{\\\"begin\\\":\\\"(\\\\\\\\#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.python\\\"}},\\\"end\\\":\\\"($|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"name\\\":\\\"comment.line.number-sign.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"comments-string-single-three\\\":{\\\"begin\\\":\\\"(\\\\\\\\#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.python\\\"}},\\\"end\\\":\\\"($|(?='''))\\\",\\\"name\\\":\\\"comment.line.number-sign.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"curly-braces\\\":{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dict.begin.python\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.dict.end.python\\\"}},\\\"patterns\\\":[{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.dict.python\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"decorator\\\":{\\\"begin\\\":\\\"^\\\\\\\\s*((@))\\\\\\\\s*(?=[[:alpha:]_]\\\\\\\\w*)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.decorator.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.decorator.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\))(?:(.*?)(?=\\\\\\\\s*(?:\\\\\\\\#|$)))|(?=\\\\\\\\n|\\\\\\\\#)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.decorator.python\\\"}},\\\"name\\\":\\\"meta.function.decorator.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#decorator-name\\\"},{\\\"include\\\":\\\"#function-arguments\\\"}]},\\\"decorator-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin-callables\\\"},{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.period.python\\\"}},\\\"match\\\":\\\"([[:alpha:]_]\\\\\\\\w*)|(\\\\\\\\.)\\\",\\\"name\\\":\\\"entity.name.function.decorator.python\\\"},{\\\"include\\\":\\\"#line-continuation\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.decorator.python\\\"}},\\\"match\\\":\\\"\\\\\\\\s*([^([:alpha:]\\\\\\\\s_\\\\\\\\.#\\\\\\\\\\\\\\\\].*?)(?=\\\\\\\\#|$)\\\",\\\"name\\\":\\\"invalid.illegal.decorator.python\\\"}]},\\\"docstring\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(\\\\\\\\'\\\\\\\\'\\\\\\\\'|\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\1)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"}},\\\"name\\\":\\\"string.quoted.docstring.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docstring-prompt\\\"},{\\\"include\\\":\\\"#codetags\\\"},{\\\"include\\\":\\\"#docstring-guts-unicode\\\"}]},{\\\"begin\\\":\\\"([rR])(\\\\\\\\'\\\\\\\\'\\\\\\\\'|\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"}},\\\"name\\\":\\\"string.quoted.docstring.raw.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#docstring-prompt\\\"},{\\\"include\\\":\\\"#codetags\\\"}]},{\\\"begin\\\":\\\"(\\\\\\\\'|\\\\\\\\\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\1)|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.docstring.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"},{\\\"include\\\":\\\"#docstring-guts-unicode\\\"}]},{\\\"begin\\\":\\\"([rR])(\\\\\\\\'|\\\\\\\\\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.docstring.raw.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#codetags\\\"}]}]},\\\"docstring-guts-unicode\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#escape-sequence\\\"},{\\\"include\\\":\\\"#string-line-continuation\\\"}]},\\\"docstring-prompt\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"}},\\\"match\\\":\\\"(?:(?:^|\\\\\\\\G)\\\\\\\\s*((?:>>>|\\\\\\\\.\\\\\\\\.\\\\\\\\.)\\\\\\\\s)(?=\\\\\\\\s*\\\\\\\\S))\\\"},\\\"docstring-statement\\\":{\\\"begin\\\":\\\"^(?=\\\\\\\\s*[rR]?(\\\\\\\\'\\\\\\\\'\\\\\\\\'|\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"|\\\\\\\\'|\\\\\\\\\\\\\\\"))\\\",\\\"end\\\":\\\"((?<=\\\\\\\\1)|^)(?!\\\\\\\\s*[rR]?(\\\\\\\\'\\\\\\\\'\\\\\\\\'|\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"|\\\\\\\\'|\\\\\\\\\\\\\\\"))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#docstring\\\"}]},\\\"double-one-regexp-character-set\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\^?\\\\\\\\](?!.*?\\\\\\\\])\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?(\\\\\\\\])?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.begin.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.set.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\]|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.end.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.character.set.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-charecter-set-escapes\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.set.regexp\\\"}]}]},\\\"double-one-regexp-comments\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.comment.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comment.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"comment.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"double-one-regexp-conditional\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?\\\\\\\\((\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?|\\\\\\\\d+)\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.conditional.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.conditional.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-base-expression\\\"},{\\\"include\\\":\\\"#double-one-regexp-character-set\\\"},{\\\"include\\\":\\\"#double-one-regexp-comments\\\"},{\\\"include\\\":\\\"#regexp-flags\\\"},{\\\"include\\\":\\\"#double-one-regexp-named-group\\\"},{\\\"include\\\":\\\"#regexp-backreference\\\"},{\\\"include\\\":\\\"#double-one-regexp-lookahead\\\"},{\\\"include\\\":\\\"#double-one-regexp-lookahead-negative\\\"},{\\\"include\\\":\\\"#double-one-regexp-lookbehind\\\"},{\\\"include\\\":\\\"#double-one-regexp-lookbehind-negative\\\"},{\\\"include\\\":\\\"#double-one-regexp-conditional\\\"},{\\\"include\\\":\\\"#double-one-regexp-parentheses-non-capturing\\\"},{\\\"include\\\":\\\"#double-one-regexp-parentheses\\\"}]},\\\"double-one-regexp-lookahead\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-lookahead-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-lookbehind\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-lookbehind-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-named-group\\\":{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?P<\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.group.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.named.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-one-regexp-parentheses-non-capturing\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"double-three-regexp-character-set\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\^?\\\\\\\\](?!.*?\\\\\\\\])\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?(\\\\\\\\])?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.begin.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.set.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\]|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.end.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.character.set.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-charecter-set-escapes\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.set.regexp\\\"}]}]},\\\"double-three-regexp-comments\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.comment.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comment.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"comment.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"double-three-regexp-conditional\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?\\\\\\\\((\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?|\\\\\\\\d+)\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.conditional.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.conditional.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-base-expression\\\"},{\\\"include\\\":\\\"#double-three-regexp-character-set\\\"},{\\\"include\\\":\\\"#double-three-regexp-comments\\\"},{\\\"include\\\":\\\"#regexp-flags\\\"},{\\\"include\\\":\\\"#double-three-regexp-named-group\\\"},{\\\"include\\\":\\\"#regexp-backreference\\\"},{\\\"include\\\":\\\"#double-three-regexp-lookahead\\\"},{\\\"include\\\":\\\"#double-three-regexp-lookahead-negative\\\"},{\\\"include\\\":\\\"#double-three-regexp-lookbehind\\\"},{\\\"include\\\":\\\"#double-three-regexp-lookbehind-negative\\\"},{\\\"include\\\":\\\"#double-three-regexp-conditional\\\"},{\\\"include\\\":\\\"#double-three-regexp-parentheses-non-capturing\\\"},{\\\"include\\\":\\\"#double-three-regexp-parentheses\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-lookahead\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-lookahead-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-lookbehind\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-lookbehind-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-named-group\\\":{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?P<\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.group.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.named.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"double-three-regexp-parentheses-non-capturing\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-double-three\\\"}]},\\\"ellipsis\\\":{\\\"match\\\":\\\"\\\\\\\\.\\\\\\\\.\\\\\\\\.\\\",\\\"name\\\":\\\"constant.other.ellipsis.python\\\"},\\\"escape-sequence\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(x[0-9A-Fa-f]{2}|[0-7]{1,3}|[\\\\\\\\\\\\\\\\\\\\\\\"'abfnrtv])\\\",\\\"name\\\":\\\"constant.character.escape.python\\\"},\\\"escape-sequence-unicode\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8}|N\\\\\\\\{[\\\\\\\\w\\\\\\\\s]+?\\\\\\\\})\\\",\\\"name\\\":\\\"constant.character.escape.python\\\"}]},\\\"expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-base\\\"},{\\\"include\\\":\\\"#member-access\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\"}]},\\\"expression-bare\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#backticks\\\"},{\\\"include\\\":\\\"#illegal-anno\\\"},{\\\"include\\\":\\\"#literal\\\"},{\\\"include\\\":\\\"#regexp\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#lambda\\\"},{\\\"include\\\":\\\"#generator\\\"},{\\\"include\\\":\\\"#illegal-operator\\\"},{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#curly-braces\\\"},{\\\"include\\\":\\\"#item-access\\\"},{\\\"include\\\":\\\"#list\\\"},{\\\"include\\\":\\\"#odd-function-call\\\"},{\\\"include\\\":\\\"#round-braces\\\"},{\\\"include\\\":\\\"#function-call\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#builtin-types\\\"},{\\\"include\\\":\\\"#builtin-exceptions\\\"},{\\\"include\\\":\\\"#magic-names\\\"},{\\\"include\\\":\\\"#special-names\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"include\\\":\\\"#special-variables\\\"},{\\\"include\\\":\\\"#ellipsis\\\"},{\\\"include\\\":\\\"#punctuation\\\"},{\\\"include\\\":\\\"#line-continuation\\\"}]},\\\"expression-base\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#expression-bare\\\"},{\\\"include\\\":\\\"#line-continuation\\\"}]},\\\"f-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#expression-bare\\\"},{\\\"include\\\":\\\"#member-access\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\"}]},\\\"fregexp-base-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#fregexp-quantifier\\\"},{\\\"include\\\":\\\"#fstring-formatting-braces\\\"},{\\\"match\\\":\\\"\\\\\\\\{.*?\\\\\\\\}\\\"},{\\\"include\\\":\\\"#regexp-base-common\\\"}]},\\\"fregexp-quantifier\\\":{\\\"match\\\":\\\"\\\\\\\\{\\\\\\\\{(\\\\\\\\d+|\\\\\\\\d+,(\\\\\\\\d+)?|,\\\\\\\\d+)\\\\\\\\}\\\\\\\\}\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},\\\"fstring-fnorm-quoted-multi-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[fF])([bBuU])?('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.multi.python storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.interpolated.python string.quoted.multi.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.multi.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-core\\\"}]},\\\"fstring-fnorm-quoted-single-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[fF])([bBuU])?((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.single.python storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.interpolated.python string.quoted.single.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.single.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-core\\\"}]},\\\"fstring-formatting\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-formatting-braces\\\"},{\\\"include\\\":\\\"#fstring-formatting-singe-brace\\\"}]},\\\"fstring-formatting-braces\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.brace.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"match\\\":\\\"({)(\\\\\\\\s*?)(})\\\"},{\\\"match\\\":\\\"({{|}})\\\",\\\"name\\\":\\\"constant.character.escape.python\\\"}]},\\\"fstring-formatting-singe-brace\\\":{\\\"match\\\":\\\"(}(?!}))\\\",\\\"name\\\":\\\"invalid.illegal.brace.python\\\"},\\\"fstring-guts\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#escape-sequence\\\"},{\\\"include\\\":\\\"#string-line-continuation\\\"},{\\\"include\\\":\\\"#fstring-formatting\\\"}]},\\\"fstring-illegal-multi-brace\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#impossible\\\"}]},\\\"fstring-illegal-single-brace\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)(?=[^\\\\\\\\n}]*$\\\\\\\\n?)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\})|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-terminator-single\\\"},{\\\"include\\\":\\\"#f-expression\\\"}]},\\\"fstring-multi-brace\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\})\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-terminator-multi\\\"},{\\\"include\\\":\\\"#f-expression\\\"}]},\\\"fstring-multi-core\\\":{\\\"match\\\":\\\"(.+?)(($\\\\\\\\n?)|(?=[\\\\\\\\\\\\\\\\\\\\\\\\}\\\\\\\\{]|'''|\\\\\\\"\\\\\\\"\\\\\\\"))|\\\\\\\\n\\\",\\\"name\\\":\\\"string.interpolated.python string.quoted.multi.python\\\"},\\\"fstring-normf-quoted-multi-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[bBuU])([fF])('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.multi.python storage.type.string.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.quoted.multi.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.multi.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-core\\\"}]},\\\"fstring-normf-quoted-single-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[bBuU])([fF])((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.single.python storage.type.string.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.quoted.single.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.single.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-core\\\"}]},\\\"fstring-raw-guts\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#fstring-formatting\\\"}]},\\\"fstring-raw-multi-core\\\":{\\\"match\\\":\\\"(.+?)(($\\\\\\\\n?)|(?=[\\\\\\\\\\\\\\\\\\\\\\\\}\\\\\\\\{]|'''|\\\\\\\"\\\\\\\"\\\\\\\"))|\\\\\\\\n\\\",\\\"name\\\":\\\"string.interpolated.python string.quoted.raw.multi.python\\\"},\\\"fstring-raw-quoted-multi-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:[rR][fF]|[fF][rR]))('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.raw.multi.python storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.quoted.raw.multi.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.raw.multi.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-raw-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-raw-multi-core\\\"}]},\\\"fstring-raw-quoted-single-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:[rR][fF]|[fF][rR]))((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"string.interpolated.python string.quoted.raw.single.python storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python string.quoted.raw.single.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python string.interpolated.python string.quoted.raw.single.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.fstring.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-raw-guts\\\"},{\\\"include\\\":\\\"#fstring-illegal-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-brace\\\"},{\\\"include\\\":\\\"#fstring-raw-single-core\\\"}]},\\\"fstring-raw-single-core\\\":{\\\"match\\\":\\\"(.+?)(($\\\\\\\\n?)|(?=[\\\\\\\\\\\\\\\\\\\\\\\\}\\\\\\\\{]|(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)))|\\\\\\\\n\\\",\\\"name\\\":\\\"string.interpolated.python string.quoted.raw.single.python\\\"},\\\"fstring-single-brace\\\":{\\\"begin\\\":\\\"(\\\\\\\\{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\})|(?=\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-terminator-single\\\"},{\\\"include\\\":\\\"#f-expression\\\"}]},\\\"fstring-single-core\\\":{\\\"match\\\":\\\"(.+?)(($\\\\\\\\n?)|(?=[\\\\\\\\\\\\\\\\\\\\\\\\}\\\\\\\\{]|(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)))|\\\\\\\\n\\\",\\\"name\\\":\\\"string.interpolated.python string.quoted.single.python\\\"},\\\"fstring-terminator-multi\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(=(![rsa])?)(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(=?![rsa])(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"match\\\":\\\"((?:=?)(?:![rsa])?)(:\\\\\\\\w?[<>=^]?[-+ ]?\\\\\\\\#?\\\\\\\\d*,?(\\\\\\\\.\\\\\\\\d+)?[bcdeEfFgGnosxX%]?)(?=})\\\"},{\\\"include\\\":\\\"#fstring-terminator-multi-tail\\\"}]},\\\"fstring-terminator-multi-tail\\\":{\\\"begin\\\":\\\"((?:=?)(?:![rsa])?)(:)(?=.*?{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"end\\\":\\\"(?=})\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-illegal-multi-brace\\\"},{\\\"include\\\":\\\"#fstring-multi-brace\\\"},{\\\"match\\\":\\\"([bcdeEfFgGnosxX%])(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\.\\\\\\\\d+)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\d+)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\#)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"([-+ ])\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"([<>=^])\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\w)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"}]},\\\"fstring-terminator-single\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(=(![rsa])?)(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(=?![rsa])(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"match\\\":\\\"((?:=?)(?:![rsa])?)(:\\\\\\\\w?[<>=^]?[-+ ]?\\\\\\\\#?\\\\\\\\d*,?(\\\\\\\\.\\\\\\\\d+)?[bcdeEfFgGnosxX%]?)(?=})\\\"},{\\\"include\\\":\\\"#fstring-terminator-single-tail\\\"}]},\\\"fstring-terminator-single-tail\\\":{\\\"begin\\\":\\\"((?:=?)(?:![rsa])?)(:)(?=.*?{)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"end\\\":\\\"(?=})|(?=\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#fstring-illegal-single-brace\\\"},{\\\"include\\\":\\\"#fstring-single-brace\\\"},{\\\"match\\\":\\\"([bcdeEfFgGnosxX%])(?=})\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\.\\\\\\\\d+)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\d+)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\#)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"([-+ ])\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"([<>=^])\\\",\\\"name\\\":\\\"storage.type.format.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\w)\\\",\\\"name\\\":\\\"storage.type.format.python\\\"}]},\\\"function-arguments\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.python\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.python\\\",\\\"end\\\":\\\"(?=\\\\\\\\))(?!\\\\\\\\)\\\\\\\\s*\\\\\\\\()\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"(,)\\\",\\\"name\\\":\\\"punctuation.separator.arguments.python\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.unpacking.arguments.python\\\"}},\\\"match\\\":\\\"(?:(?<=[,(])|^)\\\\\\\\s*(\\\\\\\\*{1,2})\\\"},{\\\"include\\\":\\\"#lambda-incomplete\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function-call.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(=)(?!=)\\\"},{\\\"match\\\":\\\"=(?!=)\\\",\\\"name\\\":\\\"keyword.operator.assignment.python\\\"},{\\\"include\\\":\\\"#expression\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.python\\\"}},\\\"match\\\":\\\"\\\\\\\\s*(\\\\\\\\))\\\\\\\\s*(\\\\\\\\()\\\"}]},\\\"function-call\\\":{\\\"begin\\\":\\\"\\\\\\\\b(?=([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(\\\\\\\\())\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"}},\\\"name\\\":\\\"meta.function-call.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#special-variables\\\"},{\\\"include\\\":\\\"#function-name\\\"},{\\\"include\\\":\\\"#function-arguments\\\"}]},\\\"function-declaration\\\":{\\\"begin\\\":\\\"\\\\\\\\s*(?:\\\\\\\\b(async)\\\\\\\\s+)?\\\\\\\\b(def)\\\\\\\\s+(?=[[:alpha:]_][[:word:]]*\\\\\\\\s*\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.async.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.function.python\\\"}},\\\"end\\\":\\\"(:|(?=[#'\\\\\\\"\\\\\\\\n]))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.begin.python\\\"}},\\\"name\\\":\\\"meta.function.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-def-name\\\"},{\\\"include\\\":\\\"#parameters\\\"},{\\\"include\\\":\\\"#line-continuation\\\"},{\\\"include\\\":\\\"#return-annotation\\\"}]},\\\"function-def-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"include\\\":\\\"#builtin-possible-callables\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"entity.name.function.python\\\"}]},\\\"function-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#builtin-possible-callables\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.function-call.generic.python\\\"}]},\\\"generator\\\":{\\\"begin\\\":\\\"\\\\\\\\bfor\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"}},\\\"end\\\":\\\"\\\\\\\\bin\\\\\\\\b\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"illegal-anno\\\":{\\\"match\\\":\\\"->\\\",\\\"name\\\":\\\"invalid.illegal.annotation.python\\\"},\\\"illegal-names\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.import.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?:(and|assert|async|await|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|in|is|(?<=\\\\\\\\.)lambda|lambda(?=\\\\\\\\s*[\\\\\\\\.=])|nonlocal|not|or|pass|raise|return|try|while|with|yield)|(as|import))\\\\\\\\b\\\"},\\\"illegal-object-name\\\":{\\\"match\\\":\\\"\\\\\\\\b(True|False|None)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.illegal.name.python\\\"},\\\"illegal-operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"&&|\\\\\\\\|\\\\\\\\||--|\\\\\\\\+\\\\\\\\+\\\",\\\"name\\\":\\\"invalid.illegal.operator.python\\\"},{\\\"match\\\":\\\"[?$]\\\",\\\"name\\\":\\\"invalid.illegal.operator.python\\\"},{\\\"match\\\":\\\"!\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.operator.python\\\"}]},\\\"import\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(from)\\\\\\\\b(?=.+import)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.import.python\\\"}},\\\"end\\\":\\\"$|(?=import)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.+\\\",\\\"name\\\":\\\"punctuation.separator.period.python\\\"},{\\\"include\\\":\\\"#expression\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(import)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.import.python\\\"}},\\\"end\\\":\\\"$\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)as\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.python\\\"},{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"impossible\\\":{\\\"match\\\":\\\"$.^\\\"},\\\"inheritance-identifier\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.inherited-class.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\"},\\\"inheritance-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#lambda-incomplete\\\"},{\\\"include\\\":\\\"#builtin-possible-callables\\\"},{\\\"include\\\":\\\"#inheritance-identifier\\\"}]},\\\"item-access\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(?=[[:alpha:]_]\\\\\\\\w*\\\\\\\\s*\\\\\\\\[)\\\",\\\"end\\\":\\\"(\\\\\\\\])\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"}},\\\"name\\\":\\\"meta.item-access.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#item-name\\\"},{\\\"include\\\":\\\"#item-index\\\"},{\\\"include\\\":\\\"#expression\\\"}]}]},\\\"item-index\\\":{\\\"begin\\\":\\\"(\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.begin.python\\\"}},\\\"contentName\\\":\\\"meta.item-access.arguments.python\\\",\\\"end\\\":\\\"(?=\\\\\\\\])\\\",\\\"patterns\\\":[{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.slice.python\\\"},{\\\"include\\\":\\\"#expression\\\"}]},\\\"item-name\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#special-variables\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#special-names\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.indexed-name.python\\\"}]},\\\"lambda\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"}},\\\"match\\\":\\\"((?<=\\\\\\\\.)lambda|lambda(?=\\\\\\\\s*[\\\\\\\\.=]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.lambda.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(lambda)\\\\\\\\s*?(?=[,\\\\\\\\n]|$)\\\"},{\\\"begin\\\":\\\"\\\\\\\\b(lambda)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.function.lambda.python\\\"}},\\\"contentName\\\":\\\"meta.function.lambda.parameters.python\\\",\\\"end\\\":\\\"(:)|(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.function.lambda.begin.python\\\"}},\\\"name\\\":\\\"meta.lambda-function.python\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"/\\\",\\\"name\\\":\\\"keyword.operator.positional.parameter.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\*\\\\\\\\*|\\\\\\\\*)\\\",\\\"name\\\":\\\"keyword.operator.unpacking.parameter.python\\\"},{\\\"include\\\":\\\"#lambda-nested-incomplete\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"match\\\":\\\"([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(?:(,)|(?=:|$))\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#backticks\\\"},{\\\"include\\\":\\\"#illegal-anno\\\"},{\\\"include\\\":\\\"#lambda-parameter-with-default\\\"},{\\\"include\\\":\\\"#line-continuation\\\"},{\\\"include\\\":\\\"#illegal-operator\\\"}]}]},\\\"lambda-incomplete\\\":{\\\"match\\\":\\\"\\\\\\\\blambda(?=\\\\\\\\s*[,)])\\\",\\\"name\\\":\\\"storage.type.function.lambda.python\\\"},\\\"lambda-nested-incomplete\\\":{\\\"match\\\":\\\"\\\\\\\\blambda(?=\\\\\\\\s*[:,)])\\\",\\\"name\\\":\\\"storage.type.function.lambda.python\\\"},\\\"lambda-parameter-with-default\\\":{\\\"begin\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.python\\\"}},\\\"end\\\":\\\"(,)|(?=:|$)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"line-continuation\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.continuation.line.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.line.continuation.python\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\s*(\\\\\\\\S.*$\\\\\\\\n?)\\\"},{\\\"begin\\\":\\\"(\\\\\\\\\\\\\\\\)\\\\\\\\s*$\\\\\\\\n?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.continuation.line.python\\\"}},\\\"end\\\":\\\"(?=^\\\\\\\\s*$)|(?!(\\\\\\\\s*[rR]?(\\\\\\\\'\\\\\\\\'\\\\\\\\'|\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"|\\\\\\\\'|\\\\\\\\\\\\\\\"))|(\\\\\\\\G$))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp\\\"},{\\\"include\\\":\\\"#string\\\"}]}]},\\\"list\\\":{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.begin.python\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.list.end.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"literal\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(True|False|None|NotImplemented|Ellipsis)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.python\\\"},{\\\"include\\\":\\\"#number\\\"}]},\\\"loose-default\\\":{\\\"begin\\\":\\\"(=)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.python\\\"}},\\\"end\\\":\\\"(,)|(?=\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"magic-function-names\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.magic.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(__(?:abs|add|aenter|aexit|aiter|and|anext|await|bool|call|ceil|class_getitem|cmp|coerce|complex|contains|copy|deepcopy|del|delattr|delete|delitem|delslice|dir|div|divmod|enter|eq|exit|float|floor|floordiv|format|ge|get|getattr|getattribute|getinitargs|getitem|getnewargs|getslice|getstate|gt|hash|hex|iadd|iand|idiv|ifloordiv||ilshift|imod|imul|index|init|instancecheck|int|invert|ior|ipow|irshift|isub|iter|itruediv|ixor|le|len|long|lshift|lt|missing|mod|mul|ne|neg|new|next|nonzero|oct|or|pos|pow|radd|rand|rdiv|rdivmod|reduce|reduce_ex|repr|reversed|rfloordiv||rlshift|rmod|rmul|ror|round|rpow|rrshift|rshift|rsub|rtruediv|rxor|set|setattr|setitem|set_name|setslice|setstate|sizeof|str|sub|subclasscheck|truediv|trunc|unicode|xor|matmul|rmatmul|imatmul|init_subclass|set_name|fspath|bytes|prepare|length_hint)__)\\\\\\\\b\\\"},\\\"magic-names\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#magic-function-names\\\"},{\\\"include\\\":\\\"#magic-variable-names\\\"}]},\\\"magic-variable-names\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.variable.magic.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(__(?:all|annotations|bases|builtins|class|closure|code|debug|defaults|dict|doc|file|func|globals|kwdefaults|match_args|members|metaclass|methods|module|mro|mro_entries|name|qualname|post_init|self|signature|slots|subclasses|version|weakref|wrapped|classcell|spec|path|package|future|traceback)__)\\\\\\\\b\\\"},\\\"member-access\\\":{\\\"begin\\\":\\\"(\\\\\\\\.)\\\\\\\\s*(?!\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.period.python\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\S)(?=\\\\\\\\W)|(^|(?<=\\\\\\\\s))(?=[^\\\\\\\\\\\\\\\\\\\\\\\\w\\\\\\\\s])|$\\\",\\\"name\\\":\\\"meta.member.access.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-call\\\"},{\\\"include\\\":\\\"#member-access-base\\\"},{\\\"include\\\":\\\"#member-access-attribute\\\"}]},\\\"member-access-attribute\\\":{\\\"match\\\":\\\"\\\\\\\\b([[:alpha:]_]\\\\\\\\w*)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.attribute.python\\\"},\\\"member-access-base\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#magic-names\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"include\\\":\\\"#special-names\\\"},{\\\"include\\\":\\\"#line-continuation\\\"},{\\\"include\\\":\\\"#item-access\\\"}]},\\\"member-access-class\\\":{\\\"begin\\\":\\\"(\\\\\\\\.)\\\\\\\\s*(?!\\\\\\\\.)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.period.python\\\"}},\\\"end\\\":\\\"(?<=\\\\\\\\S)(?=\\\\\\\\W)|$\\\",\\\"name\\\":\\\"meta.member.access.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#call-wrapper-inheritance\\\"},{\\\"include\\\":\\\"#member-access-base\\\"},{\\\"include\\\":\\\"#inheritance-identifier\\\"}]},\\\"number\\\":{\\\"name\\\":\\\"constant.numeric.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#number-float\\\"},{\\\"include\\\":\\\"#number-dec\\\"},{\\\"include\\\":\\\"#number-hex\\\"},{\\\"include\\\":\\\"#number-oct\\\"},{\\\"include\\\":\\\"#number-bin\\\"},{\\\"include\\\":\\\"#number-long\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\w+\\\",\\\"name\\\":\\\"invalid.illegal.name.python\\\"}]},\\\"number-bin\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.number.python\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w\\\\\\\\.])(0[bB])(_?[01])+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.bin.python\\\"},\\\"number-dec\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.imaginary.number.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.dec.python\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w\\\\\\\\.])(?:[1-9](?:_?[0-9])*|0+|[0-9](?:_?[0-9])*([jJ])|0([0-9]+)(?![eE\\\\\\\\.]))\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.dec.python\\\"},\\\"number-float\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.imaginary.number.python\\\"}},\\\"match\\\":\\\"(?<!\\\\\\\\w)(?:(?:\\\\\\\\.[0-9](?:_?[0-9])*|[0-9](?:_?[0-9])*\\\\\\\\.[0-9](?:_?[0-9])*|[0-9](?:_?[0-9])*\\\\\\\\.)(?:[eE][+-]?[0-9](?:_?[0-9])*)?|[0-9](?:_?[0-9])*(?:[eE][+-]?[0-9](?:_?[0-9])*))([jJ])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.python\\\"},\\\"number-hex\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.number.python\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w\\\\\\\\.])(0[xX])(_?[0-9a-fA-F])+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hex.python\\\"},\\\"number-long\\\":{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"storage.type.number.python\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w\\\\\\\\.])([1-9][0-9]*|0)([lL])\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.bin.python\\\"},\\\"number-oct\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.number.python\\\"}},\\\"match\\\":\\\"(?<![\\\\\\\\w\\\\\\\\.])(0[oO])(_?[0-7])+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.oct.python\\\"},\\\"odd-function-call\\\":{\\\"begin\\\":\\\"(?<=\\\\\\\\]|\\\\\\\\))\\\\\\\\s*(?=\\\\\\\\()\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.arguments.end.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#function-arguments\\\"}]},\\\"operator\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.logical.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.bitwise.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"keyword.operator.arithmetic.python\\\"},\\\"5\\\":{\\\"name\\\":\\\"keyword.operator.comparison.python\\\"},\\\"6\\\":{\\\"name\\\":\\\"keyword.operator.assignment.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(?:(and|or|not|in|is)|(for|if|else|await|(?:yield(?:\\\\\\\\s+from)?)))(?!\\\\\\\\s*:)\\\\\\\\b|(<<|>>|&|\\\\\\\\||\\\\\\\\^|~)|(\\\\\\\\*\\\\\\\\*|\\\\\\\\*|\\\\\\\\+|-|%|//|/|@)|(!=|==|>=|<=|<|>)|(:=)\\\"},\\\"parameter-special\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.function.language.special.self.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"variable.parameter.function.language.special.cls.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b((self)|(cls))\\\\\\\\b\\\\\\\\s*(?:(,)|(?=\\\\\\\\)))\\\"},\\\"parameters\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.parameters.end.python\\\"}},\\\"name\\\":\\\"meta.function.parameters.python\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"/\\\",\\\"name\\\":\\\"keyword.operator.positional.parameter.python\\\"},{\\\"match\\\":\\\"(\\\\\\\\*\\\\\\\\*|\\\\\\\\*)\\\",\\\"name\\\":\\\"keyword.operator.unpacking.parameter.python\\\"},{\\\"include\\\":\\\"#lambda-incomplete\\\"},{\\\"include\\\":\\\"#illegal-names\\\"},{\\\"include\\\":\\\"#illegal-object-name\\\"},{\\\"include\\\":\\\"#parameter-special\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.function.language.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.parameters.python\\\"}},\\\"match\\\":\\\"([[:alpha:]_]\\\\\\\\w*)\\\\\\\\s*(?:(,)|(?=[)#\\\\\\\\n=]))\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#loose-default\\\"},{\\\"include\\\":\\\"#annotated-parameter\\\"}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\":\\\",\\\"name\\\":\\\"punctuation.separator.colon.python\\\"},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.element.python\\\"}]},\\\"regexp\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-single-three-line\\\"},{\\\"include\\\":\\\"#regexp-double-three-line\\\"},{\\\"include\\\":\\\"#regexp-single-one-line\\\"},{\\\"include\\\":\\\"#regexp-double-one-line\\\"}]},\\\"regexp-backreference\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.backreference.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp\\\"}},\\\"match\\\":\\\"(\\\\\\\\()(\\\\\\\\?P=\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?)(\\\\\\\\))\\\",\\\"name\\\":\\\"meta.backreference.named.regexp\\\"},\\\"regexp-backreference-number\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.tag.backreference.regexp\\\"}},\\\"match\\\":\\\"(\\\\\\\\\\\\\\\\[1-9]\\\\\\\\d?)\\\",\\\"name\\\":\\\"meta.backreference.regexp\\\"},\\\"regexp-base-common\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.\\\",\\\"name\\\":\\\"support.other.match.any.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\^\\\",\\\"name\\\":\\\"support.other.match.begin.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\$\\\",\\\"name\\\":\\\"support.other.match.end.regexp\\\"},{\\\"match\\\":\\\"[+*?]\\\\\\\\??\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"match\\\":\\\"\\\\\\\\|\\\",\\\"name\\\":\\\"keyword.operator.disjunction.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-sequence\\\"}]},\\\"regexp-base-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-quantifier\\\"},{\\\"include\\\":\\\"#regexp-base-common\\\"}]},\\\"regexp-charecter-set-escapes\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\[abfnrtv\\\\\\\\\\\\\\\\]\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-special\\\"},{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([0-7]{1,3})\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},{\\\"include\\\":\\\"#regexp-escape-character\\\"},{\\\"include\\\":\\\"#regexp-escape-unicode\\\"},{\\\"include\\\":\\\"#regexp-escape-catchall\\\"}]},\\\"regexp-double-one-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\")|(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.regexp.quoted.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double-one-regexp-expression\\\"}]},\\\"regexp-double-three-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.regexp.quoted.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#double-three-regexp-expression\\\"}]},\\\"regexp-escape-catchall\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(.|\\\\\\\\n)\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},\\\"regexp-escape-character\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(x[0-9A-Fa-f]{2}|0[0-7]{1,2}|[0-7]{3})\\\",\\\"name\\\":\\\"constant.character.escape.regexp\\\"},\\\"regexp-escape-sequence\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-escape-special\\\"},{\\\"include\\\":\\\"#regexp-escape-character\\\"},{\\\"include\\\":\\\"#regexp-escape-unicode\\\"},{\\\"include\\\":\\\"#regexp-backreference-number\\\"},{\\\"include\\\":\\\"#regexp-escape-catchall\\\"}]},\\\"regexp-escape-special\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\([AbBdDsSwWZ])\\\",\\\"name\\\":\\\"support.other.escape.special.regexp\\\"},\\\"regexp-escape-unicode\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})\\\",\\\"name\\\":\\\"constant.character.unicode.regexp\\\"},\\\"regexp-flags\\\":{\\\"match\\\":\\\"\\\\\\\\(\\\\\\\\?[aiLmsux]+\\\\\\\\)\\\",\\\"name\\\":\\\"storage.modifier.flag.regexp\\\"},\\\"regexp-quantifier\\\":{\\\"match\\\":\\\"\\\\\\\\{(\\\\\\\\d+|\\\\\\\\d+,(\\\\\\\\d+)?|,\\\\\\\\d+)\\\\\\\\}\\\",\\\"name\\\":\\\"keyword.operator.quantifier.regexp\\\"},\\\"regexp-single-one-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\\\\\')\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\')|(?<!\\\\\\\\\\\\\\\\)(\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.regexp.quoted.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"regexp-single-three-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]r)|([bB]r)|(r[bB]?))(\\\\\\\\'\\\\\\\\'\\\\\\\\')\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"5\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\'\\\\\\\\'\\\\\\\\')\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.regexp.quoted.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"}]},\\\"return-annotation\\\":{\\\"begin\\\":\\\"(->)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.separator.annotation.result.python\\\"}},\\\"end\\\":\\\"(?=:)\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"round-braces\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.begin.python\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.parenthesis.end.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#expression\\\"}]},\\\"semicolon\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\;$\\\",\\\"name\\\":\\\"invalid.deprecated.semicolon.python\\\"}]},\\\"single-one-regexp-character-set\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\^?\\\\\\\\](?!.*?\\\\\\\\])\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?(\\\\\\\\])?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.begin.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.set.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\]|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.end.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.character.set.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-charecter-set-escapes\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.set.regexp\\\"}]}]},\\\"single-one-regexp-comments\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.comment.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comment.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"comment.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"single-one-regexp-conditional\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?\\\\\\\\((\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?|\\\\\\\\d+)\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.conditional.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.conditional.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-base-expression\\\"},{\\\"include\\\":\\\"#single-one-regexp-character-set\\\"},{\\\"include\\\":\\\"#single-one-regexp-comments\\\"},{\\\"include\\\":\\\"#regexp-flags\\\"},{\\\"include\\\":\\\"#single-one-regexp-named-group\\\"},{\\\"include\\\":\\\"#regexp-backreference\\\"},{\\\"include\\\":\\\"#single-one-regexp-lookahead\\\"},{\\\"include\\\":\\\"#single-one-regexp-lookahead-negative\\\"},{\\\"include\\\":\\\"#single-one-regexp-lookbehind\\\"},{\\\"include\\\":\\\"#single-one-regexp-lookbehind-negative\\\"},{\\\"include\\\":\\\"#single-one-regexp-conditional\\\"},{\\\"include\\\":\\\"#single-one-regexp-parentheses-non-capturing\\\"},{\\\"include\\\":\\\"#single-one-regexp-parentheses\\\"}]},\\\"single-one-regexp-lookahead\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-lookahead-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-lookbehind\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-lookbehind-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-named-group\\\":{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?P<\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.group.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.named.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-one-regexp-parentheses-non-capturing\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'))|((?=(?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-one-regexp-expression\\\"}]},\\\"single-three-regexp-character-set\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\[\\\\\\\\^?\\\\\\\\](?!.*?\\\\\\\\])\\\"},{\\\"begin\\\":\\\"(\\\\\\\\[)(\\\\\\\\^)?(\\\\\\\\])?\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.begin.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.negation.regexp\\\"},\\\"3\\\":{\\\"name\\\":\\\"constant.character.set.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\]|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.character.set.end.regexp constant.other.set.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.character.set.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-charecter-set-escapes\\\"},{\\\"match\\\":\\\"[^\\\\\\\\n]\\\",\\\"name\\\":\\\"constant.character.set.regexp\\\"}]}]},\\\"single-three-regexp-comments\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.comment.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.comment.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"comment.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#codetags\\\"}]},\\\"single-three-regexp-conditional\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?\\\\\\\\((\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?|\\\\\\\\d+)\\\\\\\\)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.conditional.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.conditional.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-expression\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#regexp-base-expression\\\"},{\\\"include\\\":\\\"#single-three-regexp-character-set\\\"},{\\\"include\\\":\\\"#single-three-regexp-comments\\\"},{\\\"include\\\":\\\"#regexp-flags\\\"},{\\\"include\\\":\\\"#single-three-regexp-named-group\\\"},{\\\"include\\\":\\\"#regexp-backreference\\\"},{\\\"include\\\":\\\"#single-three-regexp-lookahead\\\"},{\\\"include\\\":\\\"#single-three-regexp-lookahead-negative\\\"},{\\\"include\\\":\\\"#single-three-regexp-lookbehind\\\"},{\\\"include\\\":\\\"#single-three-regexp-lookbehind-negative\\\"},{\\\"include\\\":\\\"#single-three-regexp-conditional\\\"},{\\\"include\\\":\\\"#single-three-regexp-parentheses-non-capturing\\\"},{\\\"include\\\":\\\"#single-three-regexp-parentheses\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-lookahead\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-lookahead-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookahead.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-lookbehind\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-lookbehind-negative\\\":{\\\"begin\\\":\\\"(\\\\\\\\()\\\\\\\\?<!\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp\\\"},\\\"1\\\":{\\\"name\\\":\\\"punctuation.parenthesis.lookbehind.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-named-group\\\":{\\\"begin\\\":\\\"(\\\\\\\\()(\\\\\\\\?P<\\\\\\\\w+(?:\\\\\\\\s+[[:alnum:]]+)?>)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.tag.named.group.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"meta.named.regexp\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-parentheses\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"single-three-regexp-parentheses-non-capturing\\\":{\\\"begin\\\":\\\"\\\\\\\\(\\\\\\\\?:\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\\\"}},\\\"end\\\":\\\"(\\\\\\\\)|(?=\\\\\\\\'\\\\\\\\'\\\\\\\\'))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#single-three-regexp-expression\\\"},{\\\"include\\\":\\\"#comments-string-single-three\\\"}]},\\\"special-names\\\":{\\\"match\\\":\\\"\\\\\\\\b(_*[[:upper:]][_\\\\\\\\d]*[[:upper:]])[[:upper:]\\\\\\\\d]*(_\\\\\\\\w*)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.other.caps.python\\\"},\\\"special-variables\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.language.special.self.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.language.special.cls.python\\\"}},\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(?:(self)|(cls))\\\\\\\\b\\\"},\\\"statement\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#import\\\"},{\\\"include\\\":\\\"#class-declaration\\\"},{\\\"include\\\":\\\"#function-declaration\\\"},{\\\"include\\\":\\\"#generator\\\"},{\\\"include\\\":\\\"#statement-keyword\\\"},{\\\"include\\\":\\\"#assignment-operator\\\"},{\\\"include\\\":\\\"#decorator\\\"},{\\\"include\\\":\\\"#docstring-statement\\\"},{\\\"include\\\":\\\"#semicolon\\\"}]},\\\"statement-keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b((async\\\\\\\\s+)?\\\\\\\\s*def)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.function.python\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)as\\\\\\\\b(?=.*[:\\\\\\\\\\\\\\\\])\\\",\\\"name\\\":\\\"keyword.control.flow.python\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)as\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.python\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(async|continue|del|assert|break|finally|for|from|elif|else|if|except|pass|raise|return|try|while|with)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.flow.python\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(global|nonlocal)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.declaration.python\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<!\\\\\\\\.)(class)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.class.python\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.flow.python\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(case|match)(?=\\\\\\\\s*([-+\\\\\\\\w\\\\\\\\d(\\\\\\\\[{'\\\\\\\":#]|$))\\\\\\\\b\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-quoted-multi-line\\\"},{\\\"include\\\":\\\"#string-quoted-single-line\\\"},{\\\"include\\\":\\\"#string-bin-quoted-multi-line\\\"},{\\\"include\\\":\\\"#string-bin-quoted-single-line\\\"},{\\\"include\\\":\\\"#string-raw-quoted-multi-line\\\"},{\\\"include\\\":\\\"#string-raw-quoted-single-line\\\"},{\\\"include\\\":\\\"#string-raw-bin-quoted-multi-line\\\"},{\\\"include\\\":\\\"#string-raw-bin-quoted-single-line\\\"},{\\\"include\\\":\\\"#fstring-fnorm-quoted-multi-line\\\"},{\\\"include\\\":\\\"#fstring-fnorm-quoted-single-line\\\"},{\\\"include\\\":\\\"#fstring-normf-quoted-multi-line\\\"},{\\\"include\\\":\\\"#fstring-normf-quoted-single-line\\\"},{\\\"include\\\":\\\"#fstring-raw-quoted-multi-line\\\"},{\\\"include\\\":\\\"#fstring-raw-quoted-single-line\\\"}]},\\\"string-bin-quoted-multi-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[bB])('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.binary.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-entity\\\"}]},\\\"string-bin-quoted-single-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b[bB])((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.binary.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-entity\\\"}]},\\\"string-brace-formatting\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"match\\\":\\\"({{|}}|(?:{\\\\\\\\w*(\\\\\\\\.[[:alpha:]_]\\\\\\\\w*|\\\\\\\\[[^\\\\\\\\]'\\\\\\\"]+\\\\\\\\])*(![rsa])?(:\\\\\\\\w?[<>=^]?[-+ ]?\\\\\\\\#?\\\\\\\\d*,?(\\\\\\\\.\\\\\\\\d+)?[bcdeEfFgGnosxX%]?)?}))\\\",\\\"name\\\":\\\"meta.format.brace.python\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"storage.type.format.python\\\"}},\\\"match\\\":\\\"({\\\\\\\\w*(\\\\\\\\.[[:alpha:]_]\\\\\\\\w*|\\\\\\\\[[^\\\\\\\\]'\\\\\\\"]+\\\\\\\\])*(![rsa])?(:)[^'\\\\\\\"{}\\\\\\\\n]*(?:\\\\\\\\{[^'\\\\\\\"}\\\\\\\\n]*?\\\\\\\\}[^'\\\\\\\"{}\\\\\\\\n]*)*})\\\",\\\"name\\\":\\\"meta.format.brace.python\\\"}]},\\\"string-consume-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\['\\\\\\\"\\\\\\\\n\\\\\\\\\\\\\\\\]\\\"},\\\"string-entity\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence\\\"},{\\\"include\\\":\\\"#string-line-continuation\\\"},{\\\"include\\\":\\\"#string-formatting\\\"}]},\\\"string-formatting\\\":{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.character.format.placeholder.other.python\\\"}},\\\"match\\\":\\\"(%(\\\\\\\\([\\\\\\\\w\\\\\\\\s]*\\\\\\\\))?[-+#0 ]*(\\\\\\\\d+|\\\\\\\\*)?(\\\\\\\\.(\\\\\\\\d+|\\\\\\\\*))?([hlL])?[diouxXeEfFgGcrsab%])\\\",\\\"name\\\":\\\"meta.format.percent.python\\\"},\\\"string-line-continuation\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\$\\\",\\\"name\\\":\\\"constant.language.python\\\"},\\\"string-multi-bad-brace1-formatting-raw\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{%(.*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\"))%\\\\\\\\})\\\",\\\"end\\\":\\\"(?='''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"}]},\\\"string-multi-bad-brace1-formatting-unicode\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{%(.*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\"))%\\\\\\\\})\\\",\\\"end\\\":\\\"(?='''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#escape-sequence\\\"},{\\\"include\\\":\\\"#string-line-continuation\\\"}]},\\\"string-multi-bad-brace2-formatting-raw\\\":{\\\"begin\\\":\\\"(?!\\\\\\\\{\\\\\\\\{)(?=\\\\\\\\{(\\\\\\\\w*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\")[^!:\\\\\\\\.\\\\\\\\[}\\\\\\\\w]).*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\")\\\\\\\\})\\\",\\\"end\\\":\\\"(?='''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#string-formatting\\\"}]},\\\"string-multi-bad-brace2-formatting-unicode\\\":{\\\"begin\\\":\\\"(?!\\\\\\\\{\\\\\\\\{)(?=\\\\\\\\{(\\\\\\\\w*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\")[^!:\\\\\\\\.\\\\\\\\[}\\\\\\\\w]).*?(?!'''|\\\\\\\"\\\\\\\"\\\\\\\")\\\\\\\\})\\\",\\\"end\\\":\\\"(?='''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#string-entity\\\"}]},\\\"string-quoted-multi-line\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\b([rR])(?=[uU]))?([uU])?('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-multi-bad-brace1-formatting-unicode\\\"},{\\\"include\\\":\\\"#string-multi-bad-brace2-formatting-unicode\\\"},{\\\"include\\\":\\\"#string-unicode-guts\\\"}]},\\\"string-quoted-single-line\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\b([rR])(?=[uU]))?([uU])?((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"invalid.illegal.prefix.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\3)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-single-bad-brace1-formatting-unicode\\\"},{\\\"include\\\":\\\"#string-single-bad-brace2-formatting-unicode\\\"},{\\\"include\\\":\\\"#string-unicode-guts\\\"}]},\\\"string-raw-bin-guts\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#string-formatting\\\"}]},\\\"string-raw-bin-quoted-multi-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:R[bB]|[bB]R))('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.raw.binary.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-raw-bin-guts\\\"}]},\\\"string-raw-bin-quoted-single-line\\\":{\\\"begin\\\":\\\"(\\\\\\\\b(?:R[bB]|[bB]R))((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\2)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.raw.binary.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-raw-bin-guts\\\"}]},\\\"string-raw-guts\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#string-formatting\\\"},{\\\"include\\\":\\\"#string-brace-formatting\\\"}]},\\\"string-raw-quoted-multi-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]R)|(R))('''|\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\4)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.raw.multi.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-multi-bad-brace1-formatting-raw\\\"},{\\\"include\\\":\\\"#string-multi-bad-brace2-formatting-raw\\\"},{\\\"include\\\":\\\"#string-raw-guts\\\"}]},\\\"string-raw-quoted-single-line\\\":{\\\"begin\\\":\\\"\\\\\\\\b(([uU]R)|(R))((['\\\\\\\"]))\\\",\\\"beginCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"invalid.deprecated.prefix.python\\\"},\\\"3\\\":{\\\"name\\\":\\\"storage.type.string.python\\\"},\\\"4\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.python\\\"}},\\\"end\\\":\\\"(\\\\\\\\4)|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.python\\\"},\\\"2\\\":{\\\"name\\\":\\\"invalid.illegal.newline.python\\\"}},\\\"name\\\":\\\"string.quoted.raw.single.python\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-single-bad-brace1-formatting-raw\\\"},{\\\"include\\\":\\\"#string-single-bad-brace2-formatting-raw\\\"},{\\\"include\\\":\\\"#string-raw-guts\\\"}]},\\\"string-single-bad-brace1-formatting-raw\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{%(.*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)))%\\\\\\\\})\\\",\\\"end\\\":\\\"(?=(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"}]},\\\"string-single-bad-brace1-formatting-unicode\\\":{\\\"begin\\\":\\\"(?=\\\\\\\\{%(.*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n)))%\\\\\\\\})\\\",\\\"end\\\":\\\"(?=(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#escape-sequence\\\"},{\\\"include\\\":\\\"#string-line-continuation\\\"}]},\\\"string-single-bad-brace2-formatting-raw\\\":{\\\"begin\\\":\\\"(?!\\\\\\\\{\\\\\\\\{)(?=\\\\\\\\{(\\\\\\\\w*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))[^!:\\\\\\\\.\\\\\\\\[}\\\\\\\\w]).*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\\\\\\})\\\",\\\"end\\\":\\\"(?=(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-consume-escape\\\"},{\\\"include\\\":\\\"#string-formatting\\\"}]},\\\"string-single-bad-brace2-formatting-unicode\\\":{\\\"begin\\\":\\\"(?!\\\\\\\\{\\\\\\\\{)(?=\\\\\\\\{(\\\\\\\\w*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))[^!:\\\\\\\\.\\\\\\\\[}\\\\\\\\w]).*?(?!(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\\\\\\})\\\",\\\"end\\\":\\\"(?=(['\\\\\\\"])|((?<!\\\\\\\\\\\\\\\\)\\\\\\\\n))\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#string-entity\\\"}]},\\\"string-unicode-guts\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#escape-sequence-unicode\\\"},{\\\"include\\\":\\\"#string-entity\\\"},{\\\"include\\\":\\\"#string-brace-formatting\\\"}]}},\\\"scopeName\\\":\\\"source.python\\\",\\\"aliases\\\":[\\\"py\\\"]}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/r.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"R\\\",\\\"name\\\":\\\"r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#roxygen\\\"},{\\\"include\\\":\\\"#comments\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#storage-type\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#brackets\\\"},{\\\"include\\\":\\\"#function-declarations\\\"},{\\\"include\\\":\\\"#lambda-functions\\\"},{\\\"include\\\":\\\"#builtin-functions\\\"},{\\\"include\\\":\\\"#function-calls\\\"},{\\\"include\\\":\\\"#general-variables\\\"}],\\\"repository\\\":{\\\"brackets\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[(?!\\\\\\\\[)\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.single.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.single.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\[\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.double.begin.r\\\"}},\\\"contentName\\\":\\\"meta.item-access.arguments.r\\\",\\\"end\\\":\\\"\\\\\\\\]\\\\\\\\]\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.brackets.double.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.braces.end.r\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]}]},\\\"builtin-functions\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(abbreviate|abs|acos|acosh|activeBindingFunction|addNA|addTaskCallback|agrep|agrepl|alist|all|all\\\\\\\\.equal|all\\\\\\\\.equal\\\\\\\\.character|all\\\\\\\\.equal\\\\\\\\.default|all\\\\\\\\.equal\\\\\\\\.environment|all\\\\\\\\.equal\\\\\\\\.envRefClass|all\\\\\\\\.equal\\\\\\\\.factor|all\\\\\\\\.equal\\\\\\\\.formula|all\\\\\\\\.equal\\\\\\\\.function|all\\\\\\\\.equal\\\\\\\\.language|all\\\\\\\\.equal\\\\\\\\.list|all\\\\\\\\.equal\\\\\\\\.numeric|all\\\\\\\\.equal\\\\\\\\.POSIXt|all\\\\\\\\.equal\\\\\\\\.raw|all\\\\\\\\.names|allowInterrupts|all\\\\\\\\.vars|any|anyDuplicated|anyDuplicated\\\\\\\\.array|anyDuplicated\\\\\\\\.data\\\\\\\\.frame|anyDuplicated\\\\\\\\.default|anyDuplicated\\\\\\\\.matrix|anyNA|anyNA\\\\\\\\.data\\\\\\\\.frame|anyNA\\\\\\\\.numeric_version|anyNA\\\\\\\\.POSIXlt|aperm|aperm\\\\\\\\.default|aperm\\\\\\\\.table|append|apply|Arg|args|array|arrayInd|as\\\\\\\\.array|as\\\\\\\\.array\\\\\\\\.default|as\\\\\\\\.call|as\\\\\\\\.character|as\\\\\\\\.character\\\\\\\\.condition|as\\\\\\\\.character\\\\\\\\.Date|as\\\\\\\\.character\\\\\\\\.default|as\\\\\\\\.character\\\\\\\\.error|as\\\\\\\\.character\\\\\\\\.factor|as\\\\\\\\.character\\\\\\\\.hexmode|as\\\\\\\\.character\\\\\\\\.numeric_version|as\\\\\\\\.character\\\\\\\\.octmode|as\\\\\\\\.character\\\\\\\\.POSIXt|as\\\\\\\\.character\\\\\\\\.srcref|as\\\\\\\\.complex|as\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.array|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.AsIs|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.character|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.complex|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.Date|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.default|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.difftime|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.factor|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.integer|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.list|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.logical|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.matrix|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.model\\\\\\\\.matrix|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.noquote|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.numeric|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.numeric_version|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.ordered|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.POSIXct|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.POSIXlt|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.raw|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.table|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.ts|as\\\\\\\\.data\\\\\\\\.frame\\\\\\\\.vector|as\\\\\\\\.Date|as\\\\\\\\.Date\\\\\\\\.character|as\\\\\\\\.Date\\\\\\\\.default|as\\\\\\\\.Date\\\\\\\\.factor|as\\\\\\\\.Date\\\\\\\\.numeric|as\\\\\\\\.Date\\\\\\\\.POSIXct|as\\\\\\\\.Date\\\\\\\\.POSIXlt|as\\\\\\\\.difftime|as\\\\\\\\.double|as\\\\\\\\.double\\\\\\\\.difftime|as\\\\\\\\.double\\\\\\\\.POSIXlt|as\\\\\\\\.environment|as\\\\\\\\.expression|as\\\\\\\\.expression\\\\\\\\.default|as\\\\\\\\.factor|as\\\\\\\\.function|as\\\\\\\\.function\\\\\\\\.default|as\\\\\\\\.hexmode|asin|asinh|as\\\\\\\\.integer|as\\\\\\\\.list|as\\\\\\\\.list\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.list\\\\\\\\.Date|as\\\\\\\\.list\\\\\\\\.default|as\\\\\\\\.list\\\\\\\\.difftime|as\\\\\\\\.list\\\\\\\\.environment|as\\\\\\\\.list\\\\\\\\.factor|as\\\\\\\\.list\\\\\\\\.function|as\\\\\\\\.list\\\\\\\\.numeric_version|as\\\\\\\\.list\\\\\\\\.POSIXct|as\\\\\\\\.list\\\\\\\\.POSIXlt|as\\\\\\\\.logical|as\\\\\\\\.logical\\\\\\\\.factor|as\\\\\\\\.matrix|as\\\\\\\\.matrix\\\\\\\\.data\\\\\\\\.frame|as\\\\\\\\.matrix\\\\\\\\.default|as\\\\\\\\.matrix\\\\\\\\.noquote|as\\\\\\\\.matrix\\\\\\\\.POSIXlt|as\\\\\\\\.name|asNamespace|as\\\\\\\\.null|as\\\\\\\\.null\\\\\\\\.default|as\\\\\\\\.numeric|as\\\\\\\\.numeric_version|as\\\\\\\\.octmode|as\\\\\\\\.ordered|as\\\\\\\\.package_version|as\\\\\\\\.pairlist|asplit|as\\\\\\\\.POSIXct|as\\\\\\\\.POSIXct\\\\\\\\.Date|as\\\\\\\\.POSIXct\\\\\\\\.default|as\\\\\\\\.POSIXct\\\\\\\\.numeric|as\\\\\\\\.POSIXct\\\\\\\\.POSIXlt|as\\\\\\\\.POSIXlt|as\\\\\\\\.POSIXlt\\\\\\\\.character|as\\\\\\\\.POSIXlt\\\\\\\\.Date|as\\\\\\\\.POSIXlt\\\\\\\\.default|as\\\\\\\\.POSIXlt\\\\\\\\.factor|as\\\\\\\\.POSIXlt\\\\\\\\.numeric|as\\\\\\\\.POSIXlt\\\\\\\\.POSIXct|as\\\\\\\\.qr|as\\\\\\\\.raw|asS3|asS4|assign|as\\\\\\\\.single|as\\\\\\\\.single\\\\\\\\.default|as\\\\\\\\.symbol|as\\\\\\\\.table|as\\\\\\\\.table\\\\\\\\.default|as\\\\\\\\.vector|as\\\\\\\\.vector\\\\\\\\.factor|atan|atan2|atanh|attach|attachNamespace|attr|attr\\\\\\\\.all\\\\\\\\.equal|attributes|autoload|autoloader|backsolve|baseenv|basename|besselI|besselJ|besselK|besselY|beta|bindingIsActive|bindingIsLocked|bindtextdomain|bitwAnd|bitwNot|bitwOr|bitwShiftL|bitwShiftR|bitwXor|body|bquote|break|browser|browserCondition|browserSetDebug|browserText|builtins|by|by\\\\\\\\.data\\\\\\\\.frame|by\\\\\\\\.default|bzfile|c|call|callCC|capabilities|casefold|cat|cbind|cbind\\\\\\\\.data\\\\\\\\.frame|c\\\\\\\\.Date|c\\\\\\\\.difftime|ceiling|c\\\\\\\\.factor|character|char\\\\\\\\.expand|charmatch|charToRaw|chartr|check_tzones|chkDots|chol|chol2inv|chol\\\\\\\\.default|choose|class|clearPushBack|close|closeAllConnections|close\\\\\\\\.connection|close\\\\\\\\.srcfile|close\\\\\\\\.srcfilealias|c\\\\\\\\.noquote|c\\\\\\\\.numeric_version|col|colMeans|colnames|colSums|commandArgs|comment|complex|computeRestarts|conditionCall|conditionCall\\\\\\\\.condition|conditionMessage|conditionMessage\\\\\\\\.condition|conflictRules|conflicts|Conj|contributors|cos|cosh|cospi|c\\\\\\\\.POSIXct|c\\\\\\\\.POSIXlt|crossprod|Cstack_info|cummax|cummin|cumprod|cumsum|curlGetHeaders|cut|cut\\\\\\\\.Date|cut\\\\\\\\.default|cut\\\\\\\\.POSIXt|c\\\\\\\\.warnings|data\\\\\\\\.class|data\\\\\\\\.frame|data\\\\\\\\.matrix|date|debug|debuggingState|debugonce|default\\\\\\\\.stringsAsFactors|delayedAssign|deparse|deparse1|det|detach|determinant|determinant\\\\\\\\.matrix|dget|diag|diff|diff\\\\\\\\.Date|diff\\\\\\\\.default|diff\\\\\\\\.difftime|diff\\\\\\\\.POSIXt|difftime|digamma|dim|dim\\\\\\\\.data\\\\\\\\.frame|dimnames|dimnames\\\\\\\\.data\\\\\\\\.frame|dir|dir\\\\\\\\.create|dir\\\\\\\\.exists|dirname|do\\\\\\\\.call|dontCheck|double|dput|dQuote|drop|droplevels|droplevels\\\\\\\\.data\\\\\\\\.frame|droplevels\\\\\\\\.factor|dump|duplicated|duplicated\\\\\\\\.array|duplicated\\\\\\\\.data\\\\\\\\.frame|duplicated\\\\\\\\.default|duplicated\\\\\\\\.matrix|duplicated\\\\\\\\.numeric_version|duplicated\\\\\\\\.POSIXlt|duplicated\\\\\\\\.warnings|dynGet|dyn\\\\\\\\.load|dyn\\\\\\\\.unload|eapply|eigen|emptyenv|enc2native|enc2utf8|encodeString|Encoding|endsWith|enquote|environment|environmentIsLocked|environmentName|env\\\\\\\\.profile|errorCondition|eval|eval\\\\\\\\.parent|evalq|exists|exp|expand\\\\\\\\.grid|expm1|expression|extSoftVersion|factor|factorial|fifo|file|file\\\\\\\\.access|file\\\\\\\\.append|file\\\\\\\\.choose|file\\\\\\\\.copy|file\\\\\\\\.create|file\\\\\\\\.exists|file\\\\\\\\.info|file\\\\\\\\.link|file\\\\\\\\.mode|file\\\\\\\\.mtime|file\\\\\\\\.path|file\\\\\\\\.remove|file\\\\\\\\.rename|file\\\\\\\\.show|file\\\\\\\\.size|file\\\\\\\\.symlink|Filter|Find|findInterval|find\\\\\\\\.package|findPackageEnv|findRestart|floor|flush|flush\\\\\\\\.connection|for|force|forceAndCall|formals|format|format\\\\\\\\.AsIs|formatC|format\\\\\\\\.data\\\\\\\\.frame|format\\\\\\\\.Date|format\\\\\\\\.default|format\\\\\\\\.difftime|formatDL|format\\\\\\\\.factor|format\\\\\\\\.hexmode|format\\\\\\\\.info|format\\\\\\\\.libraryIQR|format\\\\\\\\.numeric_version|format\\\\\\\\.octmode|format\\\\\\\\.packageInfo|format\\\\\\\\.POSIXct|format\\\\\\\\.POSIXlt|format\\\\\\\\.pval|format\\\\\\\\.summaryDefault|forwardsolve|function|gamma|gc|gcinfo|gc\\\\\\\\.time|gctorture|gctorture2|get|get0|getAllConnections|getCallingDLL|getCallingDLLe|getConnection|getDLLRegisteredRoutines|getDLLRegisteredRoutines\\\\\\\\.character|getDLLRegisteredRoutines\\\\\\\\.DLLInfo|getElement|geterrmessage|getExportedValue|getHook|getLoadedDLLs|getNamespace|getNamespaceExports|getNamespaceImports|getNamespaceInfo|getNamespaceName|getNamespaceUsers|getNamespaceVersion|getNativeSymbolInfo|getOption|getRversion|getSrcLines|getTaskCallbackNames|gettext|gettextf|getwd|gl|globalCallingHandlers|globalenv|gregexec|gregexpr|grep|grepl|grepRaw|grouping|gsub|gzcon|gzfile|I|iconv|iconvlist|icuGetCollate|icuSetCollate|identical|identity|if|ifelse|Im|importIntoEnv|infoRDS|inherits|integer|interaction|interactive|intersect|intToBits|intToUtf8|inverse\\\\\\\\.rle|invisible|invokeRestart|invokeRestartInteractively|isa|is\\\\\\\\.array|is\\\\\\\\.atomic|isatty|isBaseNamespace|is\\\\\\\\.call|is\\\\\\\\.character|is\\\\\\\\.complex|is\\\\\\\\.data\\\\\\\\.frame|isdebugged|is\\\\\\\\.double|is\\\\\\\\.element|is\\\\\\\\.environment|is\\\\\\\\.expression|is\\\\\\\\.factor|isFALSE|is\\\\\\\\.finite|is\\\\\\\\.function|isIncomplete|is\\\\\\\\.infinite|is\\\\\\\\.integer|is\\\\\\\\.language|is\\\\\\\\.list|is\\\\\\\\.loaded|is\\\\\\\\.logical|is\\\\\\\\.matrix|is\\\\\\\\.na|is\\\\\\\\.na\\\\\\\\.data\\\\\\\\.frame|is\\\\\\\\.name|isNamespace|isNamespaceLoaded|is\\\\\\\\.nan|is\\\\\\\\.na\\\\\\\\.numeric_version|is\\\\\\\\.na\\\\\\\\.POSIXlt|is\\\\\\\\.null|is\\\\\\\\.numeric|is\\\\\\\\.numeric\\\\\\\\.Date|is\\\\\\\\.numeric\\\\\\\\.difftime|is\\\\\\\\.numeric\\\\\\\\.POSIXt|is\\\\\\\\.numeric_version|is\\\\\\\\.object|ISOdate|ISOdatetime|isOpen|is\\\\\\\\.ordered|is\\\\\\\\.package_version|is\\\\\\\\.pairlist|is\\\\\\\\.primitive|is\\\\\\\\.qr|is\\\\\\\\.R|is\\\\\\\\.raw|is\\\\\\\\.recursive|isRestart|isS4|isSeekable|is\\\\\\\\.single|is\\\\\\\\.symbol|isSymmetric|isSymmetric\\\\\\\\.matrix|is\\\\\\\\.table|isTRUE|is\\\\\\\\.unsorted|is\\\\\\\\.vector|jitter|julian|julian\\\\\\\\.Date|julian\\\\\\\\.POSIXt|kappa|kappa\\\\\\\\.default|kappa\\\\\\\\.lm|kappa\\\\\\\\.qr|kronecker|l10n_info|labels|labels\\\\\\\\.default|La_library|lapply|La\\\\\\\\.svd|La_version|lazyLoad|lazyLoadDBexec|lazyLoadDBfetch|lbeta|lchoose|length|length\\\\\\\\.POSIXlt|lengths|levels|levels\\\\\\\\.default|lfactorial|lgamma|libcurlVersion|library|library\\\\\\\\.dynam|library\\\\\\\\.dynam\\\\\\\\.unload|licence|license|list|list2DF|list2env|list\\\\\\\\.dirs|list\\\\\\\\.files|load|loadedNamespaces|loadingNamespaceInfo|loadNamespace|local|lockBinding|lockEnvironment|log|log10|log1p|log2|logb|logical|lower\\\\\\\\.tri|ls|makeActiveBinding|make\\\\\\\\.names|make\\\\\\\\.unique|Map|mapply|marginSums|margin\\\\\\\\.table|match|match\\\\\\\\.arg|match\\\\\\\\.call|match\\\\\\\\.fun|Math\\\\\\\\.data\\\\\\\\.frame|Math\\\\\\\\.Date|Math\\\\\\\\.difftime|Math\\\\\\\\.factor|Math\\\\\\\\.POSIXt|mat\\\\\\\\.or\\\\\\\\.vec|matrix|max|max\\\\\\\\.col|mean|mean\\\\\\\\.Date|mean\\\\\\\\.default|mean\\\\\\\\.difftime|mean\\\\\\\\.POSIXct|mean\\\\\\\\.POSIXlt|memCompress|memDecompress|mem\\\\\\\\.maxNSize|mem\\\\\\\\.maxVSize|memory\\\\\\\\.profile|merge|merge\\\\\\\\.data\\\\\\\\.frame|merge\\\\\\\\.default|message|mget|min|missing|Mod|mode|months|months\\\\\\\\.Date|months\\\\\\\\.POSIXt|names|namespaceExport|namespaceImport|namespaceImportClasses|namespaceImportFrom|namespaceImportMethods|names\\\\\\\\.POSIXlt|nargs|nchar|ncol|NCOL|Negate|new\\\\\\\\.env|next|NextMethod|ngettext|nlevels|noquote|norm|normalizePath|nrow|NROW|nullfile|numeric|numeric_version|numToBits|numToInts|nzchar|objects|oldClass|OlsonNames|on\\\\\\\\.exit|open|open\\\\\\\\.connection|open\\\\\\\\.srcfile|open\\\\\\\\.srcfilealias|open\\\\\\\\.srcfilecopy|Ops\\\\\\\\.data\\\\\\\\.frame|Ops\\\\\\\\.Date|Ops\\\\\\\\.difftime|Ops\\\\\\\\.factor|Ops\\\\\\\\.numeric_version|Ops\\\\\\\\.ordered|Ops\\\\\\\\.POSIXt|options|order|ordered|outer|packageEvent|packageHasNamespace|packageNotFoundError|packageStartupMessage|package_version|packBits|pairlist|parent\\\\\\\\.env|parent\\\\\\\\.frame|parse|parseNamespaceFile|paste|paste0|path\\\\\\\\.expand|path\\\\\\\\.package|pcre_config|pi|pipe|plot|pmatch|pmax|pmax\\\\\\\\.int|pmin|pmin\\\\\\\\.int|polyroot|Position|pos\\\\\\\\.to\\\\\\\\.env|pretty|pretty\\\\\\\\.default|prettyNum|print|print\\\\\\\\.AsIs|print\\\\\\\\.by|print\\\\\\\\.condition|print\\\\\\\\.connection|print\\\\\\\\.data\\\\\\\\.frame|print\\\\\\\\.Date|print\\\\\\\\.default|print\\\\\\\\.difftime|print\\\\\\\\.Dlist|print\\\\\\\\.DLLInfo|print\\\\\\\\.DLLInfoList|print\\\\\\\\.DLLRegisteredRoutines|print\\\\\\\\.eigen|print\\\\\\\\.factor|print\\\\\\\\.function|print\\\\\\\\.hexmode|print\\\\\\\\.libraryIQR|print\\\\\\\\.listof|print\\\\\\\\.NativeRoutineList|print\\\\\\\\.noquote|print\\\\\\\\.numeric_version|print\\\\\\\\.octmode|print\\\\\\\\.packageInfo|print\\\\\\\\.POSIXct|print\\\\\\\\.POSIXlt|print\\\\\\\\.proc_time|print\\\\\\\\.restart|print\\\\\\\\.rle|print\\\\\\\\.simple\\\\\\\\.list|print\\\\\\\\.srcfile|print\\\\\\\\.srcref|print\\\\\\\\.summaryDefault|print\\\\\\\\.summary\\\\\\\\.table|print\\\\\\\\.summary\\\\\\\\.warnings|print\\\\\\\\.table|print\\\\\\\\.warnings|prmatrix|proc\\\\\\\\.time|prod|proportions|prop\\\\\\\\.table|provideDimnames|psigamma|pushBack|pushBackLength|q|qr|qr\\\\\\\\.coef|qr\\\\\\\\.default|qr\\\\\\\\.fitted|qr\\\\\\\\.Q|qr\\\\\\\\.qty|qr\\\\\\\\.qy|qr\\\\\\\\.R|qr\\\\\\\\.resid|qr\\\\\\\\.solve|qr\\\\\\\\.X|quarters|quarters\\\\\\\\.Date|quarters\\\\\\\\.POSIXt|quit|quote|range|range\\\\\\\\.default|rank|rapply|raw|rawConnection|rawConnectionValue|rawShift|rawToBits|rawToChar|rbind|rbind\\\\\\\\.data\\\\\\\\.frame|rcond|Re|readBin|readChar|read\\\\\\\\.dcf|readline|readLines|readRDS|readRenviron|Recall|Reduce|regexec|regexpr|reg\\\\\\\\.finalizer|registerS3method|registerS3methods|regmatches|remove|removeTaskCallback|rep|rep\\\\\\\\.Date|rep\\\\\\\\.difftime|repeat|rep\\\\\\\\.factor|rep\\\\\\\\.int|replace|rep_len|replicate|rep\\\\\\\\.numeric_version|rep\\\\\\\\.POSIXct|rep\\\\\\\\.POSIXlt|require|requireNamespace|restartDescription|restartFormals|retracemem|return|returnValue|rev|rev\\\\\\\\.default|R\\\\\\\\.home|rle|rm|RNGkind|RNGversion|round|round\\\\\\\\.Date|round\\\\\\\\.POSIXt|row|rowMeans|rownames|row\\\\\\\\.names|row\\\\\\\\.names\\\\\\\\.data\\\\\\\\.frame|row\\\\\\\\.names\\\\\\\\.default|rowsum|rowsum\\\\\\\\.data\\\\\\\\.frame|rowsum\\\\\\\\.default|rowSums|R_system_version|R\\\\\\\\.version|R\\\\\\\\.Version|R\\\\\\\\.version\\\\\\\\.string|sample|sample\\\\\\\\.int|sapply|save|save\\\\\\\\.image|saveRDS|scale|scale\\\\\\\\.default|scan|search|searchpaths|seek|seek\\\\\\\\.connection|seq|seq_along|seq\\\\\\\\.Date|seq\\\\\\\\.default|seq\\\\\\\\.int|seq_len|seq\\\\\\\\.POSIXt|sequence|sequence\\\\\\\\.default|serialize|serverSocket|setdiff|setequal|setHook|setNamespaceInfo|set\\\\\\\\.seed|setSessionTimeLimit|setTimeLimit|setwd|showConnections|shQuote|sign|signalCondition|signif|simpleCondition|simpleError|simpleMessage|simpleWarning|simplify2array|sin|single|sinh|sink|sink\\\\\\\\.number|sinpi|slice\\\\\\\\.index|socketAccept|socketConnection|socketSelect|socketTimeout|solve|solve\\\\\\\\.default|solve\\\\\\\\.qr|sort|sort\\\\\\\\.default|sort\\\\\\\\.int|sort\\\\\\\\.list|sort\\\\\\\\.POSIXlt|source|split|split\\\\\\\\.data\\\\\\\\.frame|split\\\\\\\\.Date|split\\\\\\\\.default|split\\\\\\\\.POSIXct|sprintf|sqrt|sQuote|srcfile|srcfilealias|srcfilecopy|srcref|standardGeneric|startsWith|stderr|stdin|stdout|stop|stopifnot|storage\\\\\\\\.mode|str2expression|str2lang|strftime|strptime|strrep|strsplit|strtoi|strtrim|structure|strwrap|sub|subset|subset\\\\\\\\.data\\\\\\\\.frame|subset\\\\\\\\.default|subset\\\\\\\\.matrix|substitute|substr|substring|sum|summary|summary\\\\\\\\.connection|summary\\\\\\\\.data\\\\\\\\.frame|Summary\\\\\\\\.data\\\\\\\\.frame|summary\\\\\\\\.Date|Summary\\\\\\\\.Date|summary\\\\\\\\.default|Summary\\\\\\\\.difftime|summary\\\\\\\\.factor|Summary\\\\\\\\.factor|summary\\\\\\\\.matrix|Summary\\\\\\\\.numeric_version|Summary\\\\\\\\.ordered|summary\\\\\\\\.POSIXct|Summary\\\\\\\\.POSIXct|summary\\\\\\\\.POSIXlt|Summary\\\\\\\\.POSIXlt|summary\\\\\\\\.proc_time|summary\\\\\\\\.srcfile|summary\\\\\\\\.srcref|summary\\\\\\\\.table|summary\\\\\\\\.warnings|suppressMessages|suppressPackageStartupMessages|suppressWarnings|suspendInterrupts|svd|sweep|switch|sys\\\\\\\\.call|sys\\\\\\\\.calls|Sys\\\\\\\\.chmod|Sys\\\\\\\\.Date|sys\\\\\\\\.frame|sys\\\\\\\\.frames|sys\\\\\\\\.function|Sys\\\\\\\\.getenv|Sys\\\\\\\\.getlocale|Sys\\\\\\\\.getpid|Sys\\\\\\\\.glob|Sys\\\\\\\\.info|sys\\\\\\\\.load\\\\\\\\.image|Sys\\\\\\\\.localeconv|sys\\\\\\\\.nframe|sys\\\\\\\\.on\\\\\\\\.exit|sys\\\\\\\\.parent|sys\\\\\\\\.parents|Sys\\\\\\\\.readlink|sys\\\\\\\\.save\\\\\\\\.image|Sys\\\\\\\\.setenv|Sys\\\\\\\\.setFileTime|Sys\\\\\\\\.setlocale|Sys\\\\\\\\.sleep|sys\\\\\\\\.source|sys\\\\\\\\.status|system|system2|system\\\\\\\\.file|system\\\\\\\\.time|Sys\\\\\\\\.time|Sys\\\\\\\\.timezone|Sys\\\\\\\\.umask|Sys\\\\\\\\.unsetenv|Sys\\\\\\\\.which|t|table|tabulate|tan|tanh|tanpi|tapply|taskCallbackManager|tcrossprod|t\\\\\\\\.data\\\\\\\\.frame|t\\\\\\\\.default|tempdir|tempfile|textConnection|textConnectionValue|tolower|topenv|toString|toString\\\\\\\\.default|toupper|trace|traceback|tracemem|tracingState|transform|transform\\\\\\\\.data\\\\\\\\.frame|transform\\\\\\\\.default|trigamma|trimws|trunc|truncate|truncate\\\\\\\\.connection|trunc\\\\\\\\.Date|trunc\\\\\\\\.POSIXt|try|tryCatch|tryInvokeRestart|typeof|unclass|undebug|union|unique|unique\\\\\\\\.array|unique\\\\\\\\.data\\\\\\\\.frame|unique\\\\\\\\.default|unique\\\\\\\\.matrix|unique\\\\\\\\.numeric_version|unique\\\\\\\\.POSIXlt|unique\\\\\\\\.warnings|units|units\\\\\\\\.difftime|unix\\\\\\\\.time|unlink|unlist|unloadNamespace|unlockBinding|unname|unserialize|unsplit|untrace|untracemem|unz|upper\\\\\\\\.tri|url|UseMethod|utf8ToInt|validEnc|validUTF8|vapply|vector|Vectorize|version|warning|warningCondition|warnings|weekdays|weekdays\\\\\\\\.Date|weekdays\\\\\\\\.POSIXt|which|which\\\\\\\\.max|which\\\\\\\\.min|while|with|withAutoprint|withCallingHandlers|with\\\\\\\\.default|within|within\\\\\\\\.data\\\\\\\\.frame|within\\\\\\\\.list|withRestarts|withVisible|write|writeBin|writeChar|write\\\\\\\\.dcf|writeLines|xor|xpdrows\\\\\\\\.data\\\\\\\\.frame|xtfrm|xtfrm\\\\\\\\.AsIs|xtfrm\\\\\\\\.data\\\\\\\\.frame|xtfrm\\\\\\\\.Date|xtfrm\\\\\\\\.default|xtfrm\\\\\\\\.difftime|xtfrm\\\\\\\\.factor|xtfrm\\\\\\\\.numeric_version|xtfrm\\\\\\\\.POSIXct|xtfrm\\\\\\\\.POSIXlt|xzfile|zapsmall)\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(abline|arrows|assocplot|axis|Axis|axis\\\\\\\\.Date|axis\\\\\\\\.POSIXct|axTicks|barplot|barplot\\\\\\\\.default|box|boxplot|boxplot\\\\\\\\.default|boxplot\\\\\\\\.matrix|bxp|cdplot|clip|close\\\\\\\\.screen|co\\\\\\\\.intervals|contour|contour\\\\\\\\.default|coplot|curve|dotchart|erase\\\\\\\\.screen|filled\\\\\\\\.contour|fourfoldplot|frame|grconvertX|grconvertY|grid|hist|hist\\\\\\\\.default|identify|image|image\\\\\\\\.default|layout|layout\\\\\\\\.show|lcm|legend|lines|lines\\\\\\\\.default|locator|matlines|matplot|matpoints|mosaicplot|mtext|pairs|pairs\\\\\\\\.default|panel\\\\\\\\.smooth|par|persp|pie|plot|plot\\\\\\\\.default|plot\\\\\\\\.design|plot\\\\\\\\.function|plot\\\\\\\\.new|plot\\\\\\\\.window|plot\\\\\\\\.xy|points|points\\\\\\\\.default|polygon|polypath|rasterImage|rect|rug|screen|segments|smoothScatter|spineplot|split\\\\\\\\.screen|stars|stem|strheight|stripchart|strwidth|sunflowerplot|symbols|text|text\\\\\\\\.default|title|xinch|xspline|xyinch|yinch)\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(adjustcolor|as\\\\\\\\.graphicsAnnot|as\\\\\\\\.raster|axisTicks|bitmap|blues9|bmp|boxplot\\\\\\\\.stats|cairo_pdf|cairo_ps|cairoSymbolFont|check\\\\\\\\.options|chull|CIDFont|cm|cm\\\\\\\\.colors|col2rgb|colorConverter|colorRamp|colorRampPalette|colors|colorspaces|colours|contourLines|convertColor|densCols|dev2bitmap|devAskNewPage|dev\\\\\\\\.capabilities|dev\\\\\\\\.capture|dev\\\\\\\\.control|dev\\\\\\\\.copy|dev\\\\\\\\.copy2eps|dev\\\\\\\\.copy2pdf|dev\\\\\\\\.cur|dev\\\\\\\\.flush|dev\\\\\\\\.hold|deviceIsInteractive|dev\\\\\\\\.interactive|dev\\\\\\\\.list|dev\\\\\\\\.new|dev\\\\\\\\.next|dev\\\\\\\\.off|dev\\\\\\\\.prev|dev\\\\\\\\.print|dev\\\\\\\\.set|dev\\\\\\\\.size|embedFonts|extendrange|getGraphicsEvent|getGraphicsEventEnv|graphics\\\\\\\\.off|gray|gray\\\\\\\\.colors|grey|grey\\\\\\\\.colors|grSoftVersion|hcl|hcl\\\\\\\\.colors|hcl\\\\\\\\.pals|heat\\\\\\\\.colors|Hershey|hsv|is\\\\\\\\.raster|jpeg|make\\\\\\\\.rgb|n2mfrow|nclass\\\\\\\\.FD|nclass\\\\\\\\.scott|nclass\\\\\\\\.Sturges|palette|palette\\\\\\\\.colors|palette\\\\\\\\.pals|pdf|pdfFonts|pdf\\\\\\\\.options|pictex|png|postscript|postscriptFonts|ps\\\\\\\\.options|quartz|quartzFont|quartzFonts|quartz\\\\\\\\.options|quartz\\\\\\\\.save|rainbow|recordGraphics|recordPlot|replayPlot|rgb|rgb2hsv|savePlot|setEPS|setGraphicsEventEnv|setGraphicsEventHandlers|setPS|svg|terrain\\\\\\\\.colors|tiff|topo\\\\\\\\.colors|trans3d|Type1Font|x11|X11|X11Font|X11Fonts|X11\\\\\\\\.options|xfig|xy\\\\\\\\.coords|xyTable|xyz\\\\\\\\.coords)\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(addNextMethod|allNames|Arith|as|asMethodDefinition|assignClassDef|assignMethodsMetaData|balanceMethodsList|cacheGenericsMetaData|cacheMetaData|cacheMethod|callGeneric|callNextMethod|canCoerce|cbind2|checkAtAssignment|checkSlotAssignment|classesToAM|classLabel|classMetaName|className|coerce|Compare|completeClassDefinition|completeExtends|completeSubclasses|Complex|conformMethod|defaultDumpName|defaultPrototype|doPrimitiveMethod|dumpMethod|dumpMethods|el|elNamed|empty\\\\\\\\.dump|emptyMethodsList|evalOnLoad|evalqOnLoad|evalSource|existsFunction|existsMethod|extends|externalRefMethod|finalDefaultMethod|findClass|findFunction|findMethod|findMethods|findMethodSignatures|findUnique|fixPre1\\\\\\\\.8|formalArgs|functionBody|generic\\\\\\\\.skeleton|getAllSuperClasses|getClass|getClassDef|getClasses|getDataPart|getFunction|getGeneric|getGenerics|getGroup|getGroupMembers|getLoadActions|getMethod|getMethods|getMethodsForDispatch|getMethodsMetaData|getPackageName|getRefClass|getSlots|getValidity|hasArg|hasLoadAction|hasMethod|hasMethods|implicitGeneric|inheritedSlotNames|initFieldArgs|initialize|initRefFields|insertClassMethods|insertMethod|insertSource|is|isClass|isClassDef|isClassUnion|isGeneric|isGrammarSymbol|isGroup|isRematched|isSealedClass|isSealedMethod|isVirtualClass|isXS3Class|kronecker|languageEl|linearizeMlist|listFromMethods|listFromMlist|loadMethod|Logic|makeClassRepresentation|makeExtends|makeGeneric|makeMethodsList|makePrototypeFromClassDef|makeStandardGeneric|matchSignature|Math|Math2|mergeMethods|metaNameUndo|MethodAddCoerce|methodSignatureMatrix|method\\\\\\\\.skeleton|MethodsList|MethodsListSelect|methodsPackageMetaName|missingArg|multipleClasses|new|newBasic|newClassRepresentation|newEmptyObject|Ops|packageSlot|possibleExtends|prohibitGeneric|promptClass|promptMethods|prototype|Quote|rbind2|reconcilePropertiesAndPrototype|registerImplicitGenerics|rematchDefinition|removeClass|removeGeneric|removeMethod|removeMethods|representation|requireMethods|resetClass|resetGeneric|S3Class|S3Part|sealClass|selectMethod|selectSuperClasses|setAs|setClass|setClassUnion|setDataPart|setGeneric|setGenericImplicit|setGroupGeneric|setIs|setLoadAction|setLoadActions|setMethod|setOldClass|setPackageName|setPrimitiveMethods|setRefClass|setReplaceMethod|setValidity|show|showClass|showDefault|showExtends|showMethods|showMlist|signature|SignatureMethod|sigToEnv|slot|slotNames|slotsFromS3|substituteDirect|substituteFunctionArgs|Summary|superClassDepth|testInheritedMethods|testVirtual|tryNew|unRematchDefinition|validObject|validSlotNames)\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(acf|acf2AR|add1|addmargins|add\\\\\\\\.scope|aggregate|aggregate\\\\\\\\.data\\\\\\\\.frame|aggregate\\\\\\\\.ts|AIC|alias|anova|ansari\\\\\\\\.test|aov|approx|approxfun|ar|ar\\\\\\\\.burg|arima|arima0|arima0\\\\\\\\.diag|arima\\\\\\\\.sim|ARMAacf|ARMAtoMA|ar\\\\\\\\.mle|ar\\\\\\\\.ols|ar\\\\\\\\.yw|as\\\\\\\\.dendrogram|as\\\\\\\\.dist|as\\\\\\\\.formula|as\\\\\\\\.hclust|asOneSidedFormula|as\\\\\\\\.stepfun|as\\\\\\\\.ts|ave|bandwidth\\\\\\\\.kernel|bartlett\\\\\\\\.test|BIC|binomial|binom\\\\\\\\.test|biplot|Box\\\\\\\\.test|bw\\\\\\\\.bcv|bw\\\\\\\\.nrd|bw\\\\\\\\.nrd0|bw\\\\\\\\.SJ|bw\\\\\\\\.ucv|C|cancor|case\\\\\\\\.names|ccf|chisq\\\\\\\\.test|cmdscale|coef|coefficients|complete\\\\\\\\.cases|confint|confint\\\\\\\\.default|confint\\\\\\\\.lm|constrOptim|contrasts|contr\\\\\\\\.helmert|contr\\\\\\\\.poly|contr\\\\\\\\.SAS|contr\\\\\\\\.sum|contr\\\\\\\\.treatment|convolve|cooks\\\\\\\\.distance|cophenetic|cor|cor\\\\\\\\.test|cov|cov2cor|covratio|cov\\\\\\\\.wt|cpgram|cutree|cycle|D|dbeta|dbinom|dcauchy|dchisq|decompose|delete\\\\\\\\.response|deltat|dendrapply|density|density\\\\\\\\.default|deriv|deriv3|deviance|dexp|df|DF2formula|dfbeta|dfbetas|dffits|df\\\\\\\\.kernel|df\\\\\\\\.residual|dgamma|dgeom|dhyper|diffinv|dist|dlnorm|dlogis|dmultinom|dnbinom|dnorm|dpois|drop1|drop\\\\\\\\.scope|drop\\\\\\\\.terms|dsignrank|dt|dummy\\\\\\\\.coef|dummy\\\\\\\\.coef\\\\\\\\.lm|dunif|dweibull|dwilcox|ecdf|eff\\\\\\\\.aovlist|effects|embed|end|estVar|expand\\\\\\\\.model\\\\\\\\.frame|extractAIC|factanal|factor\\\\\\\\.scope|family|fft|filter|fisher\\\\\\\\.test|fitted|fitted\\\\\\\\.values|fivenum|fligner\\\\\\\\.test|formula|frequency|friedman\\\\\\\\.test|ftable|Gamma|gaussian|get_all_vars|getCall|getInitial|glm|glm\\\\\\\\.control|glm\\\\\\\\.fit|hasTsp|hat|hatvalues|hclust|heatmap|HoltWinters|influence|influence\\\\\\\\.measures|integrate|interaction\\\\\\\\.plot|inverse\\\\\\\\.gaussian|IQR|is\\\\\\\\.empty\\\\\\\\.model|is\\\\\\\\.leaf|is\\\\\\\\.mts|isoreg|is\\\\\\\\.stepfun|is\\\\\\\\.ts|is\\\\\\\\.tskernel|KalmanForecast|KalmanLike|KalmanRun|KalmanSmooth|kernapply|kernel|kmeans|knots|kruskal\\\\\\\\.test|ksmooth|ks\\\\\\\\.test|lag|lag\\\\\\\\.plot|line|lm|lm\\\\\\\\.fit|lm\\\\\\\\.influence|lm\\\\\\\\.wfit|loadings|loess|loess\\\\\\\\.control|loess\\\\\\\\.smooth|logLik|loglin|lowess|ls\\\\\\\\.diag|lsfit|ls\\\\\\\\.print|mad|mahalanobis|makeARIMA|make\\\\\\\\.link|makepredictcall|manova|mantelhaen\\\\\\\\.test|mauchly\\\\\\\\.test|mcnemar\\\\\\\\.test|median|median\\\\\\\\.default|medpolish|model\\\\\\\\.extract|model\\\\\\\\.frame|model\\\\\\\\.frame\\\\\\\\.default|model\\\\\\\\.matrix|model\\\\\\\\.matrix\\\\\\\\.default|model\\\\\\\\.matrix\\\\\\\\.lm|model\\\\\\\\.offset|model\\\\\\\\.response|model\\\\\\\\.tables|model\\\\\\\\.weights|monthplot|mood\\\\\\\\.test|mvfft|na\\\\\\\\.action|na\\\\\\\\.contiguous|na\\\\\\\\.exclude|na\\\\\\\\.fail|na\\\\\\\\.omit|na\\\\\\\\.pass|napredict|naprint|naresid|nextn|nlm|nlminb|nls|nls\\\\\\\\.control|NLSstAsymptotic|NLSstClosestX|NLSstLfAsymptote|NLSstRtAsymptote|nobs|numericDeriv|offset|oneway\\\\\\\\.test|optim|optimHess|optimise|optimize|order\\\\\\\\.dendrogram|pacf|p\\\\\\\\.adjust|p\\\\\\\\.adjust\\\\\\\\.methods|Pair|pairwise\\\\\\\\.prop\\\\\\\\.test|pairwise\\\\\\\\.table|pairwise\\\\\\\\.t\\\\\\\\.test|pairwise\\\\\\\\.wilcox\\\\\\\\.test|pbeta|pbinom|pbirthday|pcauchy|pchisq|pexp|pf|pgamma|pgeom|phyper|plclust|plnorm|plogis|plot\\\\\\\\.ecdf|plot\\\\\\\\.spec\\\\\\\\.coherency|plot\\\\\\\\.spec\\\\\\\\.phase|plot\\\\\\\\.stepfun|plot\\\\\\\\.ts|pnbinom|pnorm|poisson|poisson\\\\\\\\.test|poly|polym|power|power\\\\\\\\.anova\\\\\\\\.test|power\\\\\\\\.prop\\\\\\\\.test|power\\\\\\\\.t\\\\\\\\.test|ppoints|ppois|ppr|PP\\\\\\\\.test|prcomp|predict|predict\\\\\\\\.glm|predict\\\\\\\\.lm|preplot|princomp|printCoefmat|profile|proj|promax|prop\\\\\\\\.test|prop\\\\\\\\.trend\\\\\\\\.test|psignrank|pt|ptukey|punif|pweibull|pwilcox|qbeta|qbinom|qbirthday|qcauchy|qchisq|qexp|qf|qgamma|qgeom|qhyper|qlnorm|qlogis|qnbinom|qnorm|qpois|qqline|qqnorm|qqplot|qsignrank|qt|qtukey|quade\\\\\\\\.test|quantile|quasi|quasibinomial|quasipoisson|qunif|qweibull|qwilcox|r2dtable|rbeta|rbinom|rcauchy|rchisq|read\\\\\\\\.ftable|rect\\\\\\\\.hclust|reformulate|relevel|reorder|replications|reshape|resid|residuals|residuals\\\\\\\\.glm|residuals\\\\\\\\.lm|rexp|rf|rgamma|rgeom|rhyper|rlnorm|rlogis|rmultinom|rnbinom|rnorm|rpois|rsignrank|rstandard|rstudent|rt|runif|runmed|rweibull|rwilcox|rWishart|scatter\\\\\\\\.smooth|screeplot|sd|se\\\\\\\\.contrast|selfStart|setNames|shapiro\\\\\\\\.test|sigma|simulate|smooth|smoothEnds|smooth\\\\\\\\.spline|sortedXyData|spec\\\\\\\\.ar|spec\\\\\\\\.pgram|spec\\\\\\\\.taper|spectrum|spline|splinefun|splinefunH|SSasymp|SSasympOff|SSasympOrig|SSbiexp|SSD|SSfol|SSfpl|SSgompertz|SSlogis|SSmicmen|SSweibull|start|stat\\\\\\\\.anova|step|stepfun|stl|StructTS|summary\\\\\\\\.aov|summary\\\\\\\\.glm|summary\\\\\\\\.lm|summary\\\\\\\\.manova|summary\\\\\\\\.stepfun|supsmu|symnum|termplot|terms|terms\\\\\\\\.formula|time|toeplitz|ts|tsdiag|ts\\\\\\\\.intersect|tsp|ts\\\\\\\\.plot|tsSmooth|ts\\\\\\\\.union|t\\\\\\\\.test|TukeyHSD|uniroot|update|update\\\\\\\\.default|update\\\\\\\\.formula|var|variable\\\\\\\\.names|varimax|var\\\\\\\\.test|vcov|weighted\\\\\\\\.mean|weighted\\\\\\\\.residuals|weights|wilcox\\\\\\\\.test|window|write\\\\\\\\.ftable|xtabs)\\\\\\\\s*(\\\\\\\\()\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.r\\\"}},\\\"match\\\":\\\"\\\\\\\\b(adist|alarm|apropos|aregexec|argsAnywhere|asDateBuilt|askYesNo|aspell|aspell_package_C_files|aspell_package_Rd_files|aspell_package_R_files|aspell_package_vignettes|aspell_write_personal_dictionary_file|as\\\\\\\\.person|as\\\\\\\\.personList|as\\\\\\\\.relistable|as\\\\\\\\.roman|assignInMyNamespace|assignInNamespace|available\\\\\\\\.packages|bibentry|browseEnv|browseURL|browseVignettes|bug\\\\\\\\.report|capture\\\\\\\\.output|changedFiles|charClass|checkCRAN|chooseBioCmirror|chooseCRANmirror|citation|cite|citeNatbib|citEntry|citFooter|citHeader|close\\\\\\\\.socket|combn|compareVersion|contrib\\\\\\\\.url|count\\\\\\\\.fields|create\\\\\\\\.post|data|dataentry|data\\\\\\\\.entry|de|debugcall|debugger|demo|de\\\\\\\\.ncols|de\\\\\\\\.restore|de\\\\\\\\.setup|download\\\\\\\\.file|download\\\\\\\\.packages|dump\\\\\\\\.frames|edit|emacs|example|file\\\\\\\\.edit|fileSnapshot|file_test|find|findLineNum|fix|fixInNamespace|flush\\\\\\\\.console|formatOL|formatUL|getAnywhere|getCRANmirrors|getFromNamespace|getParseData|getParseText|getS3method|getSrcDirectory|getSrcFilename|getSrcLocation|getSrcref|getTxtProgressBar|glob2rx|globalVariables|hasName|head|head\\\\\\\\.matrix|help|help\\\\\\\\.request|help\\\\\\\\.search|help\\\\\\\\.start|history|hsearch_db|hsearch_db_concepts|hsearch_db_keywords|installed\\\\\\\\.packages|install\\\\\\\\.packages|is\\\\\\\\.relistable|isS3method|isS3stdGeneric|limitedLabels|loadhistory|localeToCharset|lsf\\\\\\\\.str|ls\\\\\\\\.str|maintainer|make\\\\\\\\.packages\\\\\\\\.html|makeRweaveLatexCodeRunner|make\\\\\\\\.socket|memory\\\\\\\\.limit|memory\\\\\\\\.size|menu|methods|mirror2html|modifyList|new\\\\\\\\.packages|news|nsl|object\\\\\\\\.size|old\\\\\\\\.packages|osVersion|packageDate|packageDescription|packageName|package\\\\\\\\.skeleton|packageStatus|packageVersion|page|person|personList|pico|process\\\\\\\\.events|prompt|promptData|promptImport|promptPackage|rc\\\\\\\\.getOption|rc\\\\\\\\.options|rc\\\\\\\\.settings|rc\\\\\\\\.status|readCitationFile|read\\\\\\\\.csv|read\\\\\\\\.csv2|read\\\\\\\\.delim|read\\\\\\\\.delim2|read\\\\\\\\.DIF|read\\\\\\\\.fortran|read\\\\\\\\.fwf|read\\\\\\\\.socket|read\\\\\\\\.table|recover|relist|remove\\\\\\\\.packages|removeSource|Rprof|Rprofmem|RShowDoc|RSiteSearch|rtags|Rtangle|RtangleFinish|RtangleRuncode|RtangleSetup|RtangleWritedoc|RweaveChunkPrefix|RweaveEvalWithOpt|RweaveLatex|RweaveLatexFinish|RweaveLatexOptions|RweaveLatexSetup|RweaveLatexWritedoc|RweaveTryStop|savehistory|select\\\\\\\\.list|sessionInfo|setBreakpoint|setRepositories|setTxtProgressBar|stack|Stangle|str|strcapture|strOptions|summaryRprof|suppressForeignCheck|Sweave|SweaveHooks|SweaveSyntaxLatex|SweaveSyntaxNoweb|SweaveSyntConv|tail|tail\\\\\\\\.matrix|tar|timestamp|toBibtex|toLatex|txtProgressBar|type\\\\\\\\.convert|undebugcall|unstack|untar|unzip|update\\\\\\\\.packages|upgrade|URLdecode|URLencode|url\\\\\\\\.show|vi|View|vignette|warnErrList|write\\\\\\\\.csv|write\\\\\\\\.csv2|write\\\\\\\\.socket|write\\\\\\\\.table|xedit|xemacs|zip)\\\\\\\\s*(\\\\\\\\()\\\"}]},\\\"comments\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"comment.line.pragma.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.name.pragma.name.r\\\"}},\\\"match\\\":\\\"^(#pragma[ \\\\\\\\t]+mark)[ \\\\\\\\t](.*)\\\",\\\"name\\\":\\\"comment.line.pragma-mark.r\\\"},{\\\"begin\\\":\\\"(^[ \\\\\\\\t]+)?(?=#)\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.r\\\"}},\\\"end\\\":\\\"(?!\\\\\\\\G)\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.r\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.r\\\"}]}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(pi|letters|LETTERS|month\\\\\\\\.abb|month\\\\\\\\.name)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.misc.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b(TRUE|FALSE|NULL|NA|NA_integer_|NA_real_|NA_complex_|NA_character_|Inf|NaN)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0(x|X)[0-9a-fA-F]+i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.?[0-9]*(?:(e|E)(\\\\\\\\+|-)?[0-9]+)?i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\.[0-9]+(?:(e|E)(\\\\\\\\+|-)?[0-9]+)?i\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.imaginary.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0(x|X)[0-9a-fA-F]+L\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?:[0-9]+\\\\\\\\.?[0-9]*)(?:(e|E)(\\\\\\\\+|-)?[0-9]+)?L\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.integer.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b0(x|X)[0-9a-fA-F]+\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.hexadecimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.?[0-9]*(?:(e|E)(\\\\\\\\+|-)?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.decimal.r\\\"},{\\\"match\\\":\\\"\\\\\\\\.[0-9]+(?:(e|E)(\\\\\\\\+|-)?[0-9]+)?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.float.decimal.r\\\"}]},\\\"function-calls\\\":{\\\"begin\\\":\\\"(?:\\\\\\\\b|(?=\\\\\\\\.))((?:[a-zA-Z._][\\\\\\\\w.]*|`[^`]+`))\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.function.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function-call.arguments.r\\\",\\\"end\\\":\\\"(\\\\\\\\))\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.r\\\"}},\\\"name\\\":\\\"meta.function-call.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#function-parameters\\\"}]},\\\"function-declarations\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.control.r\\\"}},\\\"match\\\":\\\"((?:`[^`\\\\\\\\\\\\\\\\]*(?:\\\\\\\\\\\\\\\\.[^`\\\\\\\\\\\\\\\\]*)*`)|(?:[[:alpha:].][[:alnum:]._]*))\\\\\\\\s*(<?<-|=(?!=))\\\\\\\\s*(function|\\\\\\\\\\\\\\\\)(?!\\\\\\\\w)\\\",\\\"name\\\":\\\"meta.function.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#lambda-functions\\\"}]}]},\\\"function-parameters\\\":{\\\"patterns\\\":[{\\\"contentName\\\":\\\"meta.function-call.parameters.r\\\",\\\"name\\\":\\\"meta.function-call.r\\\"},{\\\"match\\\":\\\"(?:[a-zA-Z._][\\\\\\\\w.]*|`[^`]+`)(?=\\\\\\\\s[^=])\\\",\\\"name\\\":\\\"variable.other.r\\\"},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.r\\\"},{\\\"include\\\":\\\"source.r\\\"}]},\\\"general-variables\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.r\\\"}},\\\"match\\\":\\\"([[:alpha:].][[:alnum:]._]*)\\\\\\\\s*(=)(?=[^=])\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"variable.parameter.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.assignment.r\\\"}},\\\"match\\\":\\\"(`[^`]+`)\\\\\\\\s*(=)(?=[^=])\\\"},{\\\"match\\\":\\\"\\\\\\\\b([\\\\\\\\d_][[:alnum:]._]+)\\\\\\\\b\\\",\\\"name\\\":\\\"invalid.illegal.variable.other.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b([[:alnum:]_]+)(?=::)\\\",\\\"name\\\":\\\"entity.namespace.r\\\"}]},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(break|next|repeat|else|in)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.r\\\"},{\\\"match\\\":\\\"\\\\\\\\b(ifelse|if|for|return|switch|while|invisible)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"keyword.control.r\\\"},{\\\"match\\\":\\\"(\\\\\\\\-|\\\\\\\\+|\\\\\\\\*|\\\\\\\\/|%\\\\\\\\/%|%%|%\\\\\\\\*%|%o%|%x%|\\\\\\\\^)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.r\\\"},{\\\"match\\\":\\\"(:=|<-|<<-|->|->>)\\\",\\\"name\\\":\\\"keyword.operator.assignment.r\\\"},{\\\"match\\\":\\\"(==|<=|>=|!=|<>|<|>|%in%)\\\",\\\"name\\\":\\\"keyword.operator.comparison.r\\\"},{\\\"match\\\":\\\"(!|&{1,2}|[|]{1,2})\\\",\\\"name\\\":\\\"keyword.operator.logical.r\\\"},{\\\"match\\\":\\\"(\\\\\\\\|>)\\\",\\\"name\\\":\\\"keyword.operator.pipe.r\\\"},{\\\"match\\\":\\\"(%between%|%chin%|%like%|%\\\\\\\\+%|%\\\\\\\\+replace%|%:%|%do%|%dopar%|%>%|%<>%|%T>%|%\\\\\\\\$%)\\\",\\\"name\\\":\\\"keyword.operator.other.r\\\"},{\\\"match\\\":\\\"(\\\\\\\\.\\\\\\\\.\\\\\\\\.|\\\\\\\\$|:|\\\\\\\\~|@)\\\",\\\"name\\\":\\\"keyword.other.r\\\"}]},\\\"lambda-functions\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(function)\\\\\\\\s*(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.section.parens.begin.r\\\"}},\\\"contentName\\\":\\\"meta.function.parameters.r\\\",\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.section.parens.end.r\\\"}},\\\"name\\\":\\\"meta.function.r\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comments\\\"},{\\\"match\\\":\\\"(?:[a-zA-Z._][\\\\\\\\w.]*|`[^`]+`)\\\",\\\"name\\\":\\\"variable.other.r\\\"},{\\\"begin\\\":\\\"(?==)\\\",\\\"end\\\":\\\"(?=[,)])\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"}]},{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.parameters.r\\\"}]}]},\\\"roxygen\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*(#')\\\\\\\\s*\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.comment.r\\\"}},\\\"end\\\":\\\"$\\\\\\\\n?\\\",\\\"name\\\":\\\"comment.line.roxygen.r\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.r\\\"},\\\"2\\\":{\\\"name\\\":\\\"variable.parameter.r\\\"}},\\\"match\\\":\\\"(@param)\\\\\\\\s*((?:[a-zA-Z._][\\\\\\\\w.]*|`[^`]+`))\\\"},{\\\"match\\\":\\\"@[a-zA-Z0-9]+\\\",\\\"name\\\":\\\"keyword.other.r\\\"}]}]},\\\"storage-type\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(character|complex|double|expression|integer|list|logical|numeric|single|raw)\\\\\\\\b(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"storage.type.r\\\"}]},\\\"strings\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"[rR]\\\\\\\"(-*)\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[rR]'(-*)\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\]\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"[rR]\\\\\\\"(-*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[rR]'(-*)\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\}\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"[rR]\\\\\\\"(-*)\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\\\\\\1\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.raw.r\\\"},{\\\"begin\\\":\\\"[rR]'(-*)\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\\\\\\1'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.raw.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.raw.r\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.r\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.r\\\"}},\\\"name\\\":\\\"string.quoted.double.r\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.r\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.r\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.r\\\"}},\\\"name\\\":\\\"string.quoted.single.r\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.r\\\"}]}]}},\\\"scopeName\\\":\\\"source.r\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/Querryone%20%285%29/Querryone/node_modules/%40shikijs/langs/dist/julia.mjs"], "sourcesContent": ["import cpp from './cpp.mjs'\nimport python from './python.mjs'\nimport javascript from './javascript.mjs'\nimport r from './r.mjs'\nimport sql from './sql.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Julia\\\",\\\"name\\\":\\\"julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#bracket\\\"},{\\\"include\\\":\\\"#function_decl\\\"},{\\\"include\\\":\\\"#function_call\\\"},{\\\"include\\\":\\\"#for_block\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type_decl\\\"},{\\\"include\\\":\\\"#symbol\\\"},{\\\"include\\\":\\\"#punctuation\\\"}],\\\"repository\\\":{\\\"array\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\[\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\\])((?:\\\\\\\\.)?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"name\\\":\\\"meta.array.julia\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bbegin\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bend\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.julia\\\"},{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"bracket\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\{\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\\})((?:\\\\\\\\.)?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_block\\\"},{\\\"begin\\\":\\\"#\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\n\\\",\\\"name\\\":\\\"comment.line.number-sign.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_tags\\\"}]}]},\\\"comment_block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"#=\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.begin.julia\\\"}},\\\"end\\\":\\\"=#\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.end.julia\\\"}},\\\"name\\\":\\\"comment.block.number-sign-equals.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment_tags\\\"},{\\\"include\\\":\\\"#comment_block\\\"}]}]},\\\"comment_tags\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bTODO\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bFIXME\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bCHANGED\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\bXXX\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.comment-annotation.julia\\\"}]},\\\"for_block\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\b(for)\\\\\\\\b\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"keyword.control.julia\\\"}},\\\"end\\\":\\\"(?<!,|\\\\\\\\s)(\\\\\\\\s*\\\\\\\\n)\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\bouter\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.julia\\\"},{\\\"include\\\":\\\"$self\\\"}]}]},\\\"function_call\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?\\\\\\\\.?(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\)(('|(\\\\\\\\.'))*\\\\\\\\.?')?\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.transposed-func.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"function_decl\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.function.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"}},\\\"match\\\":\\\"((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?(?=\\\\\\\\([^#]*\\\\\\\\)(::[^\\\\\\\\s]+)?(\\\\\\\\s*\\\\\\\\bwhere\\\\\\\\b\\\\\\\\s+.+?)?\\\\\\\\s*?=(?![=>]))\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.dots.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"entity.name.function.julia\\\"},\\\"4\\\":{\\\"name\\\":\\\"support.type.julia\\\"}},\\\"match\\\":\\\"\\\\\\\\b(function|macro)(?:\\\\\\\\s+(?:(?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*(\\\\\\\\.))?((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)({(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})?|\\\\\\\\s*)(?=\\\\\\\\()\\\"}]},\\\"keyword\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\b(?<![:_\\\\\\\\.])(?:function|mutable\\\\\\\\s+struct|struct|macro|quote|abstract\\\\\\\\s+type|primitive\\\\\\\\s+type|module|baremodule|where)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.other.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:if|else|elseif|for|while|begin|let|do|try|catch|finally|return|break|continue)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])end\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.end.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:global|local|const)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.storage.modifier.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:export)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.export.julia\\\"},{\\\"match\\\":\\\"^(?:public)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.public.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:import)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.import.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?<![:_])(?:using)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.using.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\S\\\\\\\\s+)\\\\\\\\b(as)\\\\\\\\b(?=\\\\\\\\s+\\\\\\\\S)\\\",\\\"name\\\":\\\"keyword.control.as.julia\\\"},{\\\"match\\\":\\\"(@(\\\\\\\\.|(?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*))\\\",\\\"name\\\":\\\"support.function.macro.julia\\\"}]},\\\"number\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"constant.numeric.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.conjugate-number.julia\\\"}},\\\"match\\\":\\\"((?<!(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿]))(?:(?:\\\\\\\\b0(?:x|X)[0-9a-fA-F](?:_?[0-9a-fA-F])*)|(?:\\\\\\\\b0o[0-7](?:_?[0-7])*)|(?:\\\\\\\\b0b[0-1](?:_?[0-1])*)|(?:(?:\\\\\\\\b[0-9](?:_?[0-9])*\\\\\\\\.?(?!\\\\\\\\.)(?:[_0-9]*))|(?:\\\\\\\\b\\\\\\\\.[0-9](?:_?[0-9])*))(?:[efE][+-]?[0-9](?:_?[0-9])*)?(?:im\\\\\\\\b|Inf(?:16|32|64)?\\\\\\\\b|NaN(?:16|32|64)?\\\\\\\\b|π\\\\\\\\b|pi\\\\\\\\b|ℯ\\\\\\\\b)?|\\\\\\\\b[0-9]+|\\\\\\\\bInf(?:16|32|64)?\\\\\\\\b|\\\\\\\\bNaN(?:16|32|64)?\\\\\\\\b|\\\\\\\\bπ\\\\\\\\b|\\\\\\\\bpi\\\\\\\\b|\\\\\\\\bℯ\\\\\\\\b))('*)\\\"},{\\\"match\\\":\\\"\\\\\\\\bARGS\\\\\\\\b|\\\\\\\\bC_NULL\\\\\\\\b|\\\\\\\\bDEPOT_PATH\\\\\\\\b|\\\\\\\\bENDIAN_BOM\\\\\\\\b|\\\\\\\\bENV\\\\\\\\b|\\\\\\\\bLOAD_PATH\\\\\\\\b|\\\\\\\\bPROGRAM_FILE\\\\\\\\b|\\\\\\\\bstdin\\\\\\\\b|\\\\\\\\bstdout\\\\\\\\b|\\\\\\\\bstderr\\\\\\\\b|\\\\\\\\bVERSION\\\\\\\\b|\\\\\\\\bdevnull\\\\\\\\b\\\",\\\"name\\\":\\\"constant.global.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\btrue\\\\\\\\b|\\\\\\\\bfalse\\\\\\\\b|\\\\\\\\bnothing\\\\\\\\b|\\\\\\\\bmissing\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.julia\\\"}]},\\\"operator\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\.?(?:<-->|->|-->|<--|←|→|↔|↚|↛|↞|↠|↢|↣|↦|↤|↮|⇎|⇍|⇏|⇐|⇒|⇔|⇴|⇶|⇷|⇸|⇹|⇺|⇻|⇼|⇽|⇾|⇿|⟵|⟶|⟷|⟹|⟺|⟻|⟼|⟽|⟾|⟿|⤀|⤁|⤂|⤃|⤄|⤅|⤆|⤇|⤌|⤍|⤎|⤏|⤐|⤑|⤔|⤕|⤖|⤗|⤘|⤝|⤞|⤟|⤠|⥄|⥅|⥆|⥇|⥈|⥊|⥋|⥎|⥐|⥒|⥓|⥖|⥗|⥚|⥛|⥞|⥟|⥢|⥤|⥦|⥧|⥨|⥩|⥪|⥫|⥬|⥭|⥰|⧴|⬱|⬰|⬲|⬳|⬴|⬵|⬶|⬷|⬸|⬹|⬺|⬻|⬼|⬽|⬾|⬿|⭀|⭁|⭂|⭃|⥷|⭄|⥺|⭇|⭈|⭉|⭊|⭋|⭌|￩|￫|⇜|⇝|↜|↝|↩|↪|↫|↬|↼|↽|⇀|⇁|⇄|⇆|⇇|⇉|⇋|⇌|⇚|⇛|⇠|⇢|↷|↶|↺|↻|=>)\\\",\\\"name\\\":\\\"keyword.operator.arrow.julia\\\"},{\\\"match\\\":\\\"(?::=|\\\\\\\\+=|-=|\\\\\\\\*=|//=|/=|\\\\\\\\.//=|\\\\\\\\./=|\\\\\\\\.\\\\\\\\*=|\\\\\\\\\\\\\\\\=|\\\\\\\\.\\\\\\\\\\\\\\\\=|\\\\\\\\^=|\\\\\\\\.\\\\\\\\^=|%=|\\\\\\\\.%=|÷=|\\\\\\\\.÷=|\\\\\\\\|=|&=|\\\\\\\\.&=|⊻=|\\\\\\\\.⊻=|\\\\\\\\$=|<<=|>>=|>>>=|=(?!=))\\\",\\\"name\\\":\\\"keyword.operator.update.julia\\\"},{\\\"match\\\":\\\"(?:<<|>>>|>>|\\\\\\\\.>>>|\\\\\\\\.>>|\\\\\\\\.<<)\\\",\\\"name\\\":\\\"keyword.operator.shift.julia\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.relation.types.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.type.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"match\\\":\\\"(?:\\\\\\\\s*(::|>:|<:)\\\\\\\\s*((?:(?:Union)?\\\\\\\\([^)]*\\\\\\\\)|[[:alpha:]_$∇][[:word:]⁺-ₜ!′\\\\\\\\.]*(?:(?:{(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*})|(?:\\\\\\\".+?(?<!\\\\\\\\\\\\\\\\)\\\\\\\"))?)))(?:\\\\\\\\.\\\\\\\\.\\\\\\\\.)?((?:\\\\\\\\.)?'*)\\\"},{\\\"match\\\":\\\"(\\\\\\\\.?((?<!<)<=|(?<!>)>=|>|<|≥|≤|===|==|≡|!=|≠|!==|≢|∈|∉|∋|∌|⊆|⊈|⊂|⊄|⊊|∝|∊|∍|∥|∦|∷|∺|∻|∽|∾|≁|≃|≂|≄|≅|≆|≇|≈|≉|≊|≋|≌|≍|≎|≐|≑|≒|≓|≖|≗|≘|≙|≚|≛|≜|≝|≞|≟|≣|≦|≧|≨|≩|≪|≫|≬|≭|≮|≯|≰|≱|≲|≳|≴|≵|≶|≷|≸|≹|≺|≻|≼|≽|≾|≿|⊀|⊁|⊃|⊅|⊇|⊉|⊋|⊏|⊐|⊑|⊒|⊜|⊩|⊬|⊮|⊰|⊱|⊲|⊳|⊴|⊵|⊶|⊷|⋍|⋐|⋑|⋕|⋖|⋗|⋘|⋙|⋚|⋛|⋜|⋝|⋞|⋟|⋠|⋡|⋢|⋣|⋤|⋥|⋦|⋧|⋨|⋩|⋪|⋫|⋬|⋭|⋲|⋳|⋴|⋵|⋶|⋷|⋸|⋹|⋺|⋻|⋼|⋽|⋾|⋿|⟈|⟉|⟒|⦷|⧀|⧁|⧡|⧣|⧤|⧥|⩦|⩧|⩪|⩫|⩬|⩭|⩮|⩯|⩰|⩱|⩲|⩳|⩵|⩶|⩷|⩸|⩹|⩺|⩻|⩼|⩽|⩾|⩿|⪀|⪁|⪂|⪃|⪄|⪅|⪆|⪇|⪈|⪉|⪊|⪋|⪌|⪍|⪎|⪏|⪐|⪑|⪒|⪓|⪔|⪕|⪖|⪗|⪘|⪙|⪚|⪛|⪜|⪝|⪞|⪟|⪠|⪡|⪢|⪣|⪤|⪥|⪦|⪧|⪨|⪩|⪪|⪫|⪬|⪭|⪮|⪯|⪰|⪱|⪲|⪳|⪴|⪵|⪶|⪷|⪸|⪹|⪺|⪻|⪼|⪽|⪾|⪿|⫀|⫁|⫂|⫃|⫄|⫅|⫆|⫇|⫈|⫉|⫊|⫋|⫌|⫍|⫎|⫏|⫐|⫑|⫒|⫓|⫔|⫕|⫖|⫗|⫘|⫙|⫷|⫸|⫹|⫺|⊢|⊣|⟂|⫪|⫫|<:|>:))\\\",\\\"name\\\":\\\"keyword.operator.relation.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)(?:\\\\\\\\?)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.ternary.julia\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\s)(?:\\\\\\\\:)(?=\\\\\\\\s)\\\",\\\"name\\\":\\\"keyword.operator.ternary.julia\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\|\\\\\\\\||&&|(?<!(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿]))!)\\\",\\\"name\\\":\\\"keyword.operator.boolean.julia\\\"},{\\\"match\\\":\\\"(?<=[[:word:]⁺-ₜ!′∇\\\\\\\\)\\\\\\\\]\\\\\\\\}])(?::)\\\",\\\"name\\\":\\\"keyword.operator.range.julia\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\|>)\\\",\\\"name\\\":\\\"keyword.operator.applies.julia\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\||\\\\\\\\.\\\\\\\\||\\\\\\\\&|\\\\\\\\.\\\\\\\\&|~|¬|\\\\\\\\.~|⊻|\\\\\\\\.⊻)\\\",\\\"name\\\":\\\"keyword.operator.bitwise.julia\\\"},{\\\"match\\\":\\\"\\\\\\\\.?(?:\\\\\\\\+\\\\\\\\+|\\\\\\\\-\\\\\\\\-|\\\\\\\\+|\\\\\\\\-|−|¦|\\\\\\\\||⊕|⊖|⊞|⊟|∪|∨|⊔|±|∓|∔|∸|≏|⊎|⊻|⊽|⋎|⋓|⟇|⧺|⧻|⨈|⨢|⨣|⨤|⨥|⨦|⨧|⨨|⨩|⨪|⨫|⨬|⨭|⨮|⨹|⨺|⩁|⩂|⩅|⩊|⩌|⩏|⩐|⩒|⩔|⩖|⩗|⩛|⩝|⩡|⩢|⩣|\\\\\\\\*|//?|⌿|÷|%|&|·|·|⋅|∘|×|\\\\\\\\\\\\\\\\|∩|∧|⊗|⊘|⊙|⊚|⊛|⊠|⊡|⊓|∗|∙|∤|⅋|≀|⊼|⋄|⋆|⋇|⋉|⋊|⋋|⋌|⋏|⋒|⟑|⦸|⦼|⦾|⦿|⧶|⧷|⨇|⨰|⨱|⨲|⨳|⨴|⨵|⨶|⨷|⨸|⨻|⨼|⨽|⩀|⩃|⩄|⩋|⩍|⩎|⩑|⩓|⩕|⩘|⩚|⩜|⩞|⩟|⩠|⫛|⊍|▷|⨝|⟕|⟖|⟗|⨟|\\\\\\\\^|↑|↓|⇵|⟰|⟱|⤈|⤉|⤊|⤋|⤒|⤓|⥉|⥌|⥍|⥏|⥑|⥔|⥕|⥘|⥙|⥜|⥝|⥠|⥡|⥣|⥥|⥮|⥯|￪|￬|√|∛|∜|⋆|±|∓)\\\",\\\"name\\\":\\\"keyword.operator.arithmetic.julia\\\"},{\\\"match\\\":\\\"(?:∘)\\\",\\\"name\\\":\\\"keyword.operator.compose.julia\\\"},{\\\"match\\\":\\\"(?:::|(?<=\\\\\\\\s)isa(?=\\\\\\\\s))\\\",\\\"name\\\":\\\"keyword.operator.isa.julia\\\"},{\\\"match\\\":\\\"(?:(?<=\\\\\\\\s)in(?=\\\\\\\\s))\\\",\\\"name\\\":\\\"keyword.operator.relation.in.julia\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\.(?=(?:@|_|\\\\\\\\p{L}))|\\\\\\\\.\\\\\\\\.+|…|⁝|⋮|⋱|⋰|⋯)\\\",\\\"name\\\":\\\"keyword.operator.dots.julia\\\"},{\\\"match\\\":\\\"(?:\\\\\\\\$)(?=.+)\\\",\\\"name\\\":\\\"keyword.operator.interpolation.julia\\\"},{\\\"captures\\\":{\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-variable.julia\\\"}},\\\"match\\\":\\\"((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)(('|(\\\\\\\\.'))*\\\\\\\\.?')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"bracket.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-matrix.julia\\\"}},\\\"match\\\":\\\"(\\\\\\\\])((?:'|(?:\\\\\\\\.'))*\\\\\\\\.?')\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"bracket.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transposed-parens.julia\\\"}},\\\"match\\\":\\\"(\\\\\\\\))((?:'|(?:\\\\\\\\.'))*\\\\\\\\.?')\\\"}]},\\\"parentheses\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"\\\\\\\\(\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\\))((?:\\\\\\\\.)?'*)\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.transpose.julia\\\"}},\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"punctuation\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\",\\\",\\\"name\\\":\\\"punctuation.separator.comma.julia\\\"},{\\\"match\\\":\\\";\\\",\\\"name\\\":\\\"punctuation.separator.semicolon.julia\\\"}]},\\\"self_no_for_block\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#operator\\\"},{\\\"include\\\":\\\"#array\\\"},{\\\"include\\\":\\\"#string\\\"},{\\\"include\\\":\\\"#parentheses\\\"},{\\\"include\\\":\\\"#bracket\\\"},{\\\"include\\\":\\\"#function_decl\\\"},{\\\"include\\\":\\\"#function_call\\\"},{\\\"include\\\":\\\"#keyword\\\"},{\\\"include\\\":\\\"#number\\\"},{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#type_decl\\\"},{\\\"include\\\":\\\"#symbol\\\"},{\\\"include\\\":\\\"#punctuation\\\"}]},\\\"string\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"(?:(@doc)\\\\\\\\s((?:doc)?\\\\\\\"\\\\\\\"\\\\\\\")|(doc\\\\\\\"\\\\\\\"\\\\\\\"))\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\") ?(->)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.operator.arrow.julia\\\"}},\\\"name\\\":\\\"string.docstring.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(i?cxx)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.cpp\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.cxx.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.cpp#root_context\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(py)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.python\\\",\\\"end\\\":\\\"([\\\\\\\\s\\\\\\\\w]*)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.python.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.python\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(js)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.javascript\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.js.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.js\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(R)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.r\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.R.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.r\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(raw)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(raw)(\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(sql)(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"contentName\\\":\\\"meta.embedded.inline.sql\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"embed.sql.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.sql\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"var\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"var\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"^\\\\\\\\s?(doc)?(\\\\\\\"\\\\\\\"\\\\\\\")\\\\\\\\s?$\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.docstring.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"'(?!')\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.single.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.multiline.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.multiline.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.triple.double.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"\\\\\\\"(?!\\\\\\\"\\\\\\\")\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"}},\\\"name\\\":\\\"string.quoted.double.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"r\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")([imsx]{0,4})?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.option-toggle.regexp.julia\\\"}},\\\"name\\\":\\\"string.regexp.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"r\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.begin.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\")([imsx]{0,4})?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.regexp.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.option-toggle.regexp.julia\\\"}},\\\"name\\\":\\\"string.regexp.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\")((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)\\\\\\\"\\\\\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(\\\\\\\"\\\\\\\"\\\\\\\")((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!\\\\\\\")((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(?<![^\\\\\\\\\\\\\\\\]\\\\\\\\\\\\\\\\)(\\\\\\\")((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.quoted.other.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"}]},{\\\"begin\\\":\\\"(?<!`)((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?```\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(```)((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.interpolated.backtick.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]},{\\\"begin\\\":\\\"(?<!`)((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?`\\\",\\\"beginCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.julia\\\"},\\\"1\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"end\\\":\\\"(?<![^\\\\\\\\\\\\\\\\]\\\\\\\\\\\\\\\\)(`)((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)?\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"support.function.macro.julia\\\"}},\\\"name\\\":\\\"string.interpolated.backtick.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string_escaped_char\\\"},{\\\"include\\\":\\\"#string_dollar_sign_interpolate\\\"}]}]},\\\"string_dollar_sign_interpolate\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\$(?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿]|[^\\\\\\\\p{^Sc}$])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿]|[^\\\\\\\\p{^Sc}$])*\\\",\\\"name\\\":\\\"variable.interpolation.julia\\\"},{\\\"begin\\\":\\\"\\\\\\\\$(\\\\\\\\()\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"end\\\":\\\"\\\\\\\\)\\\",\\\"endCaptures\\\":{\\\"0\\\":{\\\"name\\\":\\\"meta.bracket.julia\\\"}},\\\"name\\\":\\\"variable.interpolation.julia\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#self_no_for_block\\\"}]}]},\\\"string_escaped_char\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\(\\\\\\\\\\\\\\\\|[0-3]\\\\\\\\d{,2}|[4-7]\\\\\\\\d?|x[a-fA-F0-9]{,2}|u[a-fA-F0-9]{,4}|U[a-fA-F0-9]{,8}|.)\\\",\\\"name\\\":\\\"constant.character.escape.julia\\\"}]},\\\"symbol\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![[:word:]⁺-ₜ!′∇\\\\\\\\)\\\\\\\\]\\\\\\\\}]):(?:(?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)(?!(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿]))(?![\\\\\\\"`])\\\",\\\"name\\\":\\\"constant.other.symbol.julia\\\"}]},\\\"type_decl\\\":{\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.name.type.julia\\\"},\\\"2\\\":{\\\"name\\\":\\\"entity.other.inherited-class.julia\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.separator.inheritance.julia\\\"}},\\\"match\\\":\\\"(?>!:_)(?:struct|mutable\\\\\\\\s+struct|abstract\\\\\\\\s+type|primitive\\\\\\\\s+type)\\\\\\\\s+((?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*)(\\\\\\\\s*(<:)\\\\\\\\s*(?:[[:alpha:]_\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{So}←-⇿])(?:[[:word:]_!\\\\\\\\p{Lu}\\\\\\\\p{Ll}\\\\\\\\p{Lt}\\\\\\\\p{Lm}\\\\\\\\p{Lo}\\\\\\\\p{Nl}\\\\\\\\p{Sc}⅀-⅄∿⊾⊿⊤⊥∂∅-∇∎∏∐∑∞∟∫-∳⋀-⋃◸-◿♯⟘⟙⟀⟁⦰-⦴⨀-⨆⨉-⨖⨛⨜𝛁𝛛𝛻𝜕𝜵𝝏𝝯𝞉𝞩𝟃ⁱ-⁾₁-₎∠-∢⦛-⦯℘℮゛-゜𝟎-𝟡]|[^\\\\\\\\P{Mn}\\\\u0001-¡]|[^\\\\\\\\P{Mc}\\\\u0001-¡]|[^\\\\\\\\P{Nd}\\\\u0001-¡]|[^\\\\\\\\P{Pc}\\\\u0001-¡]|[^\\\\\\\\P{Sk}\\\\u0001-¡]|[^\\\\\\\\P{Me}\\\\u0001-¡]|[^\\\\\\\\P{No}\\\\u0001-¡]|[′-‷⁗]|[^\\\\\\\\P{So}←-⇿])*(?:{.*})?)?\\\",\\\"name\\\":\\\"meta.type.julia\\\"}]}},\\\"scopeName\\\":\\\"source.julia\\\",\\\"embeddedLangs\\\":[\\\"cpp\\\",\\\"python\\\",\\\"javascript\\\",\\\"r\\\",\\\"sql\\\"],\\\"aliases\\\":[\\\"jl\\\"]}\"))\n\nexport default [\n...cpp,\n...python,\n...javascript,\n...r,\n...sql,\nlang\n]\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;OACZ,qJAAA,CAAA,UAAG;OACH,wJAAA,CAAA,UAAM;OACN,4JAAA,CAAA,UAAU;OACV,mJAAA,CAAA,UAAC;OACD,qJAAA,CAAA,UAAG;IACN;CACC", "ignoreList": [0]}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}